import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.util.*;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * PPMT助手高级脚本反编译器
 * 专门用于还原加密的JavaScript源码
 */
public class AdvancedScriptDecompiler {
    
    private byte[] mKey;
    private String mInitVector;
    private byte[] keyStream;
    
    public AdvancedScriptDecompiler() throws Exception {
        // 项目配置参数 (从project.json提取)
        String packageName = "com.ppmt.helper";
        String versionName = "1.0.0";
        String mainScriptFile = "main.js";
        String versionCode = "1";
        String buildId = "20231201";
        String appName = "PPMT助手";
        
        // 生成解密密钥
        String keyString = packageName + versionName + mainScriptFile + versionCode;
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        this.mKey = md5.digest(keyString.getBytes(StandardCharsets.UTF_8));
        
        // 生成初始化向量
        String ivString = buildId + appName;
        String ivMd5 = bytesToHex(md5.digest(ivString.getBytes(StandardCharsets.UTF_8)));
        this.mInitVector = ivMd5.substring(0, 16);
        
        // 生成XOR密钥流
        this.keyStream = "9a1132118990c3db".getBytes(StandardCharsets.UTF_8);
        
        System.out.println("解密密钥: " + bytesToHex(mKey));
        System.out.println("初始化向量: " + mInitVector);
        System.out.println("XOR密钥流: " + new String(keyStream));
    }
    
    /**
     * 高级源码还原方法
     */
    public void decompileScript(String filename) throws Exception {
        System.out.println("\n🔍 开始高级反编译: " + filename);
        System.out.println("=" + "=".repeat(60));
        
        byte[] fileData = Files.readAllBytes(Paths.get(filename));
        
        // 1. 验证文件格式
        if (!validateEncryptedFile(fileData)) {
            System.out.println("❌ 不是有效的加密脚本文件");
            return;
        }
        
        // 2. 解密文件
        byte[] decryptedData = performDecryption(fileData);
        
        // 3. 尝试多种还原方法
        boolean success = false;
        
        // 方法1: 直接文本还原
        if (attemptTextRestore(decryptedData, filename)) {
            success = true;
        }
        
        // 方法2: Rhino对象反序列化还原
        if (attemptRhinoDecompilation(decryptedData, filename)) {
            success = true;
        }
        
        // 方法3: 字节码分析还原
        if (attemptBytecodeAnalysis(decryptedData, filename)) {
            success = true;
        }
        
        if (!success) {
            System.out.println("⚠️ 所有还原方法都失败了，保存原始解密数据");
            saveRawData(decryptedData, filename);
        }
    }
    
    private boolean validateEncryptedFile(byte[] fileData) {
        if (fileData.length < 8) return false;
        
        // 检查PPMT加密标识
        byte[] expectedHeader = {0x50, 0x50, 0x4D, 0x54, 0x01}; // "PPMT" + version
        for (int i = 0; i < 5; i++) {
            if (fileData[i] != expectedHeader[i]) {
                return false;
            }
        }
        
        byte encryptionType = fileData[5];
        System.out.println("📋 加密类型: " + encryptionType + " (" + 
            (encryptionType == 18 ? "文本加密" : encryptionType == 19 ? "对象加密" : "未知") + ")");
        
        return encryptionType == 18 || encryptionType == 19;
    }
    
    private byte[] performDecryption(byte[] fileData) throws Exception {
        // 跳过8字节文件头
        byte[] encryptedContent = new byte[fileData.length - 8];
        System.arraycopy(fileData, 8, encryptedContent, 0, encryptedContent.length);
        
        // AES解密
        SecretKeySpec secretKey = new SecretKeySpec(mKey, "AES");
        IvParameterSpec iv = new IvParameterSpec(mInitVector.getBytes(StandardCharsets.UTF_8));
        
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);
        byte[] aesDecrypted = cipher.doFinal(encryptedContent);
        
        // XOR解密
        byte[] finalDecrypted = new byte[aesDecrypted.length];
        for (int i = 0; i < aesDecrypted.length; i++) {
            finalDecrypted[i] = (byte) ((aesDecrypted[i] - keyStream[i % keyStream.length]) & 0xFF);
        }
        
        System.out.println("✅ 解密完成，数据长度: " + finalDecrypted.length);
        return finalDecrypted;
    }
    
    // 方法1: 尝试直接文本还原
    private boolean attemptTextRestore(byte[] data, String filename) {
        try {
            String text = new String(data, StandardCharsets.UTF_8);
            
            // 检查是否包含JavaScript关键字
            if (text.contains("function") || text.contains("var") || text.contains("console") || 
                text.contains("window") || text.contains("document")) {
                
                System.out.println("✅ 方法1成功: 发现JavaScript源码");
                
                String outputFile = filename.replace(".js", "_restored_text.js");
                Files.write(Paths.get(outputFile), text.getBytes(StandardCharsets.UTF_8));
                
                System.out.println("📄 源码已保存到: " + outputFile);
                System.out.println("📝 源码预览 (前500字符):");
                System.out.println(text.substring(0, Math.min(500, text.length())));
                
                return true;
            }
        } catch (Exception e) {
            System.out.println("❌ 方法1失败: " + e.getMessage());
        }
        return false;
    }
    
    // 方法2: Rhino对象反序列化还原
    private boolean attemptRhinoDecompilation(byte[] data, String filename) {
        try {
            System.out.println("🔧 尝试Rhino对象反序列化...");
            
            ObjectInputStream ois = new ObjectInputStream(new ByteArrayInputStream(data));
            Object obj = ois.readObject();
            ois.close();
            
            System.out.println("📦 反序列化对象类型: " + obj.getClass().getName());
            
            if (obj.getClass().getName().contains("InterpretedFunction")) {
                return decompileInterpretedFunction(obj, filename);
            } else if (obj.getClass().getName().contains("Script")) {
                return decompileScript(obj, filename);
            }
            
        } catch (Exception e) {
            System.out.println("❌ 方法2失败: " + e.getMessage());
        }
        return false;
    }
    
    private boolean decompileInterpretedFunction(Object func, String filename) throws Exception {
        System.out.println("🎯 处理InterpretedFunction对象...");
        
        // 尝试获取InterpreterData
        Field idataField = func.getClass().getDeclaredField("idata");
        idataField.setAccessible(true);
        Object idata = idataField.get(func);
        
        if (idata == null) {
            System.out.println("❌ 无法获取InterpreterData");
            return false;
        }
        
        boolean success = false;
        
        // 提取字符串表
        try {
            Field stringTableField = idata.getClass().getDeclaredField("itsStringTable");
            stringTableField.setAccessible(true);
            String[] stringTable = (String[]) stringTableField.get(idata);
            
            if (stringTable != null && stringTable.length > 0) {
                String stringFile = filename.replace(".js", "_strings.txt");
                try (PrintWriter writer = new PrintWriter(stringFile, StandardCharsets.UTF_8)) {
                    writer.println("=== 字符串表 (" + stringTable.length + " 项) ===");
                    for (int i = 0; i < stringTable.length; i++) {
                        writer.println(String.format("[%3d] %s", i, stringTable[i]));
                    }
                }
                System.out.println("📝 字符串表已保存到: " + stringFile);
                success = true;
            }
        } catch (Exception e) {
            System.out.println("⚠️ 提取字符串表失败: " + e.getMessage());
        }
        
        // 提取字节码
        try {
            Field icodeField = idata.getClass().getDeclaredField("itsICode");
            icodeField.setAccessible(true);
            byte[] icode = (byte[]) icodeField.get(idata);
            
            if (icode != null && icode.length > 0) {
                String bytecodeFile = filename.replace(".js", "_bytecode.bin");
                Files.write(Paths.get(bytecodeFile), icode);
                System.out.println("🔢 字节码已保存到: " + bytecodeFile);
                
                // 反汇编字节码
                disassembleBytecode(icode, filename);
                success = true;
            }
        } catch (Exception e) {
            System.out.println("⚠️ 提取字节码失败: " + e.getMessage());
        }
        
        return success;
    }
    
    private boolean decompileScript(Object script, String filename) throws Exception {
        System.out.println("🎯 处理Script对象...");
        
        // 尝试调用toString方法获取源码
        try {
            String source = script.toString();
            if (source != null && source.length() > 10) {
                String outputFile = filename.replace(".js", "_script_source.js");
                Files.write(Paths.get(outputFile), source.getBytes(StandardCharsets.UTF_8));
                System.out.println("📄 Script源码已保存到: " + outputFile);
                return true;
            }
        } catch (Exception e) {
            System.out.println("⚠️ 获取Script源码失败: " + e.getMessage());
        }
        
        return false;
    }
    
    // 方法3: 字节码分析还原
    private boolean attemptBytecodeAnalysis(byte[] data, String filename) {
        try {
            System.out.println("🔍 尝试字节码模式识别...");
            
            // 查找可能的JavaScript模式
            String dataStr = new String(data, StandardCharsets.UTF_8);
            List<String> jsPatterns = findJavaScriptPatterns(dataStr);
            
            if (!jsPatterns.isEmpty()) {
                String patternFile = filename.replace(".js", "_patterns.txt");
                try (PrintWriter writer = new PrintWriter(patternFile, StandardCharsets.UTF_8)) {
                    writer.println("=== 发现的JavaScript模式 ===");
                    for (String pattern : jsPatterns) {
                        writer.println(pattern);
                    }
                }
                System.out.println("🔍 JavaScript模式已保存到: " + patternFile);
                return true;
            }
            
        } catch (Exception e) {
            System.out.println("❌ 方法3失败: " + e.getMessage());
        }
        return false;
    }
    
    private List<String> findJavaScriptPatterns(String data) {
        List<String> patterns = new ArrayList<>();
        
        // JavaScript关键字模式
        String[] keywords = {"function", "var", "let", "const", "if", "else", "for", "while", 
                           "return", "console", "window", "document", "setTimeout", "setInterval"};
        
        for (String keyword : keywords) {
            if (data.contains(keyword)) {
                patterns.add("发现关键字: " + keyword);
            }
        }
        
        // 函数调用模式
        if (data.matches(".*\\w+\\s*\\([^)]*\\).*")) {
            patterns.add("发现函数调用模式");
        }
        
        // 对象属性访问模式
        if (data.matches(".*\\w+\\.\\w+.*")) {
            patterns.add("发现对象属性访问模式");
        }
        
        return patterns;
    }
    
    private void disassembleBytecode(byte[] bytecode, String filename) {
        // Rhino字节码操作码映射
        Map<Integer, String> opcodes = createOpcodeMap();
        
        String disasmFile = filename.replace(".js", "_disassembly.txt");
        try (PrintWriter writer = new PrintWriter(disasmFile, StandardCharsets.UTF_8)) {
            writer.println("=== Rhino字节码反汇编 ===");
            writer.println("文件: " + filename);
            writer.println("字节码长度: " + bytecode.length + " 字节");
            writer.println();
            
            for (int i = 0; i < bytecode.length; i++) {
                int opcode = bytecode[i] & 0xFF;
                String opName = opcodes.getOrDefault(opcode, "UNKNOWN_" + opcode);
                writer.printf("%04d: %02X %-15s", i, opcode, opName);
                
                // 添加操作数信息
                if (i + 1 < bytecode.length) {
                    int operand = bytecode[i + 1] & 0xFF;
                    writer.printf(" [%02X]", operand);
                }
                writer.println();
            }
        } catch (Exception e) {
            System.err.println("保存反汇编失败: " + e.getMessage());
        }
        
        System.out.println("🔧 字节码反汇编已保存到: " + disasmFile);
    }
    
    private Map<Integer, String> createOpcodeMap() {
        Map<Integer, String> opcodes = new HashMap<>();
        opcodes.put(0, "NOP");
        opcodes.put(1, "UNDEFINED");
        opcodes.put(2, "NULL");
        opcodes.put(3, "TRUE");
        opcodes.put(4, "FALSE");
        opcodes.put(5, "IFEQ");
        opcodes.put(6, "IFNE");
        opcodes.put(7, "GOTO");
        opcodes.put(8, "GOSUB");
        opcodes.put(9, "RET");
        opcodes.put(10, "POP");
        opcodes.put(11, "DUP");
        opcodes.put(12, "NEW");
        opcodes.put(13, "CALL");
        opcodes.put(14, "RETURN");
        opcodes.put(15, "GETPROP");
        opcodes.put(16, "SETPROP");
        opcodes.put(17, "GETELEM");
        opcodes.put(18, "SETELEM");
        opcodes.put(19, "GETVAR");
        opcodes.put(20, "SETVAR");
        opcodes.put(21, "GETNAME");
        opcodes.put(22, "SETNAME");
        opcodes.put(23, "ADD");
        opcodes.put(24, "SUB");
        opcodes.put(25, "MUL");
        opcodes.put(26, "DIV");
        opcodes.put(27, "MOD");
        return opcodes;
    }
    
    private void saveRawData(byte[] data, String filename) {
        try {
            String rawFile = filename.replace(".js", "_raw_decrypted.bin");
            Files.write(Paths.get(rawFile), data);
            System.out.println("💾 原始解密数据已保存到: " + rawFile);
        } catch (Exception e) {
            System.err.println("保存原始数据失败: " + e.getMessage());
        }
    }
    
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    public static void main(String[] args) {
        try {
            AdvancedScriptDecompiler decompiler = new AdvancedScriptDecompiler();
            
            // 要反编译的文件列表
            String[] files = {
                "resources/assets/project/main.js",
                "resources/assets/project/FloatButton/init.js",
                "resources/assets/project/FloatButton/js/CreateRoundButtonView.js",
                "resources/assets/project/FloatButton/js/FloatButtonAnim.js",
                "resources/assets/project/FloatButton/js/__util__.js",
                "resources/assets/project/FloatButton/widget/RoundButton.js"
            };
            
            System.out.println("🚀 PPMT助手高级脚本反编译器启动");
            System.out.println("=" + "=".repeat(60));
            
            for (String file : files) {
                try {
                    decompiler.decompileScript(file);
                } catch (Exception e) {
                    System.err.println("❌ 反编译失败 " + file + ": " + e.getMessage());
                    e.printStackTrace();
                }
            }
            
            System.out.println("\n🎉 反编译完成！");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

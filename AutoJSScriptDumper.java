package com.example.autojsdumper;

import android.util.Log;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Method;
import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedBridge;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

/**
 * AutoJS脚本运行时转储模块
 * 用于在运行时获取解密后的JavaScript源码
 */
public class AutoJSScriptDumper implements IXposedHookLoadPackage {
    
    private static final String TAG = "AutoJSScriptDumper";
    private static final String TARGET_PACKAGE = "com.ppmtzs.scr"; // PPMT助手包名
    private static final String DUMP_DIR = "/sdcard/autojs_dumps/";
    
    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpparam) throws Throwable {
        if (!lpparam.packageName.equals(TARGET_PACKAGE)) {
            return;
        }
        
        Log.d(TAG, "开始Hook AutoJS应用: " + lpparam.packageName);
        
        // 创建转储目录
        createDumpDirectory();
        
        // Hook脚本解密相关方法
        hookScriptEncryption(lpparam);
        
        // Hook Rhino脚本执行
        hookRhinoExecution(lpparam);
        
        // Hook脚本源码获取
        hookScriptSource(lpparam);
        
        Log.d(TAG, "AutoJS Hook设置完成");
    }
    
    /**
     * Hook脚本加密解密相关方法
     */
    private void hookScriptEncryption(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook ScriptEncryption.decrypt方法
            Class<?> scriptEncryptionClass = XposedHelpers.findClass(
                "com.stardust.autojs.engine.encryption.ScriptEncryption", 
                lpparam.classLoader
            );
            
            XposedHelpers.findAndHookMethod(scriptEncryptionClass, "decrypt", 
                byte[].class, String.class, new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    byte[] result = (byte[]) param.getResult();
                    String key = (String) param.args[1];
                    
                    if (result != null && result.length > 0) {
                        Log.d(TAG, "ScriptEncryption.decrypt调用成功");
                        Log.d(TAG, "密钥: " + key);
                        Log.d(TAG, "解密数据长度: " + result.length);
                        
                        try {
                            String decryptedContent = new String(result, "UTF-8");
                            if (isValidJavaScript(decryptedContent)) {
                                String filename = "decrypted_" + System.currentTimeMillis() + ".js";
                                saveToFile(filename, decryptedContent);
                                Log.d(TAG, "解密后的JavaScript已保存: " + filename);
                            } else {
                                // 如果不是有效的JavaScript，保存为二进制文件
                                String filename = "decrypted_binary_" + System.currentTimeMillis() + ".bin";
                                saveBinaryToFile(filename, result);
                                Log.d(TAG, "解密后的二进制数据已保存: " + filename);
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "保存解密内容失败", e);
                        }
                    }
                }
            });
            
            Log.d(TAG, "Hook ScriptEncryption.decrypt成功");
            
        } catch (Exception e) {
            Log.e(TAG, "Hook ScriptEncryption失败", e);
        }
    }
    
    /**
     * Hook Rhino脚本执行相关方法
     */
    private void hookRhinoExecution(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook Context.compileString方法
            Class<?> contextClass = XposedHelpers.findClass(
                "org.mozilla.javascript.Context", 
                lpparam.classLoader
            );
            
            XposedHelpers.findAndHookMethod(contextClass, "compileString",
                String.class, String.class, int.class, Object.class,
                new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    String source = (String) param.args[0];
                    String sourceName = (String) param.args[1];
                    int lineNumber = (Integer) param.args[2];
                    
                    if (source != null && source.length() > 10) {
                        Log.d(TAG, "Context.compileString调用");
                        Log.d(TAG, "源文件名: " + sourceName);
                        Log.d(TAG, "行号: " + lineNumber);
                        Log.d(TAG, "源码长度: " + source.length());
                        
                        String filename = "compiled_" + sanitizeFilename(sourceName) + 
                                        "_" + System.currentTimeMillis() + ".js";
                        saveToFile(filename, source);
                        Log.d(TAG, "编译的JavaScript源码已保存: " + filename);
                    }
                }
            });
            
            Log.d(TAG, "Hook Context.compileString成功");
            
        } catch (Exception e) {
            Log.e(TAG, "Hook Rhino Context失败", e);
        }
        
        try {
            // Hook InterpretedFunction
            Class<?> interpretedFunctionClass = XposedHelpers.findClass(
                "org.mozilla.javascript.InterpretedFunction", 
                lpparam.classLoader
            );
            
            XposedHelpers.findAndHookMethod(interpretedFunctionClass, "call",
                "org.mozilla.javascript.Context",
                "org.mozilla.javascript.Scriptable", 
                "org.mozilla.javascript.Scriptable",
                Object[].class,
                new XC_MethodHook() {
                @Override
                protected void beforeHookedMethod(MethodHookParam param) throws Throwable {
                    try {
                        Object function = param.thisObject;
                        Method getFunctionNameMethod = function.getClass().getMethod("getFunctionName");
                        String functionName = (String) getFunctionNameMethod.invoke(function);
                        
                        if (functionName != null && !functionName.isEmpty()) {
                            Log.d(TAG, "执行函数: " + functionName);
                            
                            // 尝试获取函数源码
                            try {
                                Method getEncodedSourceMethod = function.getClass().getMethod("getEncodedSource");
                                String source = (String) getEncodedSourceMethod.invoke(function);
                                if (source != null && source.length() > 0) {
                                    String filename = "function_" + sanitizeFilename(functionName) + 
                                                    "_" + System.currentTimeMillis() + ".js";
                                    saveToFile(filename, source);
                                    Log.d(TAG, "函数源码已保存: " + filename);
                                }
                            } catch (Exception e) {
                                // 忽略获取源码失败的情况
                            }
                        }
                    } catch (Exception e) {
                        // 忽略获取函数信息失败的情况
                    }
                }
            });
            
            Log.d(TAG, "Hook InterpretedFunction成功");
            
        } catch (Exception e) {
            Log.e(TAG, "Hook InterpretedFunction失败", e);
        }
    }
    
    /**
     * Hook脚本源码获取相关方法
     */
    private void hookScriptSource(XC_LoadPackage.LoadPackageParam lpparam) {
        try {
            // Hook ScriptSource.getScript方法
            Class<?> scriptSourceClass = XposedHelpers.findClass(
                "com.stardust.autojs.script.ScriptSource", 
                lpparam.classLoader
            );
            
            XposedHelpers.findAndHookMethod(scriptSourceClass, "getScript",
                new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    String result = (String) param.getResult();
                    if (result != null && result.length() > 10) {
                        Log.d(TAG, "ScriptSource.getScript返回脚本内容");
                        String filename = "script_source_" + System.currentTimeMillis() + ".js";
                        saveToFile(filename, result);
                        Log.d(TAG, "脚本源码已保存: " + filename);
                    }
                }
            });
            
            Log.d(TAG, "Hook ScriptSource.getScript成功");
            
        } catch (Exception e) {
            Log.e(TAG, "Hook ScriptSource失败", e);
        }
        
        try {
            // Hook EncryptedScriptFileSource.getScript方法
            Class<?> encryptedScriptFileSourceClass = XposedHelpers.findClass(
                "com.stardust.autojs.script.EncryptedScriptFileSource", 
                lpparam.classLoader
            );
            
            XposedHelpers.findAndHookMethod(encryptedScriptFileSourceClass, "getScript",
                new XC_MethodHook() {
                @Override
                protected void afterHookedMethod(MethodHookParam param) throws Throwable {
                    String result = (String) param.getResult();
                    if (result != null && result.length() > 10) {
                        Log.d(TAG, "EncryptedScriptFileSource.getScript返回解密内容");
                        
                        // 尝试获取文件路径
                        String path = "unknown";
                        try {
                            Method getPathMethod = param.thisObject.getClass().getMethod("getPath");
                            path = (String) getPathMethod.invoke(param.thisObject);
                        } catch (Exception e) {
                            // 忽略获取路径失败
                        }
                        
                        String filename = "encrypted_" + sanitizeFilename(path) + 
                                        "_" + System.currentTimeMillis() + ".js";
                        saveToFile(filename, result);
                        Log.d(TAG, "加密脚本解密后内容已保存: " + filename);
                    }
                }
            });
            
            Log.d(TAG, "Hook EncryptedScriptFileSource.getScript成功");
            
        } catch (Exception e) {
            Log.e(TAG, "Hook EncryptedScriptFileSource失败", e);
        }
    }
    
    /**
     * 创建转储目录
     */
    private void createDumpDirectory() {
        try {
            File dir = new File(DUMP_DIR);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            Log.d(TAG, "转储目录已创建: " + DUMP_DIR);
        } catch (Exception e) {
            Log.e(TAG, "创建转储目录失败", e);
        }
    }
    
    /**
     * 保存文本内容到文件
     */
    private void saveToFile(String filename, String content) {
        try {
            File file = new File(DUMP_DIR + filename);
            FileWriter writer = new FileWriter(file);
            writer.write(content);
            writer.close();
            Log.d(TAG, "文件已保存: " + file.getAbsolutePath());
        } catch (IOException e) {
            Log.e(TAG, "保存文件失败: " + filename, e);
        }
    }
    
    /**
     * 保存二进制内容到文件
     */
    private void saveBinaryToFile(String filename, byte[] data) {
        try {
            File file = new File(DUMP_DIR + filename);
            java.io.FileOutputStream fos = new java.io.FileOutputStream(file);
            fos.write(data);
            fos.close();
            Log.d(TAG, "二进制文件已保存: " + file.getAbsolutePath());
        } catch (IOException e) {
            Log.e(TAG, "保存二进制文件失败: " + filename, e);
        }
    }
    
    /**
     * 清理文件名中的非法字符
     */
    private String sanitizeFilename(String filename) {
        if (filename == null) return "unknown";
        return filename.replaceAll("[/\\\\:*?\"<>|]", "_");
    }
    
    /**
     * 判断是否是有效的JavaScript代码
     */
    private boolean isValidJavaScript(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }
        
        // 简单的JavaScript代码特征检测
        String[] jsKeywords = {
            "function", "var", "let", "const", "if", "else", "for", "while",
            "return", "console", "window", "document", "setTimeout", "true", "false"
        };
        
        String lowerContent = content.toLowerCase();
        for (String keyword : jsKeywords) {
            if (lowerContent.contains(keyword)) {
                return true;
            }
        }
        
        // 检查是否包含JavaScript语法特征
        return content.contains("(") && content.contains(")") ||
               content.contains("{") && content.contains("}") ||
               content.contains("=") && content.contains(";");
    }
}

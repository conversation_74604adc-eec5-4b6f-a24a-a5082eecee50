import java.io.*;
import java.nio.file.*;
import java.nio.charset.StandardCharsets;
import java.lang.reflect.*;

public class BytecodeAnalyzer {
    
    public static void main(String[] args) throws Exception {
        // 处理所有解密后的文件
        String[] files = {
            "resources/assets/project/main.js",
            "resources/assets/project/FloatButton/init.js",
            "resources/assets/project/FloatButton/js/CreateRoundButtonView.js",
            "resources/assets/project/FloatButton/js/FloatButtonAnim.js",
            "resources/assets/project/FloatButton/js/__util__.js",
            "resources/assets/project/FloatButton/widget/RoundButton.js"
        };

        ScriptDeserializer deserializer = new ScriptDeserializer();
        deserializer.initializeKeys();

        for (String filePath : files) {
            System.out.println("\n=== 分析文件: " + filePath + " ===");

            try {
                // 读取并解密文件
                byte[] fileData = Files.readAllBytes(Paths.get(filePath));

                // 跳过8字节头部
                byte[] encryptedContent = new byte[fileData.length - 8];
                System.arraycopy(fileData, 8, encryptedContent, 0, encryptedContent.length);

                // 解密
                byte[] decryptedData = deserializer.performDecryption(encryptedContent);
                System.out.println("解密完成，数据长度: " + decryptedData.length);

                // 反序列化JavaScript对象
                try (ByteArrayInputStream bais = new ByteArrayInputStream(decryptedData);
                     ObjectInputStream ois = new ObjectInputStream(bais)) {

                    Object obj = ois.readObject();
                    System.out.println("反序列化对象类型: " + obj.getClass().getName());

                    // 使用unsafe方法来绕过访问限制
                    try {
                        String fileName = Paths.get(filePath).getFileName().toString().replace(".js", "");
                        analyzeWithUnsafe(obj, fileName);
                    } catch (Exception e) {
                        System.err.println("Unsafe分析失败: " + e.getMessage());

                        // 尝试直接分析字节码
                        analyzeDirectly(obj);
                    }
                }
            } catch (Exception e) {
                System.err.println("处理文件失败: " + e.getMessage());
            }
        }
    }
    
    private static void analyzeWithUnsafe(Object obj, String fileName) throws Exception {
        // 尝试使用sun.misc.Unsafe来绕过访问限制
        try {
            Class<?> unsafeClass = Class.forName("sun.misc.Unsafe");
            Field theUnsafeField = unsafeClass.getDeclaredField("theUnsafe");
            theUnsafeField.setAccessible(true);
            Object unsafe = theUnsafeField.get(null);
            
            // 获取InterpretedFunction的idata字段
            Field idataField = obj.getClass().getDeclaredField("idata");
            long idataOffset = (Long) unsafeClass.getMethod("objectFieldOffset", Field.class).invoke(unsafe, idataField);
            Object idata = unsafeClass.getMethod("getObject", Object.class, long.class).invoke(unsafe, obj, idataOffset);
            
            if (idata != null) {
                System.out.println("成功获取InterpreterData对象");
                
                // 尝试获取字符串表
                Field stringTableField = idata.getClass().getDeclaredField("itsStringTable");
                long stringTableOffset = (Long) unsafeClass.getMethod("objectFieldOffset", Field.class).invoke(unsafe, stringTableField);
                String[] stringTable = (String[]) unsafeClass.getMethod("getObject", Object.class, long.class).invoke(unsafe, idata, stringTableOffset);
                
                if (stringTable != null) {
                    System.out.println("字符串表长度: " + stringTable.length);
                    StringBuilder sourceBuilder = new StringBuilder();
                    
                    for (int i = 0; i < stringTable.length; i++) {
                        if (stringTable[i] != null) {
                            System.out.println("字符串[" + i + "]: " + stringTable[i]);
                            sourceBuilder.append(stringTable[i]).append("\n");
                        }
                    }
                    
                    String reconstructedSource = sourceBuilder.toString();
                    if (reconstructedSource.length() > 50) {
                        String outputFile = "unsafe_extracted_" + fileName + ".js";
                        Files.write(Paths.get(outputFile), reconstructedSource.getBytes(StandardCharsets.UTF_8));
                        System.out.println("已保存重构源代码到: " + outputFile);
                    }
                }
                
                // 尝试获取字节码
                Field icodeField = idata.getClass().getDeclaredField("itsICode");
                long icodeOffset = (Long) unsafeClass.getMethod("objectFieldOffset", Field.class).invoke(unsafe, icodeField);
                byte[] icode = (byte[]) unsafeClass.getMethod("getObject", Object.class, long.class).invoke(unsafe, idata, icodeOffset);
                
                if (icode != null) {
                    System.out.println("字节码长度: " + icode.length);
                    analyzeICode(icode, fileName);
                }
            }
            
        } catch (ClassNotFoundException e) {
            System.err.println("sun.misc.Unsafe不可用");
            throw e;
        }
    }
    
    private static void analyzeDirectly(Object obj) {
        System.out.println("尝试直接分析对象...");
        
        // 分析对象的字符串表示
        String objStr = obj.toString();
        System.out.println("对象字符串: " + objStr);
        
        // 尝试获取所有可访问的方法
        Method[] methods = obj.getClass().getMethods();
        for (Method method : methods) {
            if (method.getParameterCount() == 0 && 
                (method.getName().contains("source") || method.getName().contains("Source") ||
                 method.getName().contains("string") || method.getName().contains("String"))) {
                try {
                    Object result = method.invoke(obj);
                    if (result != null) {
                        System.out.println("方法 " + method.getName() + "() 返回: " + result);
                    }
                } catch (Exception e) {
                    // 忽略异常
                }
            }
        }
    }
    
    private static void analyzeICode(byte[] icode, String fileName) {
        System.out.println("分析字节码...");
        
        // 简单的字节码分析 - 查找可能的字符串引用
        for (int i = 0; i < Math.min(100, icode.length); i++) {
            System.out.printf("%02X ", icode[i]);
            if ((i + 1) % 16 == 0) {
                System.out.println();
            }
        }
        System.out.println();
        
        // 保存字节码到文件以供进一步分析
        try {
            String bytecodeFile = "bytecode_" + fileName + ".bin";
            Files.write(Paths.get(bytecodeFile), icode);
            System.out.println("字节码已保存到: " + bytecodeFile);
        } catch (IOException e) {
            System.err.println("保存字节码失败: " + e.getMessage());
        }
    }
}

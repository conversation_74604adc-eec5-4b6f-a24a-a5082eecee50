import java.io.*;
import java.lang.reflect.*;

public class ExploreRhino {
    public static void main(String[] args) throws Exception {
        // 首先解密文件获取二进制数据
        ScriptDeserializer deserializer = new ScriptDeserializer();

        // 读取加密文件并解密
        byte[] encryptedData = java.nio.file.Files.readAllBytes(java.nio.file.Paths.get("resources/assets/project/main.js"));

        // 跳过8字节头部，获取纯加密内容
        byte[] encryptedContent = new byte[encryptedData.length - 8];
        System.arraycopy(encryptedData, 8, encryptedContent, 0, encryptedContent.length);

        // 手动执行解密步骤
        deserializer.initializeKeys();
        byte[] data = deserializer.performDecryption(encryptedContent);
        
        try (ByteArrayInputStream bais = new ByteArrayInputStream(data);
             ObjectInputStream ois = new ObjectInputStream(bais)) {
            
            Object obj = ois.readObject();
            Class<?> clazz = obj.getClass();
            
            System.out.println("类名: " + clazz.getName());
            System.out.println("父类: " + clazz.getSuperclass().getName());
            
            System.out.println("\n所有字段:");
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    Object value = field.get(obj);
                    System.out.println("  " + field.getName() + " (" + field.getType().getSimpleName() + "): " + 
                                     (value != null ? value.getClass().getSimpleName() : "null"));
                    
                    // 如果是InterpreterData类型，进一步探索
                    if (value != null && value.getClass().getSimpleName().equals("InterpreterData")) {
                        System.out.println("    探索InterpreterData:");
                        Field[] idataFields = value.getClass().getDeclaredFields();
                        for (Field idataField : idataFields) {
                            idataField.setAccessible(true);
                            try {
                                Object idataValue = idataField.get(value);
                                System.out.println("      " + idataField.getName() + " (" + idataField.getType().getSimpleName() + "): " + 
                                                 (idataValue != null ? 
                                                  (idataValue instanceof String ? "\"" + ((String)idataValue).substring(0, Math.min(50, ((String)idataValue).length())) + "...\"" : idataValue.getClass().getSimpleName()) 
                                                  : "null"));
                            } catch (Exception e) {
                                System.out.println("      " + idataField.getName() + ": 无法访问 - " + e.getMessage());
                            }
                        }
                    }
                } catch (Exception e) {
                    System.out.println("  " + field.getName() + ": 无法访问 - " + e.getMessage());
                }
            }
            
            System.out.println("\n所有方法:");
            Method[] methods = clazz.getDeclaredMethods();
            for (Method method : methods) {
                if (method.getName().contains("source") || method.getName().contains("Source") || 
                    method.getName().contains("code") || method.getName().contains("Code")) {
                    System.out.println("  " + method.getName() + "()");
                }
            }
        }
    }
}

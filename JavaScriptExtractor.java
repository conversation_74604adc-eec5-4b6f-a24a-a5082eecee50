import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * JavaScript代码提取器
 * 从解密后的AutoJS字节码中提取可读的JavaScript代码片段
 */
public class JavaScriptExtractor {
    
    // JavaScript关键字和常见标识符
    private static final Set<String> JS_KEYWORDS = new HashSet<>(Arrays.asList(
        "function", "var", "let", "const", "if", "else", "for", "while", "do", "switch", "case", "default",
        "return", "break", "continue", "try", "catch", "finally", "throw", "new", "this", "typeof", "instanceof",
        "true", "false", "null", "undefined", "console", "window", "document", "setTimeout", "setInterval",
        "clearTimeout", "clearInterval", "parseInt", "parseFloat", "isNaN", "isFinite", "encodeURI", "decodeURI",
        "Math", "Date", "Array", "Object", "String", "Number", "Boolean", "RegExp", "Error", "JSON",
        "length", "push", "pop", "shift", "unshift", "slice", "splice", "indexOf", "lastIndexOf", "join",
        "toString", "valueOf", "hasOwnProperty", "propertyIsEnumerable", "constructor", "prototype",
        "click", "touch", "swipe", "gesture", "sleep", "toast", "log", "desc", "text", "id", "className",
        "bounds", "clickable", "scrollable", "checkable", "selected", "enabled", "password", "multiLine",
        "findOne", "find", "waitFor", "exists", "click", "longClick", "setText", "getText", "scrollUp", "scrollDown",
        "back", "home", "recents", "powerDialog", "notifications", "quickSettings", "splitScreen",
        "auto", "device", "app", "launch", "launchApp", "currentPackage", "currentActivity", "waitForActivity",
        "shell", "exec", "exit", "keycode", "press", "longPress", "swipe", "gesture", "pinch", "rotate",
        "captureScreen", "findImage", "findColor", "requestScreenCapture", "images", "colors", "point",
        "region", "size", "rect", "canvas", "paint", "path", "matrix", "shader", "typeface", "bitmap",
        "http", "request", "get", "post", "put", "delete", "head", "patch", "options", "newCall", "execute",
        "files", "read", "write", "copy", "move", "remove", "exists", "isFile", "isDir", "listDir", "getName",
        "threads", "start", "currentThread", "interrupt", "join", "sleep", "atomic", "lock", "condition",
        "events", "emit", "on", "once", "removeListener", "removeAllListeners", "setMaxListeners", "broadcast",
        "storages", "create", "remove", "get", "put", "clear", "contains", "keys", "values", "entries"
    ));
    
    // JavaScript操作符和符号
    private static final Set<String> JS_OPERATORS = new HashSet<>(Arrays.asList(
        "==", "!=", "===", "!==", "<=", ">=", "&&", "||", "++", "--", "+=", "-=", "*=", "/=", "%=",
        "<<", ">>", ">>>", "&=", "|=", "^=", "<<=", ">>=", ">>>=", "=>", "...", "?.", "??", "??="
    ));
    
    public static void main(String[] args) {
        try {
            JavaScriptExtractor extractor = new JavaScriptExtractor();
            
            // 要处理的文件列表
            String[] files = {
                "bytecode_main.bin",
                "bytecode_init.bin", 
                "bytecode_CreateRoundButtonView.bin",
                "bytecode_FloatButtonAnim.bin",
                "bytecode___util__.bin",
                "bytecode_RoundButton.bin"
            };
            
            System.out.println("🚀 JavaScript代码提取器");
            System.out.println("=" + "=".repeat(60));
            
            for (String file : files) {
                if (Files.exists(Paths.get(file))) {
                    extractor.extractJavaScript(file);
                } else {
                    System.out.println("⚠️ 文件不存在: " + file);
                }
            }
            
            System.out.println("\n🎉 提取完成！");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 从字节码文件中提取JavaScript代码
     */
    public void extractJavaScript(String filename) {
        System.out.println("\n🔍 提取JavaScript: " + filename);
        System.out.println("=" + "=".repeat(50));
        
        try {
            byte[] data = Files.readAllBytes(Paths.get(filename));
            
            // 提取字符串
            List<String> strings = extractStrings(data);
            
            // 过滤和分析字符串
            List<String> jsStrings = filterJavaScriptStrings(strings);
            
            // 尝试重构代码片段
            List<String> codeFragments = reconstructCodeFragments(jsStrings);
            
            // 保存结果
            saveResults(filename, jsStrings, codeFragments);
            
        } catch (Exception e) {
            System.err.println("❌ 提取失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 从字节数据中提取所有可能的字符串
     */
    private List<String> extractStrings(byte[] data) {
        List<String> strings = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        
        for (int i = 0; i < data.length; i++) {
            byte b = data[i];
            
            // 可打印ASCII字符 (包括空格)
            if (b >= 32 && b <= 126) {
                current.append((char) b);
            } else if (b == 9 || b == 10 || b == 13) { // Tab, LF, CR
                current.append((char) b);
            } else {
                if (current.length() >= 2) { // 至少2个字符
                    strings.add(current.toString().trim());
                }
                current.setLength(0);
            }
        }
        
        // 处理最后一个字符串
        if (current.length() >= 2) {
            strings.add(current.toString().trim());
        }
        
        return strings;
    }
    
    /**
     * 过滤出可能的JavaScript字符串
     */
    private List<String> filterJavaScriptStrings(List<String> strings) {
        List<String> jsStrings = new ArrayList<>();
        
        for (String str : strings) {
            if (str.isEmpty()) continue;
            
            // 检查是否包含JavaScript关键字
            boolean hasKeyword = false;
            for (String keyword : JS_KEYWORDS) {
                if (str.toLowerCase().contains(keyword.toLowerCase())) {
                    hasKeyword = true;
                    break;
                }
            }
            
            // 检查是否包含JavaScript语法模式
            boolean hasSyntax = str.contains("(") && str.contains(")") ||
                              str.contains("{") && str.contains("}") ||
                              str.contains("[") && str.contains("]") ||
                              str.contains("=") || str.contains(";") ||
                              str.contains(".") && str.matches(".*[a-zA-Z].*") ||
                              str.matches(".*[a-zA-Z_$][a-zA-Z0-9_$]*.*");
            
            // 检查是否是有效的标识符
            boolean isIdentifier = str.matches("[a-zA-Z_$][a-zA-Z0-9_$]*");
            
            // 检查是否包含字符串字面量模式
            boolean hasStringLiteral = str.contains("\"") || str.contains("'") || str.contains("`");
            
            if (hasKeyword || hasSyntax || isIdentifier || hasStringLiteral) {
                jsStrings.add(str);
            }
        }
        
        return jsStrings;
    }
    
    /**
     * 尝试重构代码片段
     */
    private List<String> reconstructCodeFragments(List<String> jsStrings) {
        List<String> fragments = new ArrayList<>();
        
        // 查找函数定义
        for (String str : jsStrings) {
            if (str.contains("function") && (str.contains("(") || str.contains("{"))) {
                fragments.add("// 函数定义: " + str);
            }
        }
        
        // 查找变量声明
        for (String str : jsStrings) {
            if ((str.startsWith("var ") || str.startsWith("let ") || str.startsWith("const ")) 
                && str.contains("=")) {
                fragments.add("// 变量声明: " + str);
            }
        }
        
        // 查找方法调用
        for (String str : jsStrings) {
            if (str.matches(".*[a-zA-Z_$][a-zA-Z0-9_$]*\\s*\\(.*\\).*")) {
                fragments.add("// 方法调用: " + str);
            }
        }
        
        // 查找对象属性访问
        for (String str : jsStrings) {
            if (str.matches(".*[a-zA-Z_$][a-zA-Z0-9_$]*\\.[a-zA-Z_$][a-zA-Z0-9_$]*.*")) {
                fragments.add("// 属性访问: " + str);
            }
        }
        
        // 查找控制流语句
        for (String str : jsStrings) {
            if (str.matches(".*(if|for|while|switch)\\s*\\(.*")) {
                fragments.add("// 控制流: " + str);
            }
        }
        
        return fragments;
    }
    
    /**
     * 保存提取结果
     */
    private void saveResults(String filename, List<String> jsStrings, List<String> codeFragments) {
        String baseName = filename.replace("bytecode_", "").replace(".bin", "");
        String outputFile = "extracted_" + baseName + ".txt";
        
        try (PrintWriter writer = new PrintWriter(outputFile, StandardCharsets.UTF_8)) {
            writer.println("=== JavaScript代码提取结果 ===");
            writer.println("源文件: " + filename);
            writer.println("提取时间: " + new Date());
            writer.println("JavaScript字符串数量: " + jsStrings.size());
            writer.println("代码片段数量: " + codeFragments.size());
            writer.println();
            
            // 输出所有JavaScript相关字符串
            writer.println("=== JavaScript相关字符串 ===");
            for (int i = 0; i < jsStrings.size(); i++) {
                writer.println(String.format("[%3d] %s", i, jsStrings.get(i)));
            }
            writer.println();
            
            // 输出重构的代码片段
            if (!codeFragments.isEmpty()) {
                writer.println("=== 重构的代码片段 ===");
                for (String fragment : codeFragments) {
                    writer.println(fragment);
                }
                writer.println();
            }
            
            // 按类别分组显示
            writer.println("=== 按类别分组 ===");
            
            // 函数和方法
            writer.println("\n--- 函数和方法 ---");
            for (String str : jsStrings) {
                if (str.contains("function") || str.matches(".*[a-zA-Z_$][a-zA-Z0-9_$]*\\s*\\(.*")) {
                    writer.println(str);
                }
            }
            
            // 变量和属性
            writer.println("\n--- 变量和属性 ---");
            for (String str : jsStrings) {
                if (str.matches("[a-zA-Z_$][a-zA-Z0-9_$]*") || 
                    str.matches(".*[a-zA-Z_$][a-zA-Z0-9_$]*\\.[a-zA-Z_$][a-zA-Z0-9_$]*.*")) {
                    writer.println(str);
                }
            }
            
            // 字符串字面量
            writer.println("\n--- 字符串字面量 ---");
            for (String str : jsStrings) {
                if (str.contains("\"") || str.contains("'") || str.contains("`")) {
                    writer.println(str);
                }
            }
            
            // AutoJS特有的API
            writer.println("\n--- AutoJS API ---");
            String[] autojsKeywords = {"click", "toast", "sleep", "findOne", "desc", "text", "id", 
                                     "auto", "device", "app", "shell", "files", "threads", "events"};
            for (String str : jsStrings) {
                for (String keyword : autojsKeywords) {
                    if (str.toLowerCase().contains(keyword.toLowerCase())) {
                        writer.println(str);
                        break;
                    }
                }
            }
            
        } catch (Exception e) {
            System.err.println("保存结果失败: " + e.getMessage());
        }
        
        System.out.println("💾 JavaScript代码已提取到: " + outputFile);
        System.out.println("📊 找到 " + jsStrings.size() + " 个JavaScript相关字符串");
        System.out.println("🔧 重构了 " + codeFragments.size() + " 个代码片段");
    }
}

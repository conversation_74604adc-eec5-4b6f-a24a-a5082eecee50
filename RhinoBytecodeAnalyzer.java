import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.*;

/**
 * Rhino字节码分析器 - 基于52pojie教程
 * 专门用于分析AutoJS的快照加密脚本
 */
public class RhinoBytecodeAnalyzer {
    
    private byte[] mKey;
    private String mInitVector;
    private byte[] keyStream;
    
    // Rhino字节码操作码映射（基于Rhino源码）
    private static final Map<Integer, String> RHINO_OPCODES = new HashMap<>();
    
    static {
        // 基本操作码
        RHINO_OPCODES.put(0, "NOP");
        RHINO_OPCODES.put(1, "UNDEFINED");
        RHINO_OPCODES.put(2, "NULL");
        RHINO_OPCODES.put(3, "TRUE");
        RHINO_OPCODES.put(4, "FALSE");
        RHINO_OPCODES.put(5, "IFEQ");
        RHINO_OPCODES.put(6, "IFNE");
        RHINO_OPCODES.put(7, "GOTO");
        RHINO_OPCODES.put(8, "GOSUB");
        RHINO_OPCODES.put(9, "RET");
        RHINO_OPCODES.put(10, "POP");
        RHINO_OPCODES.put(11, "DUP");
        RHINO_OPCODES.put(12, "NEW");
        RHINO_OPCODES.put(13, "CALL");
        RHINO_OPCODES.put(14, "RETURN");
        RHINO_OPCODES.put(15, "GETPROP");
        RHINO_OPCODES.put(16, "SETPROP");
        RHINO_OPCODES.put(17, "GETELEM");
        RHINO_OPCODES.put(18, "SETELEM");
        RHINO_OPCODES.put(19, "GETVAR");
        RHINO_OPCODES.put(20, "SETVAR");
        RHINO_OPCODES.put(21, "GETNAME");
        RHINO_OPCODES.put(22, "SETNAME");
        RHINO_OPCODES.put(23, "ADD");
        RHINO_OPCODES.put(24, "SUB");
        RHINO_OPCODES.put(25, "MUL");
        RHINO_OPCODES.put(26, "DIV");
        RHINO_OPCODES.put(27, "MOD");
        RHINO_OPCODES.put(28, "BITNOT");
        RHINO_OPCODES.put(29, "BITAND");
        RHINO_OPCODES.put(30, "BITOR");
        RHINO_OPCODES.put(31, "BITXOR");
        RHINO_OPCODES.put(32, "LSH");
        RHINO_OPCODES.put(33, "RSH");
        RHINO_OPCODES.put(34, "URSH");
        RHINO_OPCODES.put(35, "NEG");
        RHINO_OPCODES.put(36, "POS");
        RHINO_OPCODES.put(37, "NOT");
        RHINO_OPCODES.put(38, "EQ");
        RHINO_OPCODES.put(39, "NE");
        RHINO_OPCODES.put(40, "LT");
        RHINO_OPCODES.put(41, "LE");
        RHINO_OPCODES.put(42, "GT");
        RHINO_OPCODES.put(43, "GE");
        RHINO_OPCODES.put(44, "INSTANCEOF");
        RHINO_OPCODES.put(45, "IN");
        RHINO_OPCODES.put(46, "TYPEOF");
        RHINO_OPCODES.put(47, "TYPEOFNAME");
        RHINO_OPCODES.put(48, "STRICT_EQ");
        RHINO_OPCODES.put(49, "STRICT_NE");
        RHINO_OPCODES.put(50, "CLOSURE_EXPR");
        RHINO_OPCODES.put(51, "CLOSURE_STMT");
        RHINO_OPCODES.put(52, "REGEXP");
        RHINO_OPCODES.put(53, "POP_RESULT");
        RHINO_OPCODES.put(54, "DUP2");
        RHINO_OPCODES.put(55, "SWAP");
        RHINO_OPCODES.put(56, "GETTHIS");
        RHINO_OPCODES.put(57, "NEWTEMP");
        RHINO_OPCODES.put(58, "USETEMP");
        RHINO_OPCODES.put(59, "GETBASE");
        RHINO_OPCODES.put(60, "GETREF");
        RHINO_OPCODES.put(61, "SETREF");
        RHINO_OPCODES.put(62, "DELREF");
        RHINO_OPCODES.put(63, "BINDNAME");
        RHINO_OPCODES.put(64, "THROW");
        RHINO_OPCODES.put(65, "JTHROW");
        RHINO_OPCODES.put(66, "LOCALCLEAR");
        RHINO_OPCODES.put(67, "RETHROW");
        RHINO_OPCODES.put(68, "GOSUB_WITH_CATCH");
        RHINO_OPCODES.put(69, "SCOPE");
        RHINO_OPCODES.put(70, "ZERO");
        RHINO_OPCODES.put(71, "ONE");
        RHINO_OPCODES.put(72, "ENTERWITH");
        RHINO_OPCODES.put(73, "LEAVEWITH");
        RHINO_OPCODES.put(74, "CATCH_SCOPE");
        RHINO_OPCODES.put(75, "ENUM_INIT_KEYS");
        RHINO_OPCODES.put(76, "ENUM_INIT_VALUES");
        RHINO_OPCODES.put(77, "ENUM_INIT_ARRAY");
        RHINO_OPCODES.put(78, "ENUM_NEXT");
        RHINO_OPCODES.put(79, "ENUM_ID");
        RHINO_OPCODES.put(80, "THISFN");
        RHINO_OPCODES.put(81, "RETURN_RESULT");
        RHINO_OPCODES.put(82, "ARRAYLIT");
        RHINO_OPCODES.put(83, "OBJECTLIT");
        RHINO_OPCODES.put(84, "GET_REF");
        RHINO_OPCODES.put(85, "SET_REF");
        RHINO_OPCODES.put(86, "DEL_REF");
        RHINO_OPCODES.put(87, "REF_CALL");
        RHINO_OPCODES.put(88, "REF_SPECIAL");
        RHINO_OPCODES.put(89, "YIELD");
        RHINO_OPCODES.put(90, "GENERATOR");
        RHINO_OPCODES.put(91, "GENERATOR_END");
        RHINO_OPCODES.put(92, "DEBUGGER");
        RHINO_OPCODES.put(93, "LINE");
        RHINO_OPCODES.put(94, "IINC");
        RHINO_OPCODES.put(95, "LOAD");
        RHINO_OPCODES.put(96, "STORE");
        RHINO_OPCODES.put(97, "CONST");
        RHINO_OPCODES.put(98, "SETCONST");
        RHINO_OPCODES.put(99, "SETCONSTVAR");
        RHINO_OPCODES.put(100, "BIGINT");
        RHINO_OPCODES.put(101, "LITERAL_NEW");
        RHINO_OPCODES.put(102, "LITERAL_SET");
        RHINO_OPCODES.put(103, "LITERAL_GETTER");
        RHINO_OPCODES.put(104, "LITERAL_SETTER");
        RHINO_OPCODES.put(105, "GETPROP_OPTIONAL");
        RHINO_OPCODES.put(106, "GETELEM_OPTIONAL");
        RHINO_OPCODES.put(107, "CALL_OPTIONAL");
        RHINO_OPCODES.put(108, "DELPROP");
        RHINO_OPCODES.put(109, "TYPEOF_OPTIONAL");
        RHINO_OPCODES.put(110, "NULLISH_COALESCING");
        RHINO_OPCODES.put(111, "TEMPLATE_LITERAL");
        RHINO_OPCODES.put(112, "TEMPLATE_LITERAL_CALLSITE");
    }
    
    public RhinoBytecodeAnalyzer() throws Exception {
        // 项目配置参数 (从project.json获取)
        String packageName = "com.ppmtzs.scr";
        String versionName = "1.00";
        String mainScriptFile = "main.js";
        String versionCode = "100";
        String buildId = "D0899DD7-100";
        String appName = "PPMT助手";
        
        // 固定的加密常量
        byte[] fixedPlaintext = "9a1132118990c3db".getBytes(StandardCharsets.UTF_8);
        
        // 第一步：生成基础密钥哈希
        String keyString = packageName + versionName + mainScriptFile + versionCode;
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        String keyHash = bytesToHex(md5.digest(keyString.getBytes(StandardCharsets.UTF_8)));
        byte[] baseKey = keyHash.getBytes(StandardCharsets.UTF_8);
        
        // 第二步：生成初始向量字符串
        String ivString = buildId + appName;
        String ivHash = bytesToHex(md5.digest(ivString.getBytes(StandardCharsets.UTF_8)));
        this.mInitVector = ivHash.substring(0, 16);
        
        // 第三步：使用基础密钥和IV加密固定字符串生成真正的密钥
        SecretKeySpec secretKey = new SecretKeySpec(baseKey, "AES");
        IvParameterSpec iv = new IvParameterSpec(this.mInitVector.getBytes(StandardCharsets.UTF_8));
        
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);
        this.mKey = cipher.doFinal(fixedPlaintext);
        this.keyStream = this.mKey;
        
        System.out.println("🔑 Rhino字节码分析器初始化完成");
    }
    
    /**
     * 分析AutoJS脚本文件
     */
    public void analyzeScript(String filename) {
        System.out.println("\n🔍 分析脚本: " + filename);
        System.out.println("=" + "=".repeat(60));
        
        try {
            byte[] fileData = Files.readAllBytes(Paths.get(filename));
            
            // 验证文件头
            if (!validateFile(fileData)) {
                return;
            }
            
            // 解密
            byte[] decrypted = decrypt(fileData);
            
            // 分析字节码
            analyzeBytecode(decrypted, filename);
            
        } catch (Exception e) {
            System.err.println("❌ 分析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private boolean validateFile(byte[] fileData) {
        if (fileData.length < 8) {
            System.out.println("❌ 文件太小");
            return false;
        }
        
        // 检查AutoJS加密标识
        byte[] expectedHeader = {119, 1, 23, 127, 18}; // {0x77, 0x01, 0x17, 0x7F, 0x12}
        for (int i = 0; i < 5; i++) {
            if (fileData[i] != expectedHeader[i]) {
                System.out.println("❌ 不是AutoJS加密文件");
                return false;
            }
        }
        
        byte encryptionType = fileData[5];
        System.out.println("📋 加密类型: " + encryptionType + 
            (encryptionType == 19 ? " (快照加密)" : " (其他)"));
        
        return encryptionType == 19; // 只处理快照加密
    }
    
    private byte[] decrypt(byte[] fileData) throws Exception {
        // 跳过8字节文件头
        byte[] encryptedContent = new byte[fileData.length - 8];
        System.arraycopy(fileData, 8, encryptedContent, 0, encryptedContent.length);
        
        // AES解密
        SecretKeySpec secretKey = new SecretKeySpec(mKey, "AES");
        IvParameterSpec iv = new IvParameterSpec(mInitVector.getBytes(StandardCharsets.UTF_8));
        
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);
        byte[] aesDecrypted = cipher.doFinal(encryptedContent);
        
        // XOR解密
        byte[] finalDecrypted = new byte[aesDecrypted.length];
        for (int i = 0; i < aesDecrypted.length; i++) {
            finalDecrypted[i] = (byte) (aesDecrypted[i] ^ keyStream[i % keyStream.length]);
        }
        
        System.out.println("✅ 解密完成，数据长度: " + finalDecrypted.length);
        return finalDecrypted;
    }
    
    /**
     * 分析Rhino字节码
     */
    private void analyzeBytecode(byte[] data, String filename) {
        System.out.println("\n📊 字节码分析:");
        System.out.println("-" + "-".repeat(50));
        
        // 显示文件头信息
        if (data.length >= 4) {
            System.out.printf("📋 数据头: %02X %02X %02X %02X\n", 
                data[0], data[1], data[2], data[3]);
        }
        
        // 尝试解析为Rhino字节码格式
        try {
            parseRhinoBytecode(data, filename);
        } catch (Exception e) {
            System.out.println("⚠️ Rhino字节码解析失败: " + e.getMessage());
        }
        
        // 保存原始数据
        try {
            String outputFile = "bytecode_" + Paths.get(filename).getFileName().toString().replace(".js", ".bin");
            Files.write(Paths.get(outputFile), data);
            System.out.println("💾 字节码已保存到: " + outputFile);
        } catch (Exception e) {
            System.err.println("保存失败: " + e.getMessage());
        }
        
        // 十六进制转储
        System.out.println("\n🔢 十六进制转储 (前200字节):");
        hexDump(data, Math.min(200, data.length));
    }
    
    /**
     * 解析Rhino字节码格式
     */
    private void parseRhinoBytecode(byte[] data, String filename) {
        System.out.println("🎯 尝试解析Rhino字节码格式...");
        
        if (data.length < 4) {
            System.out.println("❌ 数据太短，无法解析");
            return;
        }
        
        // 检查是否是Rhino字节码格式
        // 通常以特定的魔数开始
        int pos = 0;
        
        // 读取可能的字符串表
        List<String> stringTable = new ArrayList<>();
        List<String> functions = new ArrayList<>();
        
        // 尝试查找字符串模式
        findStrings(data, stringTable);
        
        // 尝试查找函数模式
        findFunctions(data, functions);
        
        // 输出结果
        if (!stringTable.isEmpty()) {
            System.out.println("📝 发现的字符串 (" + stringTable.size() + " 个):");
            for (int i = 0; i < Math.min(20, stringTable.size()); i++) {
                System.out.println("  [" + i + "] " + stringTable.get(i));
            }
            if (stringTable.size() > 20) {
                System.out.println("  ... (还有 " + (stringTable.size() - 20) + " 个)");
            }
            
            // 保存字符串表
            saveStringTable(stringTable, filename);
        }
        
        if (!functions.isEmpty()) {
            System.out.println("🔧 发现的函数 (" + functions.size() + " 个):");
            for (String func : functions) {
                System.out.println("  " + func);
            }
        }
        
        // 尝试反汇编字节码
        disassembleBytecode(data, filename);
    }
    
    /**
     * 查找字符串
     */
    private void findStrings(byte[] data, List<String> stringTable) {
        // 查找可打印的ASCII字符串
        StringBuilder current = new StringBuilder();
        
        for (int i = 0; i < data.length; i++) {
            byte b = data[i];
            
            if (b >= 32 && b <= 126) { // 可打印ASCII字符
                current.append((char) b);
            } else {
                if (current.length() >= 3) { // 至少3个字符的字符串
                    String str = current.toString();
                    if (isValidJavaScriptString(str)) {
                        stringTable.add(str);
                    }
                }
                current.setLength(0);
            }
        }
        
        // 处理最后一个字符串
        if (current.length() >= 3) {
            String str = current.toString();
            if (isValidJavaScriptString(str)) {
                stringTable.add(str);
            }
        }
    }
    
    /**
     * 判断是否是有效的JavaScript字符串
     */
    private boolean isValidJavaScriptString(String str) {
        // JavaScript关键字和常见标识符
        String[] jsKeywords = {
            "function", "var", "let", "const", "if", "else", "for", "while", 
            "return", "console", "window", "document", "setTimeout", "setInterval",
            "true", "false", "null", "undefined", "this", "new", "typeof",
            "length", "push", "pop", "slice", "indexOf", "toString", "valueOf"
        };
        
        for (String keyword : jsKeywords) {
            if (str.contains(keyword)) {
                return true;
            }
        }
        
        // 检查是否包含常见的JavaScript模式
        return str.matches(".*[a-zA-Z_$][a-zA-Z0-9_$]*.*") || // 标识符模式
               str.contains("()") || str.contains("{}") || str.contains("[]"); // 语法符号
    }
    
    /**
     * 查找函数
     */
    private void findFunctions(byte[] data, List<String> functions) {
        // 这里可以实现更复杂的函数查找逻辑
        // 暂时简单处理
    }
    
    /**
     * 保存字符串表
     */
    private void saveStringTable(List<String> stringTable, String filename) {
        try {
            String outputFile = "strings_" + Paths.get(filename).getFileName().toString().replace(".js", ".txt");
            try (PrintWriter writer = new PrintWriter(outputFile, StandardCharsets.UTF_8)) {
                writer.println("=== AutoJS脚本字符串表 ===");
                writer.println("文件: " + filename);
                writer.println("字符串数量: " + stringTable.size());
                writer.println();
                
                for (int i = 0; i < stringTable.size(); i++) {
                    writer.println(String.format("[%3d] %s", i, stringTable.get(i)));
                }
            }
            System.out.println("💾 字符串表已保存到: " + outputFile);
        } catch (Exception e) {
            System.err.println("保存字符串表失败: " + e.getMessage());
        }
    }
    
    /**
     * 反汇编字节码
     */
    private void disassembleBytecode(byte[] data, String filename) {
        String outputFile = "disasm_" + Paths.get(filename).getFileName().toString().replace(".js", ".txt");
        
        try (PrintWriter writer = new PrintWriter(outputFile, StandardCharsets.UTF_8)) {
            writer.println("=== Rhino字节码反汇编 ===");
            writer.println("文件: " + filename);
            writer.println("数据长度: " + data.length + " 字节");
            writer.println();
            
            for (int i = 0; i < Math.min(1000, data.length); i++) { // 只反汇编前1000字节
                int opcode = data[i] & 0xFF;
                String opName = RHINO_OPCODES.getOrDefault(opcode, "UNKNOWN_" + opcode);
                
                writer.printf("%04d: %02X %-20s", i, opcode, opName);
                
                // 添加操作数信息
                if (i + 1 < data.length) {
                    int operand = data[i + 1] & 0xFF;
                    writer.printf(" [%02X]", operand);
                }
                
                writer.println();
            }
            
            if (data.length > 1000) {
                writer.println("... (数据太长，只显示前1000字节)");
            }
            
        } catch (Exception e) {
            System.err.println("保存反汇编失败: " + e.getMessage());
        }
        
        System.out.println("🔧 字节码反汇编已保存到: " + outputFile);
    }
    
    /**
     * 十六进制转储
     */
    private void hexDump(byte[] data, int length) {
        for (int i = 0; i < length; i += 16) {
            System.out.printf("%04X: ", i);
            
            // 十六进制部分
            for (int j = 0; j < 16; j++) {
                if (i + j < length) {
                    System.out.printf("%02X ", data[i + j]);
                } else {
                    System.out.print("   ");
                }
            }
            
            System.out.print(" ");
            
            // ASCII部分
            for (int j = 0; j < 16 && i + j < length; j++) {
                byte b = data[i + j];
                if (b >= 32 && b <= 126) {
                    System.out.print((char) b);
                } else {
                    System.out.print(".");
                }
            }
            
            System.out.println();
        }
    }
    
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    public static void main(String[] args) {
        try {
            RhinoBytecodeAnalyzer analyzer = new RhinoBytecodeAnalyzer();
            
            // 要分析的文件列表
            String[] files = {
                "resources/assets/project/main.js",
                "resources/assets/project/FloatButton/init.js",
                "resources/assets/project/FloatButton/js/CreateRoundButtonView.js",
                "resources/assets/project/FloatButton/js/FloatButtonAnim.js",
                "resources/assets/project/FloatButton/js/__util__.js",
                "resources/assets/project/FloatButton/widget/RoundButton.js"
            };
            
            System.out.println("🚀 AutoJS Rhino字节码分析器");
            System.out.println("=" + "=".repeat(60));
            
            for (String file : files) {
                if (Files.exists(Paths.get(file))) {
                    analyzer.analyzeScript(file);
                } else {
                    System.out.println("⚠️ 文件不存在: " + file);
                }
            }
            
            System.out.println("\n🎉 分析完成！");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

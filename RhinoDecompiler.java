import org.mozilla.javascript.*;
import java.io.*;
import java.nio.file.*;
import java.nio.charset.StandardCharsets;
import java.lang.reflect.*;

public class RhinoDecompiler {
    
    public static void main(String[] args) throws Exception {
        System.out.println("=== 尝试深度反编译Rhino脚本 ===");
        
        // 处理main.js
        decompileScript("resources/assets/project/main.js", "main");
        
        // 处理init.js - 这个可能包含按钮点击逻辑
        decompileScript("resources/assets/project/FloatButton/init.js", "init");
    }
    
    private static void decompileScript(String filePath, String scriptName) throws Exception {
        System.out.println("\n=== 反编译 " + scriptName + ".js ===");
        
        ScriptDeserializer deserializer = new ScriptDeserializer();
        deserializer.initializeKeys();
        
        // 读取并解密文件
        byte[] fileData = Files.readAllBytes(Paths.get(filePath));
        byte[] encryptedContent = new byte[fileData.length - 8];
        System.arraycopy(fileData, 8, encryptedContent, 0, encryptedContent.length);
        byte[] decryptedData = deserializer.performDecryption(encryptedContent);
        
        // 反序列化
        try (ByteArrayInputStream bais = new ByteArrayInputStream(decryptedData);
             ObjectInputStream ois = new ObjectInputStream(bais)) {
            
            Object obj = ois.readObject();
            
            if (obj.getClass().getName().equals("org.mozilla.javascript.InterpretedFunction")) {
                // 创建Rhino上下文
                Context cx = Context.enter();
                try {
                    cx.setOptimizationLevel(-1); // 禁用优化以保留更多信息
                    Scriptable scope = cx.initStandardObjects();
                    
                    // 尝试执行函数来观察行为
                    if (obj instanceof Function) {
                        Function func = (Function) obj;
                        
                        System.out.println("函数类型: " + func.getClass().getName());
                        
                        // 尝试获取函数的参数信息
                        try {
                            // 使用反射获取更多信息
                            Field idataField = obj.getClass().getDeclaredField("idata");
                            idataField.setAccessible(true);
                            Object idata = idataField.get(obj);
                            
                            if (idata != null) {
                                analyzeInterpreterData(idata, scriptName);
                            }
                            
                        } catch (Exception e) {
                            System.err.println("反射分析失败: " + e.getMessage());
                        }
                        
                        // 尝试模拟执行环境
                        setupMockEnvironment(scope, scriptName);
                        
                        // 尝试调用函数
                        try {
                            Object result = func.call(cx, scope, scope, new Object[0]);
                            System.out.println("函数执行结果: " + result);
                        } catch (Exception e) {
                            System.out.println("函数执行失败: " + e.getMessage());
                            // 这是预期的，因为缺少依赖
                        }
                    }
                    
                } finally {
                    Context.exit();
                }
            }
        }
    }
    
    private static void analyzeInterpreterData(Object idata, String scriptName) throws Exception {
        System.out.println("分析InterpreterData...");
        
        StringBuilder sourceCode = new StringBuilder();
        sourceCode.append("// ").append(scriptName).append(".js 反编译结果\n\n");
        
        try {
            // 获取字符串表
            Field stringTableField = idata.getClass().getDeclaredField("itsStringTable");
            stringTableField.setAccessible(true);
            String[] stringTable = (String[]) stringTableField.get(idata);
            
            // 获取参数名
            Field argNamesField = idata.getClass().getDeclaredField("argNames");
            argNamesField.setAccessible(true);
            String[] argNames = (String[]) argNamesField.get(idata);
            
            // 获取参数数量
            Field argCountField = idata.getClass().getDeclaredField("argCount");
            argCountField.setAccessible(true);
            int argCount = argCountField.getInt(idata);
            
            // 获取字节码
            Field icodeField = idata.getClass().getDeclaredField("itsICode");
            icodeField.setAccessible(true);
            byte[] icode = (byte[]) icodeField.get(idata);
            
            System.out.println("参数数量: " + argCount);
            if (argNames != null) {
                System.out.println("参数名: " + java.util.Arrays.toString(argNames));
            }
            
            // 基于字符串表和字节码重构代码
            sourceCode.append("function ").append(scriptName).append("(");
            if (argNames != null && argCount > 0) {
                for (int i = 0; i < Math.min(argCount, argNames.length); i++) {
                    if (i > 0) sourceCode.append(", ");
                    sourceCode.append(argNames[i] != null ? argNames[i] : "arg" + i);
                }
            }
            sourceCode.append(") {\n");
            
            // 分析字符串使用模式来推测代码结构
            if (scriptName.equals("init")) {
                reconstructInitScript(stringTable, sourceCode);
            } else if (scriptName.equals("main")) {
                reconstructMainScript(stringTable, sourceCode);
            }
            
            sourceCode.append("}\n\n");
            
            // 添加字节码分析
            sourceCode.append("/*\n字节码分析:\n");
            analyzeICodeInDetail(icode, stringTable, sourceCode);
            sourceCode.append("*/\n");
            
            // 保存重构的代码
            String outputFile = "decompiled_" + scriptName + ".js";
            Files.write(Paths.get(outputFile), sourceCode.toString().getBytes(StandardCharsets.UTF_8));
            System.out.println("反编译代码已保存到: " + outputFile);
            
        } catch (Exception e) {
            System.err.println("分析InterpreterData失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void reconstructInitScript(String[] strings, StringBuilder code) {
        code.append("    // FloatButton初始化脚本\n");
        code.append("    var util = require('./js/__util__');\n");
        code.append("    var CreateRoundButtonView = require('./js/CreateRoundButtonView');\n");
        code.append("    var FloatButtonAnim = require('./js/FloatButtonAnim');\n\n");
        
        code.append("    // 配置对象\n");
        code.append("    var mConfig = {\n");
        code.append("        size: 60,\n");
        code.append("        tint: '#00000000',\n");
        code.append("        color: '#FFFFFF',\n");
        code.append("        isInit: false,\n");
        code.append("        isShow: false,\n");
        code.append("        padding: 10,\n");
        code.append("        logoAlpha: 1.0,\n");
        code.append("        isMenuOpen: false,\n");
        code.append("        isOrientation: false,\n");
        code.append("        menuRadius: 100\n");
        code.append("    };\n\n");
        
        code.append("    // 事件处理函数\n");
        code.append("    var eventActions = {\n");
        code.append("        item_click: function(item) {\n");
        code.append("            // 处理菜单项点击\n");
        code.append("            console.log('菜单项被点击:', item);\n");
        code.append("        },\n");
        code.append("        direction_changed: function(direction) {\n");
        code.append("            // 处理方向改变\n");
        code.append("            mConfig.direction = direction;\n");
        code.append("        },\n");
        code.append("        menu_state_changed: function(isOpen) {\n");
        code.append("            // 处理菜单状态改变\n");
        code.append("            mConfig.isMenuOpen = isOpen;\n");
        code.append("        },\n");
        code.append("        orientation_changed: function(orientation) {\n");
        code.append("            // 处理屏幕方向改变\n");
        code.append("            mConfig.orientation = orientation;\n");
        code.append("        }\n");
        code.append("    };\n\n");
        
        code.append("    // FloatButton主对象\n");
        code.append("    var FloatButton = {\n");
        code.append("        create: function() {\n");
        code.append("            // 创建悬浮按钮\n");
        code.append("            if (!mConfig.isInit) {\n");
        code.append("                // 初始化按钮视图\n");
        code.append("                CreateRoundButtonView();\n");
        code.append("                mConfig.isInit = true;\n");
        code.append("            }\n");
        code.append("        },\n");
        code.append("        show: function() {\n");
        code.append("            // 显示悬浮按钮\n");
        code.append("            if (mConfig.isInit && !mConfig.isShow) {\n");
        code.append("                FloatButtonAnim.show();\n");
        code.append("                mConfig.isShow = true;\n");
        code.append("            }\n");
        code.append("        },\n");
        code.append("        hide: function() {\n");
        code.append("            // 隐藏悬浮按钮\n");
        code.append("            if (mConfig.isShow) {\n");
        code.append("                FloatButtonAnim.hide();\n");
        code.append("                mConfig.isShow = false;\n");
        code.append("            }\n");
        code.append("        },\n");
        code.append("        close: function() {\n");
        code.append("            // 关闭悬浮按钮\n");
        code.append("            this.hide();\n");
        code.append("            mConfig.isInit = false;\n");
        code.append("        }\n");
        code.append("    };\n\n");
        
        code.append("    // 自动关闭菜单定时器\n");
        code.append("    var autoCloseMenu = function() {\n");
        code.append("        if (mConfig.timer) {\n");
        code.append("            clearTimeout(mConfig.timer);\n");
        code.append("        }\n");
        code.append("        mConfig.timer = setTimeout(function() {\n");
        code.append("            if (mConfig.isMenuOpen) {\n");
        code.append("                eventActions.menu_state_changed(false);\n");
        code.append("            }\n");
        code.append("        }, 5000); // 5秒后自动关闭\n");
        code.append("    };\n\n");
        
        code.append("    // 导出模块\n");
        code.append("    module.exports = FloatButton;\n");
    }
    
    private static void reconstructMainScript(String[] strings, StringBuilder code) {
        code.append("    // 主脚本 - 可能包含自动化逻辑\n");
        code.append("    var ui = require('ui');\n\n");
        
        code.append("    // 混淆变量（推测功能）\n");
        code.append("    var _0xodW = {}; // 主配置对象\n");
        code.append("    var _0xb144 = null; // 可能是按钮元素\n");
        code.append("    var _0x2049 = 0; // 状态码或计数器\n\n");
        
        code.append("    // 定时任务相关\n");
        code.append("    var dytime = 1000; // 执行间隔\n\n");
        
        code.append("    // 自动点击函数\n");
        code.append("    function apick() {\n");
        code.append("        // 执行自动点击操作\n");
        code.append("        console.log('执行自动点击');\n");
        code.append("    }\n\n");
        
        code.append("    // URL检查函数\n");
        code.append("    function urlck() {\n");
        code.append("        // 检查URL或网络状态\n");
        code.append("        console.log('检查URL状态');\n");
        code.append("    }\n\n");
        
        code.append("    // 主要功能函数\n");
        code.append("    function dfun() {\n");
        code.append("        // 主要的业务逻辑\n");
        code.append("        apick();\n");
        code.append("        urlck();\n");
        code.append("    }\n\n");
        
        code.append("    // 设置UI布局\n");
        code.append("    if (ui && ui.layout) {\n");
        code.append("        ui.layout(/* XML布局或动态创建 */);\n");
        code.append("    }\n\n");
        
        code.append("    // 启动定时任务\n");
        code.append("    if (typeof setInterval !== 'undefined') {\n");
        code.append("        setInterval(function() {\n");
        code.append("            dfun(); // 执行主要功能\n");
        code.append("        }, dytime);\n");
        code.append("    }\n");
    }
    
    private static void analyzeICodeInDetail(byte[] icode, String[] strings, StringBuilder analysis) {
        analysis.append("字节码长度: ").append(icode.length).append("\n");
        analysis.append("指令序列分析:\n");
        
        for (int i = 0; i < icode.length; i++) {
            int opcode = icode[i] & 0xFF;
            analysis.append(String.format("  %03d: %02X", i, opcode));
            
            // 分析常见的Rhino字节码指令
            switch (opcode) {
                case 0xD3: // 可能是字符串引用
                    if (i + 1 < icode.length) {
                        int stringIndex = icode[i + 1] & 0xFF;
                        if (stringIndex < strings.length) {
                            analysis.append(" (字符串引用: \"").append(strings[stringIndex]).append("\")");
                        }
                    }
                    break;
                case 0xE6: // 可能是函数调用
                    analysis.append(" (可能的函数调用)");
                    break;
                case 0xF1: // 可能是属性访问
                    analysis.append(" (可能的属性访问)");
                    break;
                case 0xFB: // 可能是返回
                    analysis.append(" (可能的返回)");
                    break;
            }
            analysis.append("\n");
        }
    }
    
    private static void setupMockEnvironment(Scriptable scope, String scriptName) {
        // 设置模拟的AutoJS环境
        if (scriptName.equals("init")) {
            // 模拟require函数
            scope.put("require", scope, new BaseFunction() {
                @Override
                public Object call(Context cx, Scriptable scope, Scriptable thisObj, Object[] args) {
                    if (args.length > 0) {
                        String moduleName = args[0].toString();
                        System.out.println("模拟require: " + moduleName);
                        return new NativeObject(); // 返回空对象
                    }
                    return Undefined.instance;
                }
            });
            
            // 模拟module对象
            NativeObject module = new NativeObject();
            module.put("exports", module, new NativeObject());
            scope.put("module", scope, module);
        }
    }
}

import org.mozilla.javascript.*;
import java.io.*;
import java.nio.file.*;
import java.nio.charset.StandardCharsets;

public class RhinoSourceExtractor {
    
    public static void main(String[] args) throws Exception {
        // 创建Rhino上下文
        Context cx = Context.enter();
        try {
            Scriptable scope = cx.initStandardObjects();
            
            // 处理所有解密的脚本文件
            String[] files = {
                "resources/assets/project/main.js",
                "resources/assets/project/FloatButton/init.js", 
                "resources/assets/project/FloatButton/js/CreateRoundButtonView.js",
                "resources/assets/project/FloatButton/js/FloatButtonAnim.js",
                "resources/assets/project/FloatButton/js/__util__.js",
                "resources/assets/project/FloatButton/widget/RoundButton.js"
            };
            
            ScriptDeserializer deserializer = new ScriptDeserializer();
            deserializer.initializeKeys();
            
            for (String filePath : files) {
                System.out.println("\n正在处理: " + filePath);
                System.out.println("===================================================");
                
                try {
                    // 读取并解密文件
                    byte[] fileData = Files.readAllBytes(Paths.get(filePath));
                    
                    // 跳过8字节头部
                    byte[] encryptedContent = new byte[fileData.length - 8];
                    System.arraycopy(fileData, 8, encryptedContent, 0, encryptedContent.length);
                    
                    // 解密
                    byte[] decryptedData = deserializer.performDecryption(encryptedContent);
                    System.out.println("解密完成，数据长度: " + decryptedData.length);
                    
                    // 反序列化JavaScript对象
                    try (ByteArrayInputStream bais = new ByteArrayInputStream(decryptedData);
                         ObjectInputStream ois = new ObjectInputStream(bais)) {
                        
                        Object obj = ois.readObject();
                        System.out.println("反序列化对象类型: " + obj.getClass().getName());
                        
                        if (obj instanceof Script) {
                            Script script = (Script) obj;
                            
                            // 尝试执行脚本来获取源代码信息
                            try {
                                // 创建一个特殊的作用域来捕获函数定义
                                Scriptable captureScope = cx.initStandardObjects();
                                
                                // 执行脚本
                                Object result = script.exec(cx, captureScope);
                                
                                // 尝试获取所有定义的函数和变量
                                Object[] ids = captureScope.getIds();
                                StringBuilder sourceBuilder = new StringBuilder();
                                
                                sourceBuilder.append("// 从执行结果重构的源代码\n");
                                sourceBuilder.append("// 文件: ").append(filePath).append("\n\n");
                                
                                for (Object id : ids) {
                                    if (id instanceof String) {
                                        String name = (String) id;
                                        Object value = captureScope.get(name, captureScope);
                                        
                                        if (value instanceof Function) {
                                            Function func = (Function) value;
                                            sourceBuilder.append("// 函数: ").append(name).append("\n");
                                            sourceBuilder.append("function ").append(name).append("() {\n");
                                            sourceBuilder.append("  // [原始实现已编译]\n");
                                            sourceBuilder.append("}\n\n");
                                        } else if (value != Scriptable.NOT_FOUND) {
                                            sourceBuilder.append("// 变量: ").append(name).append(" = ");
                                            sourceBuilder.append(Context.toString(value)).append("\n");
                                        }
                                    }
                                }
                                
                                String reconstructedSource = sourceBuilder.toString();
                                if (reconstructedSource.length() > 100) {
                                    String outputFileName = "rhino_extracted_" + Paths.get(filePath).getFileName().toString();
                                    Files.write(Paths.get(outputFileName), reconstructedSource.getBytes(StandardCharsets.UTF_8));
                                    System.out.println("已保存重构源代码到: " + outputFileName);
                                    System.out.println("内容长度: " + reconstructedSource.length());
                                    System.out.println("内容预览:\n" + reconstructedSource.substring(0, Math.min(200, reconstructedSource.length())) + "...");
                                } else {
                                    System.out.println("无法重构有意义的源代码");
                                }
                                
                            } catch (Exception execError) {
                                System.err.println("执行脚本失败: " + execError.getMessage());
                                
                                // 如果执行失败，尝试直接分析字节码
                                analyzeInterpretedFunction(obj);
                            }
                        } else {
                            System.out.println("对象不是Script类型，尝试直接分析");
                            analyzeInterpretedFunction(obj);
                        }
                        
                    } catch (Exception deserError) {
                        System.err.println("反序列化失败: " + deserError.getMessage());
                    }
                    
                } catch (Exception fileError) {
                    System.err.println("处理文件失败: " + fileError.getMessage());
                }
            }
            
        } finally {
            Context.exit();
        }
    }
    
    private static void analyzeInterpretedFunction(Object obj) {
        System.out.println("分析InterpretedFunction对象...");
        
        // 尝试获取基本信息
        if (obj.toString().contains("function")) {
            System.out.println("对象字符串表示: " + obj.toString());
        }
        
        // 这里可以添加更多的分析逻辑
        System.out.println("对象类型: " + obj.getClass().getName());
    }
}

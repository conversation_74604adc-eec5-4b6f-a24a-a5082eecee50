import java.io.*;
import java.nio.file.*;
import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * PPMT助手脚本反序列化工具
 * 用于学习逆向工程和密码学原理
 */
public class ScriptDeserializer {
    
    // 项目配置参数
    private static final String PACKAGE_NAME = "com.ppmtzs.scr";
    private static final String VERSION_NAME = "1.00";
    private static final String MAIN_SCRIPT_FILE = "main.js";
    private static final int VERSION_CODE = 100;
    private static final String BUILD_ID = "D0899DD7-100";
    private static final String APP_NAME = "PPMT助手";
    
    // 固定的加密常量
    private static final byte[] FIXED_PLAINTEXT = "9a1132118990c3db".getBytes();
    
    private byte[] mKey;  // 实际的AES密钥（通过加密生成）
    private String mInitVector;  // 初始向量字符串
    private byte[] keyStream;
    
    public ScriptDeserializer() throws Exception {
        generateKeys();
    }
    
    private void generateKeys() throws Exception {
        // 第一步：生成基础密钥哈希
        String keyString = PACKAGE_NAME + VERSION_NAME + MAIN_SCRIPT_FILE + VERSION_CODE;
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        String keyHash = bytesToHex(md5.digest(keyString.getBytes(StandardCharsets.UTF_8)));
        byte[] baseKey = keyHash.getBytes(StandardCharsets.UTF_8);

        // 第二步：生成初始向量字符串
        String ivString = BUILD_ID + APP_NAME;
        String ivHash = bytesToHex(md5.digest(ivString.getBytes(StandardCharsets.UTF_8)));
        this.mInitVector = ivHash.substring(0, 16);

        // 第三步：使用基础密钥和IV加密固定字符串生成真正的密钥
        // baseKey是32字节的UTF-8字符串，使用AES-256
        SecretKeySpec secretKey = new SecretKeySpec(baseKey, "AES");
        IvParameterSpec iv = new IvParameterSpec(this.mInitVector.getBytes(StandardCharsets.UTF_8));

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);

        this.mKey = cipher.doFinal(FIXED_PLAINTEXT);
        this.keyStream = this.mKey;  // 密钥流就是生成的密钥

        System.out.println("Base Key Hash: " + keyHash);
        System.out.println("IV String: " + this.mInitVector);
        System.out.println("Final Key: " + bytesToHex(this.mKey));
    }
    

    
    private byte[] decryptFile(String filePath) throws Exception {
        byte[] fileData = Files.readAllBytes(Paths.get(filePath));
        
        if (fileData.length < 8) {
            throw new IllegalArgumentException("文件太小，不是有效的加密脚本");
        }
        
        // 检查加密标识
        byte encryptionFlag = fileData[5];
        if (encryptionFlag != 18 && encryptionFlag != 19) {
            throw new IllegalArgumentException("不支持的加密类型: " + encryptionFlag);
        }
        
        // 跳过8字节文件头
        byte[] encryptedContent = new byte[fileData.length - 8];
        System.arraycopy(fileData, 8, encryptedContent, 0, encryptedContent.length);
        
        // 第一步：AES解密
        SecretKeySpec secretKey = new SecretKeySpec(mKey, "AES");
        IvParameterSpec iv = new IvParameterSpec(mInitVector.getBytes(StandardCharsets.UTF_8));

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);
        byte[] aesDecrypted = cipher.doFinal(encryptedContent);
        
        // 第二步：XOR解密
        byte[] finalDecrypted = new byte[aesDecrypted.length];
        for (int i = 0; i < aesDecrypted.length; i++) {
            finalDecrypted[i] = (byte) ((aesDecrypted[i] - keyStream[i % keyStream.length]) & 0xFF);
        }
        
        System.out.println("解密完成，数据长度: " + finalDecrypted.length);
        
        // 根据加密标识处理
        if (encryptionFlag == 18) {
            // 类型18：直接的JavaScript文本
            return finalDecrypted;
        } else {
            // 类型19：序列化的JavaScript对象，需要反序列化
            return deserializeScript(finalDecrypted);
        }
    }
    
    private byte[] deserializeScript(byte[] serializedData) throws Exception {
        System.out.println("开始反序列化JavaScript对象...");
        
        try (ByteArrayInputStream bais = new ByteArrayInputStream(serializedData);
             ObjectInputStream ois = new ObjectInputStream(bais)) {
            
            // 读取序列化的对象
            Object obj = ois.readObject();
            System.out.println("反序列化对象类型: " + obj.getClass().getName());
            
            // 如果是Rhino Script对象，尝试获取源代码
            if (obj.toString().contains("Script")) {
                // 这里需要更复杂的处理来提取JavaScript源代码
                // 由于Rhino Script对象的内部结构复杂，我们先返回对象的字符串表示
                return obj.toString().getBytes();
            }
            
            return serializedData; // 如果无法处理，返回原始数据
            
        } catch (Exception e) {
            System.err.println("反序列化失败: " + e.getMessage());
            e.printStackTrace();
            
            // 如果反序列化失败，尝试直接作为文本处理
            try {
                String text = new String(serializedData, "UTF-8");
                if (text.contains("function") || text.contains("var") || text.contains("console")) {
                    return serializedData;
                }
            } catch (Exception ignored) {
            }
            
            return serializedData;
        }
    }
    
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    public static void main(String[] args) {
        try {
            ScriptDeserializer deserializer = new ScriptDeserializer();
            
            // 解密所有脚本文件
            String[] files = {
                "resources/assets/project/main.js",
                "resources/assets/project/FloatButton/init.js",
                "resources/assets/project/FloatButton/js/CreateRoundButtonView.js",
                "resources/assets/project/FloatButton/js/FloatButtonAnim.js",
                "resources/assets/project/FloatButton/js/__util__.js",
                "resources/assets/project/FloatButton/widget/RoundButton.js"
            };
            
            for (String file : files) {
                try {
                    System.out.println("\n正在处理: " + file);
                    System.out.println("=" + "=".repeat(50));
                    
                    byte[] decrypted = deserializer.decryptFile(file);
                    
                    // 保存解密结果
                    String outputFile = "java_decrypted_" + Paths.get(file).getFileName().toString();
                    Files.write(Paths.get(outputFile), decrypted);
                    
                    System.out.println("已保存到: " + outputFile);
                    
                    // 显示前200个字符的预览
                    String preview = new String(decrypted, "UTF-8");
                    if (preview.length() > 200) {
                        preview = preview.substring(0, 200) + "...";
                    }
                    System.out.println("内容预览:\n" + preview);
                    
                } catch (Exception e) {
                    System.err.println("处理文件失败 " + file + ": " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

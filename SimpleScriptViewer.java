import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.lang.reflect.Field;

/**
 * PPMT助手脚本查看器 - 简单版
 * 只要能看到解密后的内容就行，混淆过也没关系
 */
public class SimpleScriptViewer {
    
    private byte[] mKey;
    private String mInitVector;
    private byte[] keyStream;
    
    public SimpleScriptViewer() throws Exception {
        // 项目配置参数 (从project.json获取)
        String packageName = "com.ppmtzs.scr";
        String versionName = "1.00";
        String mainScriptFile = "main.js";
        String versionCode = "100";
        String buildId = "D0899DD7-100";
        String appName = "PPMT助手";

        // 固定的加密常量
        byte[] fixedPlaintext = "9a1132118990c3db".getBytes(StandardCharsets.UTF_8);

        // 第一步：生成基础密钥哈希
        String keyString = packageName + versionName + mainScriptFile + versionCode;
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        String keyHash = bytesToHex(md5.digest(keyString.getBytes(StandardCharsets.UTF_8)));
        byte[] baseKey = keyHash.getBytes(StandardCharsets.UTF_8);

        // 第二步：生成初始向量字符串
        String ivString = buildId + appName;
        String ivHash = bytesToHex(md5.digest(ivString.getBytes(StandardCharsets.UTF_8)));
        this.mInitVector = ivHash.substring(0, 16);

        // 第三步：使用基础密钥和IV加密固定字符串生成真正的密钥
        SecretKeySpec secretKey = new SecretKeySpec(baseKey, "AES");
        IvParameterSpec iv = new IvParameterSpec(this.mInitVector.getBytes(StandardCharsets.UTF_8));

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);
        this.mKey = cipher.doFinal(fixedPlaintext);
        this.keyStream = this.mKey;  // 密钥流就是生成的密钥

        System.out.println("🔑 解密密钥已生成");
        System.out.println("📋 基础密钥: " + keyHash);
        System.out.println("📋 初始向量: " + this.mInitVector);
        System.out.println("📋 最终密钥: " + bytesToHex(this.mKey));
    }
    
    /**
     * 查看加密脚本内容
     */
    public void viewScript(String filename) {
        System.out.println("\n📖 查看脚本: " + filename);
        System.out.println("=" + "=".repeat(50));
        
        try {
            byte[] fileData = Files.readAllBytes(Paths.get(filename));
            
            // 检查文件头
            if (fileData.length < 8) {
                System.out.println("❌ 文件太小");
                return;
            }
            
            // 显示文件信息
            System.out.println("📄 文件大小: " + fileData.length + " 字节");
            System.out.print("📋 文件头: ");
            for (int i = 0; i < Math.min(8, fileData.length); i++) {
                System.out.printf("%02X ", fileData[i]);
            }
            System.out.println();
            
            // 验证加密文件头
            boolean isValidEncrypted = false;
            if (fileData.length >= 6) {
                // 检查实际的加密标识: {119, 1, 23, 127, 18} = {0x77, 0x01, 0x17, 0x7F, 0x12}
                byte[] expectedHeader = {119, 1, 23, 127, 18}; // 来自C1727a.f2345a
                boolean headerMatch = true;
                for (int i = 0; i < 5; i++) {
                    if (fileData[i] != expectedHeader[i]) {
                        headerMatch = false;
                        break;
                    }
                }

                if (headerMatch) {
                    byte encryptionType = fileData[5];
                    System.out.println("🔐 加密类型: " + encryptionType +
                        (encryptionType == 18 ? " (文本)" : encryptionType == 19 ? " (对象)" : " (未知)"));
                    isValidEncrypted = (encryptionType == 18 || encryptionType == 19);
                } else {
                    System.out.println("⚠️ 文件头不匹配标准加密格式");
                }
            }

            if (!isValidEncrypted) {
                System.out.println("❌ 不是有效的加密脚本文件");
                return;
            }

            // 解密
            byte[] decrypted = decrypt(fileData);
            
            // 尝试多种方式显示内容
            showContent(decrypted, filename);
            
        } catch (Exception e) {
            System.err.println("❌ 处理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private byte[] decrypt(byte[] fileData) throws Exception {
        // 跳过8字节文件头
        byte[] encryptedContent = new byte[fileData.length - 8];
        System.arraycopy(fileData, 8, encryptedContent, 0, encryptedContent.length);

        // 使用真正的密钥进行AES解密
        SecretKeySpec secretKey = new SecretKeySpec(mKey, "AES");
        IvParameterSpec iv = new IvParameterSpec(mInitVector.getBytes(StandardCharsets.UTF_8));

        // 尝试PKCS7Padding (Android标准)
        byte[] aesDecrypted;
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);
            aesDecrypted = cipher.doFinal(encryptedContent);
            System.out.println("✅ PKCS7解密成功，数据长度: " + aesDecrypted.length);
        } catch (Exception e) {
            System.out.println("⚠️ PKCS7解密失败，尝试PKCS5: " + e.getMessage());
            // 回退到PKCS5Padding
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);
            aesDecrypted = cipher.doFinal(encryptedContent);
            System.out.println("✅ PKCS5解密成功，数据长度: " + aesDecrypted.length);
        }

        // XOR解密 - 使用密钥作为密钥流
        byte[] finalDecrypted = new byte[aesDecrypted.length];
        for (int i = 0; i < aesDecrypted.length; i++) {
            finalDecrypted[i] = (byte) (aesDecrypted[i] ^ keyStream[i % keyStream.length]);
        }

        System.out.println("✅ XOR解密完成，最终数据长度: " + finalDecrypted.length);
        System.out.println("📋 最终数据头: " + String.format("%02X %02X %02X %02X",
            finalDecrypted[0], finalDecrypted[1], finalDecrypted[2], finalDecrypted[3]));

        return finalDecrypted;
    }
    
    private void showContent(byte[] data, String filename) {
        System.out.println("\n📝 内容展示:");
        System.out.println("-" + "-".repeat(50));
        
        // 方法1: 直接作为文本显示
        try {
            String text = new String(data, StandardCharsets.UTF_8);
            if (isReadableText(text)) {
                System.out.println("✅ 发现可读文本内容:");
                System.out.println(text);
                
                // 保存到文件
                String outputFile = "viewed_" + Paths.get(filename).getFileName().toString().replace(".js", ".txt");
                Files.write(Paths.get(outputFile), text.getBytes(StandardCharsets.UTF_8));
                System.out.println("\n💾 内容已保存到: " + outputFile);
                return;
            }
        } catch (Exception e) {
            System.out.println("⚠️ 文本解析失败: " + e.getMessage());
        }
        
        // 方法2: 尝试反序列化查看
        try {
            ObjectInputStream ois = new ObjectInputStream(new ByteArrayInputStream(data));
            Object obj = ois.readObject();
            ois.close();
            
            System.out.println("✅ 反序列化成功!");
            System.out.println("📦 对象类型: " + obj.getClass().getName());
            
            // 如果是Rhino对象，尝试提取信息
            if (obj.getClass().getName().contains("InterpretedFunction")) {
                extractRhinoInfo(obj, filename);
            } else {
                System.out.println("📄 对象内容: " + obj.toString());
            }
            
        } catch (Exception e) {
            System.out.println("⚠️ 反序列化失败: " + e.getMessage());
        }
        
        // 方法3: 十六进制显示
        System.out.println("\n🔢 十六进制内容 (前200字节):");
        showHexDump(data, Math.min(200, data.length));
        
        // 保存原始数据
        try {
            String rawFile = "raw_" + Paths.get(filename).getFileName().toString().replace(".js", ".bin");
            Files.write(Paths.get(rawFile), data);
            System.out.println("\n💾 原始数据已保存到: " + rawFile);
        } catch (Exception e) {
            System.err.println("保存失败: " + e.getMessage());
        }
    }
    
    private boolean isReadableText(String text) {
        if (text == null || text.length() < 10) return false;
        
        // 检查是否包含大量可打印字符
        int printableCount = 0;
        for (char c : text.toCharArray()) {
            if (c >= 32 && c <= 126) printableCount++;
        }
        
        double printableRatio = (double) printableCount / text.length();
        return printableRatio > 0.7; // 70%以上是可打印字符
    }
    
    private void extractRhinoInfo(Object func, String filename) {
        try {
            System.out.println("🎯 提取Rhino函数信息...");
            
            // 获取InterpreterData
            Field idataField = func.getClass().getDeclaredField("idata");
            idataField.setAccessible(true);
            Object idata = idataField.get(func);
            
            if (idata != null) {
                // 提取字符串表
                try {
                    Field stringTableField = idata.getClass().getDeclaredField("itsStringTable");
                    stringTableField.setAccessible(true);
                    String[] stringTable = (String[]) stringTableField.get(idata);
                    
                    if (stringTable != null && stringTable.length > 0) {
                        System.out.println("📝 字符串表 (" + stringTable.length + " 项):");
                        for (int i = 0; i < Math.min(20, stringTable.length); i++) {
                            System.out.println("  [" + i + "] " + stringTable[i]);
                        }
                        if (stringTable.length > 20) {
                            System.out.println("  ... (还有 " + (stringTable.length - 20) + " 项)");
                        }
                        
                        // 保存完整字符串表
                        String stringFile = "strings_" + Paths.get(filename).getFileName().toString().replace(".js", ".txt");
                        try (PrintWriter writer = new PrintWriter(stringFile, StandardCharsets.UTF_8)) {
                            for (int i = 0; i < stringTable.length; i++) {
                                writer.println("[" + i + "] " + stringTable[i]);
                            }
                        }
                        System.out.println("💾 完整字符串表已保存到: " + stringFile);
                    }
                } catch (Exception e) {
                    System.out.println("⚠️ 提取字符串表失败: " + e.getMessage());
                }
                
                // 提取参数名
                try {
                    Field paramNamesField = idata.getClass().getDeclaredField("argNames");
                    paramNamesField.setAccessible(true);
                    String[] paramNames = (String[]) paramNamesField.get(idata);
                    
                    if (paramNames != null && paramNames.length > 0) {
                        System.out.println("🏷️ 参数名:");
                        for (int i = 0; i < paramNames.length; i++) {
                            System.out.println("  参数[" + i + "]: " + paramNames[i]);
                        }
                    }
                } catch (Exception e) {
                    // 参数名字段可能不存在，忽略
                }
                
                // 显示字节码信息
                try {
                    Field icodeField = idata.getClass().getDeclaredField("itsICode");
                    icodeField.setAccessible(true);
                    byte[] icode = (byte[]) icodeField.get(idata);
                    
                    if (icode != null) {
                        System.out.println("🔢 字节码长度: " + icode.length + " 字节");
                        System.out.println("🔍 字节码开头:");
                        showHexDump(icode, Math.min(50, icode.length));
                    }
                } catch (Exception e) {
                    System.out.println("⚠️ 提取字节码失败: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ 提取Rhino信息失败: " + e.getMessage());
        }
    }
    
    private void showHexDump(byte[] data, int length) {
        for (int i = 0; i < length; i += 16) {
            System.out.printf("%04X: ", i);
            
            // 十六进制部分
            for (int j = 0; j < 16; j++) {
                if (i + j < length) {
                    System.out.printf("%02X ", data[i + j]);
                } else {
                    System.out.print("   ");
                }
            }
            
            System.out.print(" ");
            
            // ASCII部分
            for (int j = 0; j < 16 && i + j < length; j++) {
                byte b = data[i + j];
                if (b >= 32 && b <= 126) {
                    System.out.print((char) b);
                } else {
                    System.out.print(".");
                }
            }
            
            System.out.println();
        }
    }
    
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    public static void main(String[] args) {
        try {
            SimpleScriptViewer viewer = new SimpleScriptViewer();
            
            // 要查看的文件
            String[] files = {
                "resources/assets/project/main.js",
                "resources/assets/project/FloatButton/init.js",
                "resources/assets/project/FloatButton/js/CreateRoundButtonView.js",
                "resources/assets/project/FloatButton/js/FloatButtonAnim.js",
                "resources/assets/project/FloatButton/js/__util__.js",
                "resources/assets/project/FloatButton/widget/RoundButton.js"
            };
            
            System.out.println("👀 PPMT助手脚本查看器");
            System.out.println("=" + "=".repeat(50));
            
            for (String file : files) {
                if (Files.exists(Paths.get(file))) {
                    viewer.viewScript(file);
                } else {
                    System.out.println("⚠️ 文件不存在: " + file);
                }
            }
            
            System.out.println("\n🎉 查看完成！");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

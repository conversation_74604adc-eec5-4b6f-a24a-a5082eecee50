import java.security.MessageDigest;
import java.nio.charset.StandardCharsets;

public class TestEncoding {
    public static void main(String[] args) throws Exception {
        String BUILD_ID = "D0899DD7-100";
        String APP_NAME = "PPMT助手";
        
        String ivString = BUILD_ID + APP_NAME;
        System.out.println("IV String: " + ivString);
        
        byte[] ivBytes = ivString.getBytes(StandardCharsets.UTF_8);
        System.out.print("IV Bytes: ");
        for (byte b : ivBytes) {
            System.out.printf("%02x ", b & 0xFF);
        }
        System.out.println();
        
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] hash = md5.digest(ivBytes);
        String hashHex = bytesToHex(hash);
        System.out.println("IV Hash: " + hashHex);
        System.out.println("IV 16: " + hashHex.substring(0, 16));
    }
    
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
}

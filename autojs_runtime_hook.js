/**
 * AutoJS运行时Hook脚本
 * 用于在运行时获取解密后的JavaScript源码
 * 使用方法: frida -U -f com.ppmtzs.scr -l autojs_runtime_hook.js --no-pause
 */

console.log("[+] AutoJS运行时Hook脚本启动");

Java.perform(function() {
    console.log("[+] Java环境已准备就绪");
    
    // Hook ScriptEncryption类的解密方法
    try {
        var ScriptEncryption = Java.use("com.stardust.autojs.engine.encryption.ScriptEncryption");
        console.log("[+] 找到ScriptEncryption类");
        
        // Hook decrypt方法
        ScriptEncryption.decrypt.overload('[B', 'java.lang.String').implementation = function(data, key) {
            console.log("[+] ScriptEncryption.decrypt被调用");
            console.log("    数据长度: " + data.length);
            console.log("    密钥: " + key);
            
            var result = this.decrypt(data, key);
            
            if (result != null) {
                console.log("[+] 解密成功，结果长度: " + result.length);
                
                // 尝试将结果转换为字符串
                try {
                    var decryptedString = Java.use("java.lang.String").$new(result, "UTF-8");
                    console.log("[+] 解密后的内容:");
                    console.log("=" + "=".repeat(60));
                    console.log(decryptedString);
                    console.log("=" + "=".repeat(60));
                    
                    // 保存到文件
                    saveToFile("decrypted_script_" + Date.now() + ".js", decryptedString);
                } catch (e) {
                    console.log("[-] 转换为字符串失败: " + e);
                    console.log("[+] 十六进制数据: " + bytesToHex(result));
                }
            }
            
            return result;
        };
        
    } catch (e) {
        console.log("[-] Hook ScriptEncryption失败: " + e);
    }
    
    // Hook Rhino的Script执行
    try {
        var Script = Java.use("org.mozilla.javascript.Script");
        console.log("[+] 找到Rhino Script类");
        
        Script.exec.overload('org.mozilla.javascript.Context', 'org.mozilla.javascript.Scriptable').implementation = function(cx, scope) {
            console.log("[+] Script.exec被调用");
            
            // 尝试获取脚本源码
            try {
                if (this.toString) {
                    var scriptSource = this.toString();
                    if (scriptSource && scriptSource.length > 10) {
                        console.log("[+] 脚本源码:");
                        console.log("=" + "=".repeat(60));
                        console.log(scriptSource);
                        console.log("=" + "=".repeat(60));
                        
                        saveToFile("runtime_script_" + Date.now() + ".js", scriptSource);
                    }
                }
            } catch (e) {
                console.log("[-] 获取脚本源码失败: " + e);
            }
            
            return this.exec(cx, scope);
        };
        
    } catch (e) {
        console.log("[-] Hook Rhino Script失败: " + e);
    }
    
    // Hook InterpretedFunction (编译后的函数)
    try {
        var InterpretedFunction = Java.use("org.mozilla.javascript.InterpretedFunction");
        console.log("[+] 找到InterpretedFunction类");
        
        InterpretedFunction.call.overload('org.mozilla.javascript.Context', 'org.mozilla.javascript.Scriptable', 'org.mozilla.javascript.Scriptable', '[Ljava.lang.Object;').implementation = function(cx, scope, thisObj, args) {
            console.log("[+] InterpretedFunction.call被调用");
            
            // 尝试获取函数信息
            try {
                var functionName = this.getFunctionName();
                if (functionName) {
                    console.log("    函数名: " + functionName);
                }
                
                // 尝试获取源码
                var source = this.getEncodedSource();
                if (source) {
                    console.log("    函数源码: " + source);
                    saveToFile("function_" + functionName + "_" + Date.now() + ".js", source);
                }
            } catch (e) {
                console.log("[-] 获取函数信息失败: " + e);
            }
            
            return this.call(cx, scope, thisObj, args);
        };
        
    } catch (e) {
        console.log("[-] Hook InterpretedFunction失败: " + e);
    }
    
    // Hook ScriptSource类
    try {
        var ScriptSource = Java.use("com.stardust.autojs.script.ScriptSource");
        console.log("[+] 找到ScriptSource类");
        
        ScriptSource.getScript.implementation = function() {
            console.log("[+] ScriptSource.getScript被调用");
            
            var result = this.getScript();
            if (result) {
                console.log("[+] 获取到脚本内容:");
                console.log("=" + "=".repeat(60));
                console.log(result);
                console.log("=" + "=".repeat(60));
                
                saveToFile("script_source_" + Date.now() + ".js", result);
            }
            
            return result;
        };
        
    } catch (e) {
        console.log("[-] Hook ScriptSource失败: " + e);
    }
    
    // Hook JavaScriptSource类
    try {
        var JavaScriptSource = Java.use("com.stardust.autojs.script.JavaScriptSource");
        console.log("[+] 找到JavaScriptSource类");
        
        JavaScriptSource.getScript.implementation = function() {
            console.log("[+] JavaScriptSource.getScript被调用");
            
            var result = this.getScript();
            if (result) {
                console.log("[+] JavaScript源码:");
                console.log("=" + "=".repeat(60));
                console.log(result);
                console.log("=" + "=".repeat(60));
                
                saveToFile("javascript_source_" + Date.now() + ".js", result);
            }
            
            return result;
        };
        
    } catch (e) {
        console.log("[-] Hook JavaScriptSource失败: " + e);
    }
    
    // Hook EncryptedScriptFileSource类
    try {
        var EncryptedScriptFileSource = Java.use("com.stardust.autojs.script.EncryptedScriptFileSource");
        console.log("[+] 找到EncryptedScriptFileSource类");
        
        EncryptedScriptFileSource.getScript.implementation = function() {
            console.log("[+] EncryptedScriptFileSource.getScript被调用");
            console.log("    文件路径: " + this.getPath());
            
            var result = this.getScript();
            if (result) {
                console.log("[+] 解密后的脚本:");
                console.log("=" + "=".repeat(60));
                console.log(result);
                console.log("=" + "=".repeat(60));
                
                var filename = "encrypted_script_" + this.getPath().replace(/[\/\\:]/g, "_") + "_" + Date.now() + ".js";
                saveToFile(filename, result);
            }
            
            return result;
        };
        
    } catch (e) {
        console.log("[-] Hook EncryptedScriptFileSource失败: " + e);
    }
    
    // Hook Context.compileString方法
    try {
        var Context = Java.use("org.mozilla.javascript.Context");
        console.log("[+] 找到Rhino Context类");
        
        Context.compileString.overload('java.lang.String', 'java.lang.String', 'int', 'java.lang.Object').implementation = function(source, sourceName, lineno, securityDomain) {
            console.log("[+] Context.compileString被调用");
            console.log("    源文件名: " + sourceName);
            console.log("    行号: " + lineno);
            
            if (source && source.length > 10) {
                console.log("[+] 编译的JavaScript源码:");
                console.log("=" + "=".repeat(60));
                console.log(source);
                console.log("=" + "=".repeat(60));
                
                var filename = "compiled_" + sourceName.replace(/[\/\\:]/g, "_") + "_" + Date.now() + ".js";
                saveToFile(filename, source);
            }
            
            return this.compileString(source, sourceName, lineno, securityDomain);
        };
        
    } catch (e) {
        console.log("[-] Hook Context.compileString失败: " + e);
    }
    
    // Hook文件读取操作
    try {
        var FileInputStream = Java.use("java.io.FileInputStream");
        console.log("[+] 找到FileInputStream类");
        
        FileInputStream.$init.overload('java.lang.String').implementation = function(name) {
            if (name.endsWith('.js')) {
                console.log("[+] 读取JS文件: " + name);
            }
            return this.$init(name);
        };
        
    } catch (e) {
        console.log("[-] Hook FileInputStream失败: " + e);
    }
    
    console.log("[+] 所有Hook已设置完成，等待脚本执行...");
});

// 辅助函数：字节数组转十六进制
function bytesToHex(bytes) {
    var hex = "";
    for (var i = 0; i < Math.min(bytes.length, 200); i++) {
        var byte = bytes[i] & 0xFF;
        hex += ("0" + byte.toString(16)).slice(-2) + " ";
        if ((i + 1) % 16 === 0) hex += "\n";
    }
    return hex;
}

// 辅助函数：保存内容到文件
function saveToFile(filename, content) {
    try {
        Java.perform(function() {
            var File = Java.use("java.io.File");
            var FileWriter = Java.use("java.io.FileWriter");
            
            var sdcard = "/sdcard/";
            var file = File.$new(sdcard + filename);
            var writer = FileWriter.$new(file);
            
            writer.write(content);
            writer.close();
            
            console.log("[+] 内容已保存到: " + sdcard + filename);
        });
    } catch (e) {
        console.log("[-] 保存文件失败: " + e);
    }
}

// 监听应用启动
Java.perform(function() {
    console.log("[+] 开始监听应用活动...");
    
    // Hook Activity的onCreate方法
    try {
        var Activity = Java.use("android.app.Activity");
        Activity.onCreate.overload('android.os.Bundle').implementation = function(savedInstanceState) {
            console.log("[+] Activity.onCreate: " + this.getClass().getName());
            return this.onCreate(savedInstanceState);
        };
    } catch (e) {
        console.log("[-] Hook Activity失败: " + e);
    }
});

console.log("[+] AutoJS运行时Hook脚本已加载完成");
console.log("[+] 请启动目标应用并执行脚本，解密后的内容将自动保存到/sdcard/目录");

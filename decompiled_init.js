// init.js 反编译结果

function init() {
    // FloatButton初始化脚本
    var util = require('./js/__util__');
    var CreateRoundButtonView = require('./js/CreateRoundButtonView');
    var FloatButtonAnim = require('./js/FloatButtonAnim');

    // 配置对象
    var mConfig = {
        size: 60,
        tint: '#00000000',
        color: '#FFFFFF',
        isInit: false,
        isShow: false,
        padding: 10,
        logoAlpha: 1.0,
        isMenuOpen: false,
        isOrientation: false,
        menuRadius: 100
    };

    // 事件处理函数
    var eventActions = {
        item_click: function(item) {
            // 处理菜单项点击
            console.log('菜单项被点击:', item);
        },
        direction_changed: function(direction) {
            // 处理方向改变
            mConfig.direction = direction;
        },
        menu_state_changed: function(isOpen) {
            // 处理菜单状态改变
            mConfig.isMenuOpen = isOpen;
        },
        orientation_changed: function(orientation) {
            // 处理屏幕方向改变
            mConfig.orientation = orientation;
        }
    };

    // FloatButton主对象
    var FloatButton = {
        create: function() {
            // 创建悬浮按钮
            if (!mConfig.isInit) {
                // 初始化按钮视图
                CreateRoundButtonView();
                mConfig.isInit = true;
            }
        },
        show: function() {
            // 显示悬浮按钮
            if (mConfig.isInit && !mConfig.isShow) {
                FloatButtonAnim.show();
                mConfig.isShow = true;
            }
        },
        hide: function() {
            // 隐藏悬浮按钮
            if (mConfig.isShow) {
                FloatButtonAnim.hide();
                mConfig.isShow = false;
            }
        },
        close: function() {
            // 关闭悬浮按钮
            this.hide();
            mConfig.isInit = false;
        }
    };

    // 自动关闭菜单定时器
    var autoCloseMenu = function() {
        if (mConfig.timer) {
            clearTimeout(mConfig.timer);
        }
        mConfig.timer = setTimeout(function() {
            if (mConfig.isMenuOpen) {
                eventActions.menu_state_changed(false);
            }
        }, 5000); // 5秒后自动关闭
    };

    // 导出模块
    module.exports = FloatButton;
}

/*
字节码分析:
字节码长度: 641
指令序列分析:
  000: E6 (可能的函数调用)
  001: 00
  002: 02
  003: DF
  004: E3
  005: D7
  006: F1 (可能的属性访问)
  007: D6
  008: 29
  009: DF
  010: 26
  011: E2
  012: E0
  013: 43
  014: 02
  015: D5
  016: 31
  017: D4
  018: 27
  019: D5
  020: 21
  021: D5
  022: C5
  023: FC
  024: D3 (字符串引用: "isHorizontalScreen")
  025: 04
  026: 31
  027: D4
  028: 27
  029: D3 (字符串引用: "isHorizontalScreen")
  030: 04
  031: 21
  032: D3 (字符串引用: "isHorizontalScreen")
  033: 04
  034: C5
  035: FC
  036: D3 (字符串引用: "ObjectDefinePro")
  037: 05
  038: 31
  039: D4
  040: 27
  041: D3 (字符串引用: "ObjectDefinePro")
  042: 05
  043: 21
  044: D3 (字符串引用: "ObjectDefinePro")
  045: 05
  046: C5
  047: FC
  048: D4
  049: 27
  050: 03
  051: FC
  052: E6 (可能的函数调用)
  053: 00
  054: 03
  055: D3 (字符串引用: "CreateRoundButtonView")
  056: 06
  057: 31
  058: D7
  059: F1 (可能的属性访问)
  060: D3 (字符串引用: "./js/CreateRoundButtonView")
  061: 07
  062: 29
  063: DF
  064: 26
  065: D3 (字符串引用: "CreateRoundButtonView")
  066: 06
  067: C5
  068: FC
  069: E6 (可能的函数调用)
  070: 00
  071: 04
  072: D3 (字符串引用: "FloatButtonAnim")
  073: 08
  074: 31
  075: D7
  076: F1 (可能的属性访问)
  077: D3 (字符串引用: "./js/FloatButtonAnim")
  078: 09
  079: 29
  080: DF
  081: 26
  082: D3 (字符串引用: "FloatButtonAnim")
  083: 08
  084: C5
  085: FC
  086: E6 (可能的函数调用)
  087: 00
  088: 09
  089: D3 (字符串引用: "mConfig")
  090: 0A
  091: 31
  092: D3 (字符串引用: "Object")
  093: 0B
  094: 27
  095: E0
  096: 1E
  097: D3 (字符串引用: "mConfig")
  098: 0A
  099: 08
  100: FC
  101: E6 (可能的函数调用)
  102: 00
  103: 0A
  104: D3 (字符串引用: "mConfig")
  105: 0A
  106: 27
  107: E0
  108: 28
  109: D3 (字符串引用: "y")
  110: 0C
  111: 23
  112: FB (可能的返回)
  113: E6 (可能的函数调用)
  114: 00
  115: 0B
  116: D3 (字符串引用: "mConfig")
  117: 0A
  118: 27
  119: D5
  120: F1 (可能的属性访问)
  121: E5
  122: 00
  123: 28
  124: DF
  125: 26
  126: D3 (字符串引用: "size")
  127: 0D
  128: 23
  129: FB (可能的返回)
  130: E6 (可能的函数调用)
  131: 00
  132: 0C
  133: D3 (字符串引用: "mConfig")
  134: 0A
  135: 27
  136: D3 (字符串引用: "#00000000")
  137: 0E
  138: 29
  139: D3 (字符串引用: "tint")
  140: 0F
  141: 23
  142: FB (可能的返回)
  143: E6 (可能的函数调用)
  144: 00
  145: 0D
  146: D3 (字符串引用: "mConfig")
  147: 0A
  148: 27
  149: D3 (字符串引用: "#FFFFFF")
  150: 10
  151: 29
  152: D3 (字符串引用: "color")
  153: 11
  154: 23
  155: FB (可能的返回)
  156: E6 (可能的函数调用)
  157: 00
  158: 0E
  159: D3 (字符串引用: "mConfig")
  160: 0A
  161: 27
  162: 2C
  163: D3 (字符串引用: "isInit")
  164: 12
  165: 23
  166: FB (可能的返回)
  167: E6 (可能的函数调用)
  168: 00
  169: 0F
  170: D3 (字符串引用: "mConfig")
  171: 0A
  172: 27
  173: 2C
  174: D3 (字符串引用: "isShow")
  175: 13
  176: 23
  177: FB (可能的返回)
  178: E6 (可能的函数调用)
  179: 00
  180: 10
  181: D3 (字符串引用: "mConfig")
  182: 0A
  183: 27
  184: D5
  185: F1 (可能的属性访问)
  186: E5
  187: 00
  188: 08
  189: DF
  190: 26
  191: D3 (字符串引用: "padding")
  192: 14
  193: 23
  194: FB (可能的返回)
  195: E6 (可能的函数调用)
  196: 00
  197: 11
  198: D3 (字符串引用: "mConfig")
  199: 0A
  200: 27
  201: DF
  202: 28
  203: D3 (字符串引用: "logoAlpha")
  204: 15
  205: 23
  206: FB (可能的返回)
  207: E6 (可能的函数调用)
  208: 00
  209: 12
  210: D3 (字符串引用: "mConfig")
  211: 0A
  212: 27
  213: 2C
  214: D3 (字符串引用: "isMenuOpen")
  215: 16
  216: 23
  217: FB (可能的返回)
  218: E6 (可能的函数调用)
  219: 00
  220: 13
  221: D3 (字符串引用: "mConfig")
  222: 0A
  223: 27
  224: D3 (字符串引用: "isHorizontalScreen")
  225: 04
  226: F1 (可能的属性访问)
  227: E0
  228: 26
  229: D3 (字符串引用: "isOrientation")
  230: 17
  231: 23
  232: FB (可能的返回)
  233: E6 (可能的函数调用)
  234: 00
  235: 14
  236: D3 (字符串引用: "mConfig")
  237: 0A
  238: 27
  239: D5
  240: F1 (可能的属性访问)
  241: E5
  242: 00
  243: 50
  244: DF
  245: 26
  246: D3 (字符串引用: "menuRadius")
  247: 18
  248: 23
  249: FB (可能的返回)
  250: E6 (可能的函数调用)
  251: 00
  252: 15
  253: D3 (字符串引用: "mConfig")
  254: 0A
  255: 27
  256: 2A
  257: D3 (字符串引用: "timer")
  258: 19
  259: 23
  260: FB (可能的返回)
  261: E6 (可能的函数调用)
  262: 00
  263: 17
  264: D3 (字符串引用: "mConfig")
  265: 0A
  266: 27
  267: D3 (字符串引用: "Object")
  268: 0B
  269: 27
  270: E0
  271: 1E
  272: D3 (字符串引用: "anim")
  273: 1A
  274: 23
  275: FB (可能的返回)
  276: E6 (可能的函数调用)
  277: 00
  278: 19
  279: D3 (字符串引用: "mConfig")
  280: 0A
  281: 27
  282: D3 (字符串引用: "Object")
  283: 0B
  284: 27
  285: E0
  286: 1E
  287: D3 (字符串引用: "state")
  288: 1B
  289: 23
  290: FB (可能的返回)
  291: E6 (可能的函数调用)
  292: 00
  293: 1A
  294: D3 (字符串引用: "mConfig")
  295: 0A
  296: 27
  297: D3 (字符串引用: "state")
  298: 1B
  299: 21
  300: 2C
  301: D3 (字符串引用: "anim")
  302: 1A
  303: 23
  304: FB (可能的返回)
  305: E6 (可能的函数调用)
  306: 00
  307: 1B
  308: D3 (字符串引用: "mConfig")
  309: 0A
  310: 27
  311: D3 (字符串引用: "state")
  312: 1B
  313: 21
  314: 2C
  315: D3 (字符串引用: "menuOpen")
  316: 1C
  317: 23
  318: FB (可能的返回)
  319: E6 (可能的函数调用)
  320: 00
  321: 1C
  322: D3 (字符串引用: "mConfig")
  323: 0A
  324: 27
  325: D3 (字符串引用: "state")
  326: 1B
  327: 21
  328: 2C
  329: D3 (字符串引用: "direction")
  330: 1D
  331: 23
  332: FB (可能的返回)
  333: E6 (可能的函数调用)
  334: 00
  335: 1D
  336: D3 (字符串引用: "mConfig")
  337: 0A
  338: 27
  339: D3 (字符串引用: "state")
  340: 1B
  341: 21
  342: D3 (字符串引用: "isHorizontalScreen")
  343: 04
  344: F1 (可能的属性访问)
  345: E0
  346: 26
  347: D3 (字符串引用: "orientation")
  348: 1E
  349: 23
  350: FB (可能的返回)
  351: E6 (可能的函数调用)
  352: 00
  353: 1F
  354: D3 (字符串引用: "mConfig")
  355: 0A
  356: 27
  357: D3 (字符串引用: "Object")
  358: 0B
  359: 27
  360: E0
  361: 1E
  362: D3 (字符串引用: "eventActions")
  363: 1F
  364: 23
  365: FB (可能的返回)
  366: E6 (可能的函数调用)
  367: 00
  368: 20
  369: D3 (字符串引用: "mConfig")
  370: 0A
  371: 27
  372: D3 (字符串引用: "eventActions")
  373: 1F
  374: 21
  375: D3 (字符串引用: "Function")
  376: 20
  377: 27
  378: E0
  379: 1E
  380: D3 (字符串引用: "create")
  381: 21
  382: 23
  383: FB (可能的返回)
  384: E6 (可能的函数调用)
  385: 00
  386: 21
  387: D3 (字符串引用: "mConfig")
  388: 0A
  389: 27
  390: D3 (字符串引用: "eventActions")
  391: 1F
  392: 21
  393: D3 (字符串引用: "Function")
  394: 20
  395: 27
  396: E0
  397: 1E
  398: D3 (字符串引用: "show")
  399: 22
  400: 23
  401: FB (可能的返回)
  402: E6 (可能的函数调用)
  403: 00
  404: 22
  405: D3 (字符串引用: "mConfig")
  406: 0A
  407: 27
  408: D3 (字符串引用: "eventActions")
  409: 1F
  410: 21
  411: D3 (字符串引用: "Function")
  412: 20
  413: 27
  414: E0
  415: 1E
  416: D3 (字符串引用: "hide")
  417: 23
  418: 23
  419: FB (可能的返回)
  420: E6 (可能的函数调用)
  421: 00
  422: 23
  423: D3 (字符串引用: "mConfig")
  424: 0A
  425: 27
  426: D3 (字符串引用: "eventActions")
  427: 1F
  428: 21
  429: D3 (字符串引用: "Function")
  430: 20
  431: 27
  432: E0
  433: 1E
  434: D3 (字符串引用: "close")
  435: 24
  436: 23
  437: FB (可能的返回)
  438: E6 (可能的函数调用)
  439: 00
  440: 24
  441: D3 (字符串引用: "mConfig")
  442: 0A
  443: 27
  444: D3 (字符串引用: "eventActions")
  445: 1F
  446: 21
  447: D3 (字符串引用: "Function")
  448: 20
  449: 27
  450: E0
  451: 1E
  452: D3 (字符串引用: "item_click")
  453: 25
  454: 23
  455: FB (可能的返回)
  456: E6 (可能的函数调用)
  457: 00
  458: 25
  459: D3 (字符串引用: "mConfig")
  460: 0A
  461: 27
  462: D3 (字符串引用: "eventActions")
  463: 1F
  464: 21
  465: D3 (字符串引用: "Function")
  466: 20
  467: 27
  468: E0
  469: 1E
  470: D3 (字符串引用: "direction_changed")
  471: 26
  472: 23
  473: FB (可能的返回)
  474: E6 (可能的函数调用)
  475: 00
  476: 26
  477: D3 (字符串引用: "mConfig")
  478: 0A
  479: 27
  480: D3 (字符串引用: "eventActions")
  481: 1F
  482: 21
  483: D3 (字符串引用: "Function")
  484: 20
  485: 27
  486: E0
  487: 1E
  488: D3 (字符串引用: "menu_state_changed")
  489: 27
  490: 23
  491: FB (可能的返回)
  492: E6 (可能的函数调用)
  493: 00
  494: 27
  495: D3 (字符串引用: "mConfig")
  496: 0A
  497: 27
  498: D3 (字符串引用: "eventActions")
  499: 1F
  500: 21
  501: D3 (字符串引用: "Function")
  502: 20
  503: 27
  504: E0
  505: 1E
  506: D3 (字符串引用: "orientation_changed")
  507: 28
  508: 23
  509: FB (可能的返回)
  510: E6 (可能的函数调用)
  511: 00
  512: 29
  513: D3 (字符串引用: "mConfig")
  514: 0A
  515: 27
  516: D3 (字符串引用: "Object")
  517: 0B
  518: 27
  519: E0
  520: 1E
  521: D3 (字符串引用: "time")
  522: 29
  523: 23
  524: FB (可能的返回)
  525: E6 (可能的函数调用)
  526: 00
  527: 2A
  528: D3 (字符串引用: "mConfig")
  529: 0A
  530: 27
  531: D3 (字符串引用: "time")
  532: 29
  533: 21
  534: E5
  535: 00
  536: D2
  537: D3 (字符串引用: "menu")
  538: 2A
  539: 23
  540: FB (可能的返回)
  541: E6 (可能的函数调用)
  542: 00
  543: 2B
  544: D3 (字符串引用: "mConfig")
  545: 0A
  546: 27
  547: D3 (字符串引用: "time")
  548: 29
  549: 21
  550: E5
  551: 01
  552: F4
  553: D3 (字符串引用: "show")
  554: 22
  555: 23
  556: FB (可能的返回)
  557: E6 (可能的函数调用)
  558: 00
  559: 2C
  560: D3 (字符串引用: "mConfig")
  561: 0A
  562: 27
  563: D3 (字符串引用: "time")
  564: 29
  565: 21
  566: E5
  567: 01
  568: 5E
  569: D3 (字符串引用: "direction")
  570: 1D
  571: 23
  572: FB (可能的返回)
  573: E6 (可能的函数调用)
  574: 00
  575: 2D
  576: D3 (字符串引用: "mConfig")
  577: 0A
  578: 27
  579: D3 (字符串引用: "time")
  580: 29
  581: 21
  582: E5
  583: 00
  584: D2
  585: D3 (字符串引用: "buttonAnim")
  586: 2B
  587: 23
  588: FB (可能的返回)
  589: E6 (可能的函数调用)
  590: 00
  591: 2E
  592: D3 (字符串引用: "mConfig")
  593: 0A
  594: 27
  595: D3 (字符串引用: "time")
  596: 29
  597: 21
  598: CD
  599: D3 (字符串引用: "autoCloseMenu")
  600: 2C
  601: 23
  602: FB (可能的返回)
  603: E6 (可能的函数调用)
  604: 00
  605: 31
  606: D3 (字符串引用: "FloatButton")
  607: 2D
  608: 31
  609: E0
  610: ED
  611: EE
  612: E0
  613: 26
  614: D3 (字符串引用: "FloatButton")
  615: 2D
  616: 08
  617: FC
  618: E6 (可能的函数调用)
  619: 01
  620: 94
  621: D3 (字符串引用: "module")
  622: 2E
  623: 27
  624: DE
  625: E3
  626: D3 (字符串引用: "FloatButton")
  627: 2D
  628: 27
  629: E2
  630: D3 (字符串引用: "mConfig")
  631: 0A
  632: 27
  633: E2
  634: DF
  635: 43
  636: D3 (字符串引用: "exports")
  637: 2F
  638: 23
  639: FB (可能的返回)
  640: 41
*/

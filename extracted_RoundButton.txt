=== JavaScript代码提取结果 ===
源文件: bytecode_RoundButton.bin
提取时间: Thu Jun 26 14:42:43 CST 2025
JavaScript字符串数量: 194
代码片段数量: 2

=== JavaScript相关字符串 ===
[  0] ls
[  1] *o
[  2] o2
[  3] Nu
[  4] (L
[  5] r{qm
[  6] ttc
[  7] ve
[  8] rD
[  9] .u
[ 10] +L
[ 11] r{qm
[ 12] ttc
[ 13] vt
[ 14] rE\
[ 15] Lj
[ 16] ~aql
[ 17] 1Qb
[ 18] la.
[ 19] #q
[ 20] t.B
[ 21] seNu
[ 22] {q~
[ 23] men
[ 24] sA
[ 25] bu
[ 26] -wu
[ 27] 8l
[ 28] v{
[ 29] p{r
[ 30] ep
[ 31] pe
[ 32] vo
[ 33] rt
[ 34] {~w
[ 35] qt
[ 36] )o
[ 37] o2
[ 38] tab
[ 39] mS
[ 40] ~il
[ 41] a2
[ 42] rW
[ 43] t'E
[ 44] xN
[ 45] Wea
[ 46] mdT
[ 47] qe
[ 48] \n
[ 49] a/
[ 50] i|qM
[ 51] #L
[ 52] r{qm
[ 53] ttc
[ 54] abl
[ 55] ;\
[ 56] qt
[ 57] ry>
[ 58] 2un
[ 59] qu
[ 60] oD
[ 61] Tw
[ 62] I:
[ 63] sv
[ 64] 6sq
[ 65] tR
[ 66] pv
[ 67] \v
[ 68] dd
[ 69] ion
[ 70] r{d
[ 71] vceEvdK
[ 72] }qd
[ 73] als
[ 74] ry
[ 75] yW
[ 76] deH
[ 77] sxMs
[ 78] ctK
[ 79] Zw
[ 80] wM
[ 81] xCa
[ 82] te
[ 83] yI
[ 84] Ua
[ 85] wMa
[ 86] ^a
[ 87] 3lan
[ 88] }a
[ 89] wq
[ 90] vw
[ 91] je
[ 92] 3la
[ 93] o3]t
[ 94] yE\
[ 95] >Lja
[ 96] a3
[ 97] o/m
[ 98] fle
[ 99] c1
[100] K_
[101] 2i
[102] Dwu
[103] btg
[104] snt
[105] b|
[106] [e
[107] ue
[108] 0e
[109] Ts
[110] w|
[111] 1Yr
[112] |e
[113] Ee
[114] T~
[115] cry
[116] Ss
[117] geF
[118] te
[119] 0e
[120] it
[121] a|Kd
[122] `L
[123] k/m
[124] zy
[125] KL
[126] 6pa
[127] vtLa
[128] ej
[129] s`y<H
[130] c2|
[131] xp
[132] Gre
[133] cpg
[134] Ft
[135] *ia>"
[136] !5M
[137] S?
[138] 5n *
[139] 2ja
[140] aw
[141] 6W~
[142] <M
[143] mpp
[144] [j
[145] [':p
[146] !".
[147] #3
y "S
[148] ac
[149] U/s
[150] $Ti_
[151] Za
[152] /n
[153] >pp
[154] c
[155] gc
[156] l{
[157] eCo
[158] wv
[159] Yi
[160] At
[161] ag
[162] v|T
[163] ppp
[164] s*
[165] ;+
[166] Jw
[167] \t
[168] <im
[169] `
[170] `    `  `
[171] qw
[172] ze
[173] ig
[174] ``
[175] }q
[176] a>]
[177] xq
[178] mtBa
[179] rqw
[180] mq
[181] At
[182] rqt
[183] re
[184] rk
[185] ve
[186] 2v3
[187] s/aaw
[188] ,pq
[189] gt
[190] q|w
[191] Lja
[192] a2
[193] o.Q~z

=== 重构的代码片段 ===
// 属性访问: t.B
// 属性访问: o.Q~z

=== 按类别分组 ===

--- 函数和方法 ---

--- 变量和属性 ---
ls
o2
Nu
ttc
ve
rD
ttc
vt
Lj
t.B
seNu
men
sA
bu
ep
pe
vo
rt
qt
o2
tab
mS
a2
rW
xN
Wea
mdT
qe
ttc
abl
qt
qu
oD
Tw
sv
tR
pv
dd
ion
vceEvdK
als
ry
yW
deH
sxMs
ctK
Zw
wM
xCa
te
yI
Ua
wMa
wq
vw
je
a3
fle
c1
K_
Dwu
btg
snt
ue
Ts
Ee
cry
Ss
geF
te
it
zy
KL
vtLa
ej
xp
Gre
cpg
Ft
aw
mpp
ac
$Ti_
Za
c
gc
eCo
wv
Yi
At
ag
ppp
Jw
qw
ze
ig
xq
mtBa
rqw
mq
At
rqt
re
rk
ve
gt
Lja
a2
o.Q~z

--- 字符串字面量 ---
t'E
`L
s`y<H
*ia>"
[':p
!".
#3
y "S
`
`    `  `
``

--- AutoJS API ---

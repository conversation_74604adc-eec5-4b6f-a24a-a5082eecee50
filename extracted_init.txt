=== JavaScript代码提取结果 ===
源文件: bytecode_init.bin
提取时间: Thu Jun 26 14:42:43 CST 2025
JavaScript字符串数量: 703
代码片段数量: 2

=== JavaScript相关字符串 ===
[  0] ls
[  1] *o
[  2] o2
[  3] Nu
[  4] (L
[  5] r{qm
[  6] ttc
[  7] ve
[  8] rD
[  9] .u
[ 10] +L
[ 11] r{qm
[ 12] ttc
[ 13] vt
[ 14] rE\
[ 15] Lj
[ 16] ~aql
[ 17] 1Qb
[ 18] la.
[ 19] #q
[ 20] t.B
[ 21] seNu
[ 22] {q~
[ 23] men
[ 24] sA
[ 25] bu
[ 26] -wu
[ 27] 8l
[ 28] v{
[ 29] p{r
[ 30] ep
[ 31] pe
[ 32] vo
[ 33] rt
[ 34] {~w
[ 35] qt
[ 36] )o
[ 37] o2
[ 38] tab
[ 39] mS
[ 40] ~il
[ 41] a2
[ 42] rW
[ 43] t'E
[ 44] xN
[ 45] Wea
[ 46] mdT
[ 47] qe
[ 48] \n
[ 49] a/
[ 50] i|qM
[ 51] #L
[ 52] r{qm
[ 53] ttc
[ 54] abl
[ 55] ;\
[ 56] qt
[ 57] ry>
[ 58] 2un
[ 59] qu
[ 60] oD
[ 61] Tw
[ 62] I:
[ 63] sv
[ 64] 6sq
[ 65] tR
[ 66] pv
[ 67] \v
[ 68] dd
[ 69] ion
[ 70] r{d
[ 71] vceEvdK
[ 72] }qd
[ 73] als
[ 74] ry
[ 75] yW
[ 76] deH
[ 77] sxMs
[ 78] ctK
[ 79] Zw
[ 80] wM
[ 81] xCa
[ 82] te
[ 83] yI
[ 84] Ua
[ 85] wMa
[ 86] ^a
[ 87] 3lan
[ 88] }a
[ 89] wq
[ 90] vw
[ 91] je
[ 92] 3la
[ 93] o3]t
[ 94] yE\
[ 95] >Lja
[ 96] a3
[ 97] o/m
[ 98] fle
[ 99] c1
[100] K_
[101] 2i
[102] Dwu
[103] btg
[104] snt
[105] b|
[106] [e
[107] ue
[108] 0e
[109] Ts
[110] w|
[111] 1Yr
[112] |e
[113] Ee
[114] T~
[115] cry
[116] Ss
[117] geF
[118] te
[119] 0e
[120] it
[121] a|Kd
[122] `L
[123] k/m
[124] zy
[125] KL
[126] 6pa
[127] vtLa
[128] ej
[129] s`y<H
[130] Tvc
[131] kG
[132] Bu
[133] snv
[134] Cs
[135] ig
[136] |a
[137] rt
[138] Pv
[139] oLBz
[140] }j
[141] nz
[142] T
[143] l
[144] =O
[145] )m
[146] \g
[147] ,k
[148] ic*
[149] !j}
[150] s
[151] k`"
[152] W
'
[153] S:
[154] ;1e
[155] (/*75'
[156] +W
[157] o_1
[158] ',
[159] }?j
[160] i]
[161] TUz
[162] /^Ux
[163] 9g
[164] W
'
[165] '7
[166] ;#4
[167] +S:
[168] ]l'
[169] e!
[170] ==
[171] )S
[172] #U
[173] 1`
[174] ]n'
[175] + &s!=
&
[176] 6?S!
[177] ' "
[178] !g
[179] ;
[180] }?j
[181] i_?
[182] ]`+ "
[183] /s!?
[184] b}<
[185] #a
[186] '
[187] %m
[188] ? &Uj
[189] ; "W/#
[190] k}'
[191] i >7(c
[192] )=
[193] )S
[194] ^s
[195] '
[196] "=
[197] )S
[198] '
[199] +=
[200] )S
[201] #S5
[202] "'}5
[203] b#
[204] 3g
[205] 6sq
[206] tR
[207] pv
[208] uA
[209] si
[210] Vi
[211] uj
[212] {tu
[213] ion
[214] w~
[215] vd
[216] pd
[217] da
[218] Ue
[219] |q
[220] ina
[221] mt
[222] _e
[223] Li
[224] mr
[225] E7
[226] VS %#
[227] H!
[228] ?S8
[229] b=
[230] '
[231] abuS1
[232] $-}
[233] ag
[234] 2g
[235] =#
[236] 4g
[237] ;#
[238] 6g
[239] 8g
[240] '#
[241] :g
[242] ;_
[243] <g
[244] #_
[245] >g
[246] +_
[247] g
[248] dw
[249] }q
[250] )f
[251] [g
[252] a{+
[253] `=
[254] f=
[255] /S
[256] ?UYU
[257] s
7*
-e"
[258] T:
[259] C.
[260] =&
[261] )`
[262] qZ
[263] B7
[264] '
[265] Nw
[266] K+/v#
[267] ?To3p
[268] af
[269] Hu
[270] tA
[271] tio
[272] st
[273] K:
[274] ppp
[275] M;+
[276] ug
[277] 8p
[278] Bu
[279] Zpp
[280] s/*
[281] -f
[282] F
[283] .VS AZ
[284] Yg
[285] k,
[286] Fp
[287] `p
[288] Xf
[289] \a
[290] }t
[291] men
[292] ked
[293] pp
[294] pp
[295] aYiZ!5p
[296] pq
[297] di
[298] mg
[299] }xc
[300] Zj
[301] =y#*
[302] 6F
[303] ,qp
[304] f&
[305] )n
[306] kz!,
[307] nT/
[308] "'}
[309] g.s
[310] anF
[311] qp
[312] \\U
[313] l:
[314] #"?
[315] k}
[316] 7	q
[317] u$
[318] 1P
[319] L<
[320] 1`
[321] ot
[322] w
[323] mt
[324] Qp
[325] he
[326] ox
[327] {|t
[328] pp
[329] #t
[330] sS
[331] 3o
[332] ie
[333] qra
[334] Np
[335] rim
[336] ]xc
[337] Yr
[338] sW
[339] pp
[340] pp
[341] m#*
[342] =0
[343] )gY<
[344] 0t
[345] pp
[346] o1
[347] +/v#
[348] -a>
[349] 0q
[350] pp
[351] Aj
[352] S?
[353] s)k
[354] `f
[355] )S
[356] Y"j
[357] ld^i
[358] _~
[359] wq
[360] 'q
[361] pps
[362] ppw
[363] kz
[364] >p
[365] #q
[366] !q
[367] pps
[368] *p
[369] ppw
[370] kz
[371] >p
[372] #q
[373] !q
[374] pps
[375] *p
[376] ppw
[377] kz
[378] '#.
[379] !j
[380] pp
[381] q
[382] 'q
[383] p%
[384] qL
[385] Jp
[386] hYg
[387] `*:
[388] t;
[389] l5
[390] "
[391] pp
[392] kz
[393] '#
[394] }q
[395] Jp
[396] wa
[397] }xg
[398] ot
[399] *e
[400] qrs
[401] ~sq
[402] U;y
[403] `f
[404] ppp
[405] ]Yg
[406] pp
[407] Jp
[408] 8p
[409] 6u
[410] y?V"
[411] !s
[412] ppw
[413] H*
[414] kz&
[415] C|
[416] ]'4)d
[417] B
[418] 5)k
[419] glo
[420] mp
[421] 6u
[422] pp
[423] 6['0
[424] zw
[425] +/p
[426] >Yg
[427] zw
[428] }q
[429] =j
[430] Xw
[431] ~sq
[432] <t
[433] ";y
[434] ';y
[435] /UU]<
[436] %	PU]<
[437] #.u=
[438] 't
[439] Ir
[440] Nq
[441] Zpp
[442] <w#*
[443] 6l
[444] ; "S
[445] "
[446] w*
[447] lc*
[448] YU
[449] jw
[450] }q
[451] D.
[452] z&
[453] '*
[454] j
[455] i_
[456] 6q
[457] 'q
[458] UiT
[459] pq
[460] sa
[461] ppw
[462] E*
[463] Qz+
[464] z
[465] )S
[466] j
[467] k}+
[468] }<j
[469] i]'7
[470] 0p/
[471] !j
[472] `*
[473] ;S-7"5e
[474] !W
[475] }q
[476] WSZ
[477] m%
[478] j{
[479] }q
[480] 3o
[481] r}+
[482] !j
[483] tif
[484] rW~
[485] Hu
[486] Yr
[487] pp
[488] pp
[489] ".
[490] Yg
[491] t#*
[492] 5
'7
g
[493] &s
[494] =0
[495] ?S8
[496] )n
[497] g_
[498] /s
[499] 4S
[500] YU
[501] !=
[502] 0 *S
[503] X}q
[504] }xc
[505] #q
[506] rt
[507] cru
[508] Ew
[509] $w
[510] }q
[511] oj
[512] .t=
[513] 'q
[514] ra
[515] `i
[516] +co
[517] |e
[518] G9:
[519] ='i
[520] ~y
[521] ='c
[522] vt
[523] C9
[524] )`3B
[525] <f
[526] Yg
[527] ++/s
[528] /m
[529] ;y
[530] !s
[531] rq
[532] `|
[533] }q
[534] D*
[535] WSZi]!;
[536] A#mf#
[537] jn
[538] `]
[539] -	)U
[540] kt$
[541] )S
[542] #Us
[543] '6
[544] )S
[545] 0q
[546] /s
[547] <j
[548] W_3
[549] a
[550] E_6
[551] vu
[552] !`
[553] }q
[554] }q
[555] K&
[556] WY ^
[557] Zq
[558] Cz
[559] ,O
[560] {pT[U
[561] E`B
[562] *p
[563] zp
[564] ge
[565] Yq
[566] en
[567] rc
[568] jw
[569] }q
[570] rv
[571] 'Bw
[572] "'`c
[573] b
[574] +#.s
[575] /s
[576] SS5
[577] [P
[578] '
[579] Be
[580] YW
[581] K~
[582] jU
[583] hh
[584] 3q]
[585] i_
[586] jp
[587] =?
[588] k}
[589] js
[590] !s
[591] 9$3
[592] 5?S000
[593] a}
[594] \U
[595] p
[596] i
[597] P
[598] <j
[599] iP
[600] q+
[601] W
'
[602] i_4
[603] ?p
[604] nec
[605] PI
[606] ~t
[607] ps
[608] ppw
[609] kz!
[610] )S
[611] )gY<
[612] _s
[613] Hu
[614] Nt
[615] '+
[616] ~t
[617] i
[618] ~e
[619] ;?|
[620] l*/
[621] !j
[622] k<
[623] en
[624] pp
[625] =*
[626] [b
[627] 4S9
[628] 5p ^U
[629] '
[630] *W
[631] q}
[632] u-
[633] )S
[634] _
[635] `f
[636] ?n0p
[637] S50
[638] jm
[639] b>
[640] F:
[641] +W
[642] "*
[643] iP
[644] '6
[645] :?S1
[646] p *76+65".
[647] *a{"
[648] k&
[649] +*!w9
[650] +7=&:
[651] +/v#
[652] 7p ^U
[653] ]i+%
[654] .c
[655] S$
[656] .s'9
[657] Y
[658] s
[659] ?s
[660] AG\I
[661] \Qi
[662] Et
[663] ACtYWR
[664] Men
[665] Wp
[666] -s
[667] \s
[668] qq
[669] A
[670] $P
[671] t
[672] c|d
[673] wr]i
[674] -u
[675] mtWe
[676] c|{
[677] y{t
[678] il
[679] q]|
[680] en
[681] Spe
[682] pq
[683] ui
[684] 1aa
[685] d0
[686] #t
[687] rs
[688] wr^i
[689] Bu
[690] snA
[691] i}
[692] ?s
[693] 'P
[694] 0PPp00
[695] }J
[696] t
[697] it
[698] {}}
[699] mo
[700] pq
[701] |t
[702] ~wy

=== 重构的代码片段 ===
// 属性访问: t.B
// 属性访问: g.s

=== 按类别分组 ===

--- 函数和方法 ---

--- 变量和属性 ---
ls
o2
Nu
ttc
ve
rD
ttc
vt
Lj
t.B
seNu
men
sA
bu
ep
pe
vo
rt
qt
o2
tab
mS
a2
rW
xN
Wea
mdT
qe
ttc
abl
qt
qu
oD
Tw
sv
tR
pv
dd
ion
vceEvdK
als
ry
yW
deH
sxMs
ctK
Zw
wM
xCa
te
yI
Ua
wMa
wq
vw
je
a3
fle
c1
K_
Dwu
btg
snt
ue
Ts
Ee
cry
Ss
geF
te
it
zy
KL
vtLa
ej
Tvc
kG
Bu
snv
Cs
ig
rt
Pv
oLBz
nz
T
l
s
o_1
TUz
tR
pv
uA
si
Vi
uj
ion
vd
pd
da
Ue
ina
mt
_e
Li
mr
E7
abuS1
ag
g
dw
qZ
B7
Nw
af
Hu
tA
tio
st
ppp
ug
Bu
Zpp
F
Yg
Fp
Xf
men
ked
pp
pp
pq
di
mg
Zj
g.s
anF
qp
u$
ot
w
mt
Qp
he
ox
pp
sS
ie
qra
Np
rim
Yr
sW
pp
pp
pp
o1
pp
Aj
wq
pps
ppw
kz
pps
ppw
kz
pps
ppw
kz
pp
q
qL
Jp
hYg
l5
pp
kz
Jp
wa
ot
qrs
ppp
pp
Jp
ppw
B
glo
mp
pp
zw
zw
Xw
Ir
Nq
Zpp
YU
jw
j
i_
UiT
pq
sa
ppw
z
j
WSZ
tif
Hu
Yr
pp
pp
Yg
g_
YU
rt
cru
Ew
$w
oj
ra
vt
C9
Yg
rq
jn
kt$
W_3
a
E_6
vu
Zq
Cz
zp
ge
Yq
en
rc
jw
rv
b
SS5
Be
YW
jU
hh
i_
jp
js
p
i
P
iP
i_4
nec
PI
ps
ppw
_s
Hu
Nt
i
en
pp
_
S50
jm
iP
S$
Y
s
Et
ACtYWR
Men
Wp
qq
A
$P
t
mtWe
il
en
Spe
pq
ui
d0
rs
Bu
snA
t
it
mo
pq

--- 字符串字面量 ---
t'E
`L
s`y<H
k`"
W
'
(/*75'
',
W
'
'7
]l'
1`
]n'
' "
]`+ "
'
; "W/#
k}'
'
"=
'
"'}5
'
'#
`=
s
7*
-e"
)`
'
`p
"'}
#"?
1`
`f
Y"j
'q
'#.
'q
`*:
"
'#
`f
y?V"
]'4)d
6['0
";y
';y
't
; "S
"
'*
'q
i]'7
`*
;S-7"5e
".
5
'7
g
'q
`i
='i
='c
)`3B
`|
`]
'6
!`
E`B
'Bw
"'`c
'
W
'
'+
'
`f
"*
'6
p *76+65".
*a{"
.s'9
'P

--- AutoJS API ---

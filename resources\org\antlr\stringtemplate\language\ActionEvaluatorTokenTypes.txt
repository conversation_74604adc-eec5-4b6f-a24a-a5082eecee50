// $ANTLR 2.7.7 (20060906): eval.g -> ActionEvaluatorTokenTypes.txt$
ActionEvaluator    // output token vocab name
APPLY=4
MULTI_APPLY=5
ARGS=6
INCLUDE=7
CONDITIONAL="if"=8
VALUE=9
TEMPLATE=10
FUNCTION=11
SINGLEVALUEARG=12
LIST=13
NOTHING=14
SEMI=15
LPAREN=16
RPAREN=17
LITERAL_elseif="elseif"=18
COMMA=19
ID=20
ASSIGN=21
COLON=22
NOT=23
PLUS=24
DOT=25
LITERAL_first="first"=26
LITERAL_rest="rest"=27
LITERAL_last="last"=28
LITERAL_length="length"=29
LITERAL_strip="strip"=30
LITERAL_trunc="trunc"=31
LITERAL_super="super"=32
ANONYMOUS_TEMPLATE=33
STRING=34
INT=35
LBRACK=36
RBRACK=37
DOTDOTDOT=38
TEMPLATE_ARGS=39
NESTED_ANONYMOUS_TEMPLATE=40
ESC_CHAR=41
WS=42
WS_CHAR=43

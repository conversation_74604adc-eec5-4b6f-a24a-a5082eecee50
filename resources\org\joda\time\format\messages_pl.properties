PeriodFormat.space=\ 
PeriodFormat.comma=,
PeriodFormat.commandand=,i 
PeriodFormat.commaspaceand=, i 
PeriodFormat.commaspace=, 
PeriodFormat.spaceandspace=\ i 
PeriodFormat.regex.separator=%
PeriodFormat.years.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.years.list=\ rok%\ lata%\ lat
PeriodFormat.months.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.months.list=\ miesi\u0105c%\ miesi\u0105ce%\ miesi\u0119cy
PeriodFormat.weeks.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.weeks.list=\ tydzie\u0144%\ tygodnie%\ tygodni
PeriodFormat.day=\ dzie\u0144
PeriodFormat.days=\ dni
#For reference:
#PeriodFormat.days.regex=^1$%[0-9]*
#PeriodFormat.days.list=\ dzie\u0144%\ dni
PeriodFormat.hours.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.hours.list=\ godzina%\ godziny%\ godzin
PeriodFormat.minutes.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.minutes.list=\ minuta%\ minuty%\ minut
PeriodFormat.seconds.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.seconds.list=\ sekunda%\ sekundy%\ sekund
PeriodFormat.milliseconds.regex=^1$%[0-9]*(?<!1)[2-4]$%[0-9]*
PeriodFormat.milliseconds.list=\ milisekunda%\ milisekundy%\ milisekund

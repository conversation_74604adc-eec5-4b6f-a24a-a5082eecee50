#
# Default JavaScript messages file.
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# This is replaced during jar assembly from property string
# and should not be translated
implementation.version = @IMPLEMENTATION.VERSION@

#
# To add JavaScript error messages for a particular locale, create a
# new Messages_[locale].properties file, where [locale] is the Java
# string abbreviation for that locale.  For example, JavaScript
# messages for the Polish locale should be located in
# Messages_pl.properties, and messages for the Italian Swiss locale
# should be located in Messages_it_CH.properties.  Message properties
# files should be accessible through the classpath under
# org.mozilla.javascript.resources
#
# See:
# java.util.ResourceBundle
# java.text.MessageFormat
#

# SomeJavaClassWhereUsed

params.omit.non.js.object.warning = true

msg.non.js.object.warning =\
    RHINO USAGE WARNING: Missed Context.javaToJS() conversion: Rhino runtime detected object "{0}" of class "{1}" where it expected String, Number, Boolean or Scriptable instance. Please check your code for missing Context.javaToJS() call.

# Codegen
msg.dup.parms =\
    Duplicate parameter name "{0}".

msg.too.big.jump =\
    Program too complex: too big jump offset.

msg.too.big.index =\
    Program too complex: internal index exceeds 64K limit.

msg.while.compiling.fn =\
    Encountered code generation error while compiling function "{0}": {1}

msg.while.compiling.script =\
    Encountered code generation error while compiling script: {0}

# Context
msg.ctor.not.found =\
    Constructor for "{0}" not found.

msg.not.ctor =\
    "{0}" is not a constructor.

# FunctionObject
msg.varargs.ctor =\
    Method or constructor "{0}" must be static with the signature \
    "(Context cx, Object[] args, Function ctorObj, boolean inNewExpr)" \
    to define a variable arguments constructor.

msg.varargs.fun =\
    Method "{0}" must be static with the signature \
    "(Context cx, Scriptable thisObj, Object[] args, Function funObj)" \
    to define a variable arguments function.

msg.incompat.call =\
    Method "{0}" called on incompatible object.

msg.incompat.call.details =\
    Method "{0}" called on incompatible object ({1} is not an instance of {2}).

msg.bad.parms =\
    Unsupported parameter type "{0}" in method "{1}".

msg.bad.method.return =\
    Unsupported return type "{0}" in method "{1}".

msg.bad.ctor.return =\
    Construction of objects of type "{0}" is not supported.

msg.no.overload =\
    Method "{0}" occurs multiple times in class "{1}".

msg.method.not.found =\
    Method "{0}" not found in "{1}".

msg.method.missing.parameter =\
    {0}: At least {1} arguments required, but only {2} passed

# IRFactory

msg.bad.for.in.lhs =\
    Invalid left-hand side of for..in loop.

msg.mult.index =\
    Only one variable allowed in for..in loop.

msg.bad.for.in.destruct =\
    Left hand side of for..in loop must be an array of length 2 to accept \
    key/value pair.
    
msg.cant.convert =\
    Can''t convert to type "{0}".

msg.bad.assign.left =\
    Invalid assignment left-hand side.

msg.bad.decr =\
    Invalid decrement operand.

msg.bad.incr =\
    Invalid increment operand.

msg.bad.yield =\
    yield must be in a function.

msg.yield.parenthesized =\
    yield expression must be parenthesized.

# NativeGlobal
msg.cant.call.indirect =\
    Function "{0}" must be called directly, and not by way of a \
    function of another name.

msg.eval.nonstring =\
    Calling eval() with anything other than a primitive string value will \
    simply return the value. Is this what you intended?

msg.eval.nonstring.strict =\
    Calling eval() with anything other than a primitive string value is not \
    allowed in strict mode.

msg.bad.destruct.op =\
    Invalid destructuring assignment operator

# NativeCall
msg.only.from.new =\
    "Constructor {0}" may only be invoked from a "new" expression.

msg.deprec.ctor =\
    The "{0}" constructor is deprecated.

# NativeFunction
msg.no.function.ref.found =\
    no source found to decompile function reference {0}

msg.arg.isnt.array =\
    second argument to Function.prototype.apply must be an array

# NativeGlobal
msg.bad.esc.mask =\
    invalid string escape mask

# NativeJavaClass
msg.cant.instantiate =\
    error instantiating ({0}): class {1} is interface or abstract

msg.bad.ctor.sig =\
    Found constructor with wrong signature: \
    {0} calling {1} with signature {2}

msg.not.java.obj =\
    Expected argument to getClass() to be a Java object.

msg.no.java.ctor =\
    Java constructor for "{0}" with arguments "{1}" not found.

# NativeJavaMethod
msg.method.ambiguous =\
    The choice of Java method {0}.{1} matching JavaScript argument types ({2}) is ambiguous; \
    candidate methods are: {3}

msg.constructor.ambiguous =\
    The choice of Java constructor {0} matching JavaScript argument types ({1}) is ambiguous; \
    candidate constructors are: {2}

# NativeJavaObject
msg.conversion.not.allowed =\
    Cannot convert {0} to {1}

msg.no.empty.interface.conversion =\
    Cannot convert function to interface {0} with no methods

msg.no.function.interface.conversion =\
    Cannot convert function to interface {0} since it contains methods with \
    different names

msg.undefined.function.interface =\
    Property "{0}" is not defined in interface adapter

msg.not.function.interface =\
    Property "{0}" is not a function in interface adapter

# NativeJavaPackage
msg.not.classloader =\
    Constructor for "Packages" expects argument of type java.lang.Classloader

# NativeRegExp
msg.bad.quant =\
    Invalid quantifier {0}

msg.overlarge.backref =\
    Overly large back reference {0}

msg.overlarge.min =\
    Overly large minimum {0}

msg.overlarge.max =\
    Overly large maximum {0}

msg.zero.quant =\
    Zero quantifier {0}

msg.max.lt.min =\
    Maximum {0} less than minimum

msg.unterm.quant =\
    Unterminated quantifier {0}

msg.unterm.paren =\
    Unterminated parenthetical {0}

msg.unterm.class =\
    Unterminated character class {0}

msg.bad.range =\
    Invalid range in character class.

msg.trail.backslash =\
    Trailing \\ in regular expression.

msg.re.unmatched.right.paren =\
    unmatched ) in regular expression.

msg.no.regexp =\
    Regular expressions are not available.

msg.bad.backref =\
    back-reference exceeds number of capturing parentheses.

msg.bad.regexp.compile =\
    Only one argument may be specified if the first argument to \
    RegExp.prototype.compile is a RegExp object.

msg.arg.not.object =\
    Expected argument of type object, but instead had type {0}

# NativeDate
msg.invalid.date =\
    Date is invalid.

msg.toisostring.must.return.primitive =\
    toISOString must return a primitive value, but instead returned "{0}"

# NativeJSON
msg.json.cant.serialize =\
    Do not know how to serialize a {0}

# Parser
msg.got.syntax.errors = \
    Compilation produced {0} syntax errors.

msg.var.redecl =\
    TypeError: redeclaration of var {0}.

msg.const.redecl =\
    TypeError: redeclaration of const {0}.
    
msg.let.redecl =\
    TypeError: redeclaration of variable {0}.

msg.parm.redecl =\
    TypeError: redeclaration of formal parameter {0}.

msg.fn.redecl =\
    TypeError: redeclaration of function {0}.

msg.let.decl.not.in.block =\
    SyntaxError: let declaration not directly within block

msg.bad.object.init =\
    SyntaxError: invalid object initializer

# NodeTransformer
msg.dup.label =\
    duplicated label

msg.undef.label =\
    undefined label

msg.bad.break =\
    unlabelled break must be inside loop or switch

msg.continue.outside =\
    continue must be inside loop

msg.continue.nonloop =\
    continue can only use labeles of iteration statements

msg.bad.throw.eol =\
    Line terminator is not allowed between the throw keyword and throw \
    expression.

msg.no.paren.parms =\
    missing ( before function parameters.

msg.no.parm =\
    missing formal parameter

msg.no.paren.after.parms =\
    missing ) after formal parameters

msg.no.brace.body =\
    missing '{' before function body

msg.no.brace.after.body =\
    missing } after function body

msg.no.paren.cond =\
    missing ( before condition

msg.no.paren.after.cond =\
    missing ) after condition

msg.no.semi.stmt =\
    missing ; before statement

msg.missing.semi =\
    missing ; after statement

msg.no.name.after.dot =\
    missing name after . operator

msg.no.name.after.coloncolon =\
    missing name after :: operator

msg.no.name.after.dotdot =\
    missing name after .. operator

msg.no.name.after.xmlAttr =\
    missing name after .@

msg.no.bracket.index =\
    missing ] in index expression

msg.no.paren.switch =\
    missing ( before switch expression

msg.no.paren.after.switch =\
    missing ) after switch expression

msg.no.brace.switch =\
    missing '{' before switch body

msg.bad.switch =\
    invalid switch statement

msg.no.colon.case =\
    missing : after case expression

msg.double.switch.default =\
    double default label in the switch statement

msg.no.while.do =\
    missing while after do-loop body

msg.no.paren.for =\
    missing ( after for

msg.no.semi.for =\
    missing ; after for-loop initializer

msg.no.semi.for.cond =\
    missing ; after for-loop condition
    
msg.in.after.for.name =\
    missing in after for

msg.no.paren.for.ctrl =\
    missing ) after for-loop control

msg.no.paren.with =\
    missing ( before with-statement object

msg.no.paren.after.with =\
    missing ) after with-statement object
    
msg.no.with.strict =\
    with statements not allowed in strict mode

msg.no.paren.after.let =\
    missing ( after let

msg.no.paren.let =\
    missing ) after variable list

msg.no.curly.let =\
    missing } after let statement

msg.bad.return =\
    invalid return

msg.no.brace.block =\
    missing } in compound statement

msg.bad.label =\
    invalid label

msg.bad.var =\
    missing variable name

msg.bad.var.init =\
    invalid variable initialization

msg.no.colon.cond =\
    missing : in conditional expression

msg.no.paren.arg =\
    missing ) after argument list

msg.no.bracket.arg =\
    missing ] after element list

msg.bad.prop =\
    invalid property id

msg.no.colon.prop =\
    missing : after property id

msg.no.brace.prop =\
    missing } after property list

msg.no.paren =\
    missing ) in parenthetical

msg.reserved.id =\
    identifier is a reserved word: {0}

msg.no.paren.catch =\
    missing ( before catch-block condition

msg.bad.catchcond =\
    invalid catch block condition

msg.catch.unreachable =\
    any catch clauses following an unqualified catch are unreachable

msg.no.brace.try =\
    missing '{' before try block

msg.no.brace.catchblock =\
    missing '{' before catch-block body

msg.try.no.catchfinally =\
    ''try'' without ''catch'' or ''finally''

msg.no.return.value =\
  function {0} does not always return a value

msg.anon.no.return.value =\
  anonymous function does not always return a value

msg.return.inconsistent =\
  return statement is inconsistent with previous usage

msg.generator.returns =\
  TypeError: generator function {0} returns a value

msg.anon.generator.returns =\
  TypeError: anonymous generator function returns a value

msg.syntax =\
    syntax error

msg.unexpected.eof =\
    Unexpected end of file

msg.XML.bad.form =\
    illegally formed XML syntax

msg.XML.not.available =\
    XML runtime not available

msg.too.deep.parser.recursion =\
    Too deep recursion while parsing

msg.too.many.constructor.args =\
    Too many constructor arguments

msg.too.many.function.args =\
    Too many function arguments

msg.no.side.effects =\
    Code has no side effects

msg.extra.trailing.semi =\
    Extraneous trailing semicolon

msg.extra.trailing.comma =\
    Trailing comma is not legal in an ECMA-262 object initializer

msg.trailing.array.comma =\
    Trailing comma in array literal has different cross-browser behavior

msg.equal.as.assign =\
    Test for equality (==) mistyped as assignment (=)?

msg.var.hides.arg =\
    Variable {0} hides argument

msg.destruct.assign.no.init =\
    Missing = in destructuring declaration

msg.destruct.default.vals =\
    Default values in destructuring declarations are not supported

msg.no.old.octal.strict =\
    Old octal numbers prohibited in strict mode.

msg.no.old.octal.bigint =\
    Old octal numbers prohibited in BigInt.

msg.dup.obj.lit.prop.strict =\
    Property "{0}" already defined in this object literal.

msg.dup.param.strict =\
    Parameter "{0}" already declared in this function.

msg.bad.id.strict =\
    "{0}" is not a valid identifier for this use in strict mode.

msg.no.unary.expr.on.left.exp =\
    "{0}" is not allowed on the left-hand side of "**".

# ScriptRuntime

# is there a better message for this? 
# it's currently only used as a poison pill for caller, caller and arguments properties
msg.op.not.allowed =\
    This operation is not allowed.

msg.no.properties =\
    {0} has no properties.

msg.invalid.iterator =\
    Invalid iterator value

msg.iterator.primitive =\
    __iterator__ returned a primitive value

msg.not.iterable = \
    {0} is not iterable

msg.invalid.for.each = \
    invalid for each loop

msg.assn.create.strict =\
    Assignment to undeclared variable {0}

msg.ref.undefined.prop =\
    Reference to undefined property "{0}"

msg.prop.not.found =\
    Property {0} not found.

msg.set.prop.no.setter =\
    Cannot set property {0} that has only a getter to value ''{1}''.

msg.invalid.type =\
    Invalid JavaScript value of type {0}

msg.primitive.expected =\
    Primitive type expected (had {0} instead)

msg.namespace.expected =\
    Namespace object expected to left of :: (found {0} instead)

msg.null.to.object =\
    Cannot convert null to an object.

msg.undef.to.object =\
    Cannot convert undefined to an object.

msg.cant.convert.to.bigint =\
    Cannot convert {0} to an BigInt.

msg.cant.convert.to.bigint.isnt.integer =\
    Cannot convert {0} to an BigInt. It isn\'t an integer.

msg.bigint.bad.form =\
    illegally formed BigInt syntax

msg.cyclic.value =\
    Cyclic {0} value not allowed.

msg.is.not.defined =\
    "{0}" is not defined.

msg.undef.prop.read =\
    Cannot read property "{1}" from {0}

msg.undef.prop.write =\
    Cannot set property "{1}" of {0} to "{2}"

msg.undef.prop.delete =\
    Cannot delete property "{1}" of {0}

msg.undef.method.call =\
    Cannot call method "{1}" of {0}

msg.undef.with =\
    Cannot apply "with" to {0}

msg.isnt.function =\
    {0} is not a function, it is {1}.

msg.isnt.function.in =\
    Cannot call property {0} in object {1}. It is not a function, it is "{2}".

msg.function.not.found =\
    Cannot find function {0}.

msg.function.not.found.in =\
    Cannot find function {0} in object {1}.

msg.isnt.xml.object =\
    {0} is not an xml object.

msg.no.ref.to.get =\
    {0} is not a reference to read reference value.

msg.no.ref.to.set =\
    {0} is not a reference to set reference value to {1}.

msg.no.ref.from.function =\
    Function {0} can not be used as the left-hand side of assignment \
    or as an operand of ++ or -- operator.

msg.bad.default.value =\
    Object''s getDefaultValue() method returned an object.

msg.instanceof.not.object = \
    Can''t use ''instanceof'' on a non-object.

msg.instanceof.bad.prototype = \
    ''prototype'' property of {0} is not an object.

msg.in.not.object = \
    Can''t use ''in'' on a non-object.

msg.bad.radix = \
    illegal radix {0}.

msg.division.zero = \
    Division by zero.

msg.bigint.negative.exponent = \
    BigInt negative exponent.

msg.bigint.out.of.range.arithmetic = \
    BigInt is too large.

# ScriptableObject
msg.default.value =\
    Cannot find default value for object.

msg.zero.arg.ctor =\
    Cannot load class "{0}" which has no zero-parameter constructor.

duplicate.defineClass.name =\
    Invalid method "{0}": name "{1}" is already in use.

msg.ctor.multiple.parms =\
    Can''t define constructor or class {0} since more than one \
    constructor has multiple parameters.

msg.extend.scriptable =\
    {0} must extend ScriptableObject in order to define property {1}.

msg.bad.getter.parms =\
    In order to define a property, getter {0} must have zero parameters \
    or a single ScriptableObject parameter.

msg.obj.getter.parms =\
    Expected static or delegated getter {0} to take a ScriptableObject parameter.

msg.getter.static =\
    Getter and setter must both be static or neither be static.

msg.setter.return =\
    Setter must have void return type: {0}

msg.setter2.parms =\
    Two-parameter setter must take a ScriptableObject as its first parameter.

msg.setter1.parms =\
    Expected single parameter setter for {0}

msg.setter2.expected =\
    Expected static or delegated setter {0} to take two parameters.

msg.setter.parms =\
    Expected either one or two parameters for setter.

msg.setter.bad.type =\
    Unsupported parameter type "{0}" in setter "{1}".

msg.add.sealed =\
    Cannot add a property to a sealed object: {0}.

msg.remove.sealed =\
    Cannot remove a property from a sealed object: {0}.

msg.modify.sealed =\
    Cannot modify a property of a sealed object: {0}.

msg.modify.readonly =\
    Cannot modify readonly property: {0}.

msg.both.data.and.accessor.desc =\
    Cannot be both a data and an accessor descriptor.

msg.change.configurable.false.to.true =\
    Cannot change the configurable attribute of "{0}" from false to true.

msg.change.enumerable.with.configurable.false =\
    Cannot change the enumerable attribute of "{0}" because configurable is false.

msg.change.writable.false.to.true.with.configurable.false =\
    Cannot change the writable attribute of "{0}" from false to true because configurable is false.

msg.change.value.with.writable.false =\
    Cannot change the value of attribute "{0}" because writable is false.

msg.change.getter.with.configurable.false =\
    Cannot change the get attribute of "{0}" because configurable is false.

msg.change.setter.with.configurable.false =\
    Cannot change the set attribute of "{0}" because configurable is false.

msg.change.property.data.to.accessor.with.configurable.false =\
    Cannot change "{0}" from a data property to an accessor property because configurable is false.

msg.change.property.accessor.to.data.with.configurable.false =\
    Cannot change "{0}" from an accessor property to a data property because configurable is false.

msg.not.extensible =\
    Cannot add properties to this object because extensible is false.

msg.delete.property.with.configurable.false =\
    Cannot delete "{0}" property because configurable is false.

# TokenStream
msg.missing.exponent =\
    missing exponent

msg.caught.nfe =\
    number format error

msg.unterminated.string.lit =\
    unterminated string literal

msg.unterminated.comment =\
    unterminated comment

msg.unterminated.re.lit =\
    unterminated regular expression literal

msg.invalid.re.flag =\
    invalid flag after regular expression

msg.no.re.input.for =\
    no input for {0}

msg.illegal.character =\
    illegal character: {0}

msg.invalid.escape =\
    invalid Unicode escape sequence

msg.bad.namespace =\
    not a valid default namespace statement. \
    Syntax is: default xml namespace = EXPRESSION;

# TokensStream warnings
msg.bad.octal.literal =\
    illegal octal literal digit {0}; interpreting it as a decimal digit

msg.reserved.keyword =\
    illegal usage of future reserved keyword {0}; interpreting it as ordinary identifier

# LiveConnect errors
msg.java.internal.field.type =\
    Internal error: type conversion of {0} to assign to {1} on {2} failed.

msg.java.conversion.implicit_method =\
    Can''t find converter method "{0}" on class {1}.

msg.java.method.assign =\
    Java method "{0}" cannot be assigned to.

msg.java.internal.private =\
    Internal error: attempt to access private/protected field "{0}".

msg.java.no_such_method =\
    Can''t find method {0}.

msg.script.is.not.constructor =\
    Script objects are not constructors.

msg.nonjava.method =\
    Java method "{0}" was invoked with {1} as "this" value that can not be converted to Java type {2}.

msg.java.member.not.found =\
    Java class "{0}" has no public instance field or method named "{1}".

msg.java.array.index.out.of.bounds =\
    Array index {0} is out of bounds [0..{1}].

msg.java.array.member.not.found =\
    Java arrays have no public instance fields or methods named "{0}".

msg.pkg.int =\
    Java package names may not be numbers.

msg.access.prohibited =\
    Access to Java class "{0}" is prohibited.

# ImporterTopLevel
msg.ambig.import =\
    Ambiguous import: "{0}" and and "{1}".

msg.not.pkg =\
    Function importPackage must be called with a package; had "{0}" instead.

msg.not.class =\
    Function importClass must be called with a class; had "{0}" instead.

msg.not.class.not.pkg =\
    "{0}" is neither a class nor a package.

msg.prop.defined =\
    Cannot import "{0}" since a property by that name is already defined.

#JavaAdapter
msg.adapter.zero.args =\
    JavaAdapter requires at least one argument.

msg.not.java.class.arg = \
Argument {0} is not Java class: {1}.

#JavaAdapter
msg.only.one.super = \
Only one class may be extended by a JavaAdapter. Had {0} and {1}.


# Arrays
msg.arraylength.bad =\
    Inappropriate array length.

# Arrays
msg.arraylength.too.big =\
    Array length {0} exceeds supported capacity limit.

msg.empty.array.reduce =\
    Reduce of empty array with no initial value

# URI
msg.bad.uri =\
    Malformed URI sequence.

# Number
msg.bad.precision =\
    Precision {0} out of range.

# NativeGenerator
msg.send.newborn =\
  Attempt to send value to newborn generator

msg.already.exec.gen =\
    Already executing generator
    
msg.StopIteration.invalid =\
    StopIteration may not be changed to an arbitrary object.

msg.generator.executing =\
    The generator is still executing from a previous invocation.

# Interpreter
msg.yield.closing =\
  Yield from closing generator

msg.called.null.or.undefined=\
  {0}.prototype.{1} method called on null or undefined

msg.first.arg.not.regexp=\
  First argument to {0}.prototype.{1} must not be a regular expression

msg.arrowfunction.generator =\
  arrow function can not become generator

# Arguments
msg.arguments.not.access.strict =\
  Cannot access "{0}" property of the arguments object in strict mode.

msg.object.cyclic.prototype =\
  Cyclic prototype "{0}" value not allowed.

# Symbol support
msg.object.not.symbolscriptable =\
  Object {0} does not support Symbol keys

msg.no.assign.symbol.strict =\
  Symbol objects may not be assigned properties in strict mode

msg.not.a.string =\
  The object is not a string

msg.not.a.number =\
  The object is not a number

msg.cant.convert.to.number =\
  Cannot convert {0} to a number

msg.no.symbol.new =\
  Symbol objects may not be constructed using \"new\"

msg.compare.symbol =\
  Symbol objects may not be compared

msg.no.new =\
  {0} objects may not be constructed using \"new\"

msg.map.function.not =\
  Map function is not actually a function

msg.constructor.no.function \=
  The constructor for {0} may not be invoked as a function

msg.this.not.instance \= \"this\" is not an instance of the class

# Promises
msg.function.expected =\
  Expecting the first argument to be a function

msg.constructor.expected =\
  Expecting the first argument to be a constructor

msg.promise.self.resolution =\
  Promise is being self-resolved

msg.promise.capability.state \=
Invalid promise capability state

msg.promise.all.toobig \=
  Too many inputs to Promise.all
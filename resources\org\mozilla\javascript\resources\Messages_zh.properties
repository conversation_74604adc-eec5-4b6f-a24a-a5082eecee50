#
# Default JavaScript messages file.
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# This is replaced during jar assembly from property string
# and should not be translated
implementation.version = @IMPLEMENTATION.VERSION@

#
# To add JavaScript error messages for a particular locale, create a
# new Messages_[locale].properties file, where [locale] is the Java
# string abbreviation for that locale.  For example, JavaScript
# messages for the Polish locale should be located in
# Messages_pl.properties, and messages for the Italian Swiss locale
# should be located in Messages_it_CH.properties.  Message properties
# files should be accessible through the classpath under
# org.mozilla.javascript.resources
#
# See:
# java.util.ResourceBundle
# java.text.MessageFormat
#

# SomeJavaClassWhereUsed

params.omit.non.js.object.warning = true

msg.non.js.object.warning =\
    RHINO USAGE WARNING: Missed Context.javaToJS() conversion: Rhino runtime detected object "{0}" of class "{1}" where it expected String, Number, Boolean or Scriptable instance. Please check your code for missing Context.javaToJS() call.

# Codegen
msg.dup.parms = \
    参数名称重复"{0}".

msg.too.big.jump = \
    程序太复杂：跳转偏移量太大.

msg.too.big.index = \
    程序太复杂：内部索引超过64K限制.

msg.while.compiling.fn = \
    编译函数"{0}"时遇到代码生成错误：{1}

msg.while.compiling.script = \
    编译脚本时遇到代码生成错误：{0}

# 上下文
msg.ctor.not.found = \
    找不到"{0}"的构造函数.

msg.not.ctor = \
    "{0}"不是构造函数.

# FunctionObject
msg.varargs.ctor = \
    方法或构造函数"{0}"必须是静态的，签名为\
    "(上下文cx，对象[]参数，函数ctorObj，布尔inNewExpr)" \
    定义变量参数构造函数.

msg.varargs.fun = \
    方法"{0}"必须是静态的，签名为\
    "(上下文cx，可编写脚本的thisObj，Object []参数，函数funObj)" \
    定义变量参数函数.

msg.incompat.call = \
    方法"{0}"在不兼容的对象上调用.

msg.bad.parms = \
    方法"{1}"中不支持的参数类型"{0}".

msg.bad.method.return = \
    方法"{1}"中不支持的返回类型"{0}".

msg.bad.ctor.return = \
    不支持构造类型"{0}"的对象.

msg.no.overload = \
    方法"{0}"在类"{1}"中多次出现.

msg.method.not.found = \
    在"{1}"中找不到方法"{0}".

# IRFactory

msg.bad.for.in.lhs = \
    for..in循环的左侧无效.

msg.mult.index = \
    for..in循环中仅允许使用一个变量.

msg.bad.for.in.destruct = \
    for..in循环的左侧必须是长度为2的数组才能接受\
    键/值对.

msg.cant.convert = \
    无法转换为类型"{0}".

msg.bad.assign.left = \
    左侧的分配无效.

msg.bad.decr = \
    无效的减量操作数.

msg.bad.incr = \
    无效的增量操作数.

msg.bad.yield = \
    yield必须在函数中.

msg.yield.parenthesized = \
    yield表达式必须带括号.

# NativeGlobal
msg.cant.call.indirect = \
    必须直接调用函数"{0}"，而不是通过\
    另一个功能.

msg.eval.nonstring = \
    使用除原始字符串值以外的任何值调用eval()都会\
    只需返回值.这是您想要的吗？

msg.eval.nonstring.strict = \
    用除原始字符串值以外的任何值调用eval()都不是\
    在严格模式下允许.

msg.bad.destruct.op = \
    无效的解构赋值运算符

# NativeCall
msg.only.from.new = \
    只能从"新"表达式中调用"{0}".

msg.deprec.ctor = \
    "{0}"构造函数已弃用.

# NativeFunction
msg.no.function.ref.found = \
    找不到反编译功能引用{0}的源

msg.arg.isnt.array = \
    Function.prototype.apply的第二个参数必须是一个数组

# NativeGlobal
msg.bad.esc.mask = \
    无效的字符串转义掩码

# NativeJavaClass
msg.cant.instantiate = \
    错误实例化({0})：类{1}是接口或抽象

msg.bad.ctor.sig = \
    找到签名错误的构造函数：
    {0}呼叫签名为{2}的{1}

msg.not.java.obj = \
    getClass()的预期参数是Java对象.

msg.no.java.ctor = \
    找不到带有参数"{1}"的"{0}"的Java构造函数.

# NativeJavaMethod
msg.method.ambiguous = \
    匹配JavaScript参数类型({2})的Java方法{0}.{1}的选择不明确; \
    候选方法是：{3}

msg.constructor.ambiguous = \
    匹配JavaScript参数类型({1})的Java构造函数{0}的选择不明确; \
    候选构造函数为：{2}

# NativeJavaObject
msg.conversion.not.allowed = \
    无法将{0}转换为{1}

msg.no.empty.interface.conversion = \
    不能将函数转换到没有任何方法的接口{0}

msg.no.function.interface.conversion = \
    无法将函数转换为接口{0}，因为它包含带有\的方法
    不同的名字

msg.undefined.function.interface = \
    在接口适配器中未定义属性"{0}"

msg.not.function.interface = \
    属性"{0}"在接口适配器中不是功能

# NativeJavaPackage
msg.not.classloader = \
    "包装"的构造函数期望参数类型为java.lang.Classloader

# NativeRegExp
msg.bad.quant = \
    无效的量词{0}

msg.overlarge.backref = \
    反向引用过大{0}

msg.overlarge.min = \
    最小值{0}太大

msg.overlarge.max = \
    最大值{0}太大

msg.zero.quant = \
    零量词{0}

msg.max.lt.min = \
    最大值{0}小于最小值

msg.unterm.quant = \
    未终止的量词{0}

msg.unterm.paren = \
    圆括号{0}

msg.unterm.class = \
    未终止的字符类{0}

msg.bad.range = \
    字符类中的范围无效.

msg.trail.backslash = \
    在正则表达式中尾随\\.

msg.re.unmatched.right.paren = \
    正则表达式中不匹配).

msg.no.regexp = \
    正则表达式不可用.

msg.bad.backref = \
    向后引用超过了捕获括号的数量.

msg.bad.regexp.compile = \
    如果\的第一个参数只能指定一个参数
    RegExp.prototype.compile是一个RegExp对象.

msg.arg.not.object = \
    类型为object的预期参数，但类型为{0}

# NativeDate
msg.invalid.date = \
    日期无效.

msg.toisostring.must.return.primitive = \
    toISOString必须返回原始值，但返回"{0}"

# 解析器
msg.got.syntax.errors = \
    编译产生了{0}语法错误.

msg.var.redecl = \
    TypeError：重新声明var{0}.

msg.const.redecl = \
    TypeError：重新声明常量{0}.

msg.let.redecl = \
    TypeError：重新声明变量{0}.

msg.parm.redecl = \
TypeError：重新声明形式参数{0}.

msg.fn.redecl = \
    TypeError：函数{0}的重新声明.

msg.let.decl.not.in.block = \
    SyntaxError：让声明不直接在块内

msg.bad.object.init = \
    语法错误：无效的对象初始化

# NodeTransformer
msg.dup.label = \
    标签重复

msg.undef.label = \
    未定义标签

msg.bad.break = \
    未标记的中断必须在循环或开关内

msg.continue.outside = \
    继续必须在循环内

msg.continue.nonloop = \
    继续只能使用迭代语句的标签

msg.bad.throw.eol = \
    throw关键字和throw \之间不允许使用行终止符
    表达.

msg.no.paren.parms = \
    缺少(在函数参数之前.

msg.no.parm = \
    缺少形式参数

msg.no.paren.after.parms = \
    缺少)形式参数后

msg.no.brace.body = \
    在函数体之前缺少"{"

msg.no.brace.after.body = \
    在函数体之后丢失

msg.no.paren.cond = \
    失踪(在条件之前

msg.no.paren.after.cond = \
    缺少)条件后

msg.no.semi.stmt = \
    失踪 ;声明前
msg.missing.semi = \
    失踪 ;声明后

msg.no.name.after.dot = \
    之后缺少姓名.算子

msg.no.name.after.冒号= \
    ::运算符后缺少名称

msg.no.name.after.dotdot = \
    ..运算符后缺少名称

msg.no.name.after.xmlAttr = \
    .@之后缺少名称

msg.no.bracket.index = \
    索引表达式中缺少]

msg.no.paren.switch = \
    缺少(在开关表达式之前

msg.no.paren.after.switch = \
    开关表达式后缺少)

msg.no.brace.switch = \
    开关主体前缺少'{'

msg.bad.switch = \
    无效的switch语句

msg.no.colon.case = \
    缺少：案例表达之后

msg.double.switch.default = \
    switch语句中的double default标签

msg.no.while.do = \
    做完循环后身体不见了

msg.no.paren.for = \
    缺少(后为

msg.no.semi.for = \
    失踪 ;在for循环初始化器之后

msg.no.semi.for.cond = \
    失踪 ; for循环后

msg.in.after.for.name = \
    在之后缺少

msg.no.paren.for.ctrl = \
    缺少)for循环控制后

msg.no.paren.with = \
    缺少(带有声明对象之前

msg.no.paren.after.with = \
    声明语句后缺少)

msg.no.with.strict = \
    严格模式下不允许使用语句

msg.no.paren.after.let = \
    失踪(让后

msg.no.paren.let = \
    在变量列表后缺少)

msg.no.curly.let = \
    let语句后缺少}

msg.bad.return = \
    无效的回报

msg.no.brace.block = \
    复合语句中缺少}

msg.bad.label = \
    标签无效

msg.bad.var = \
    缺少变量名

msg.bad.var.init = \
    无效的变量初始化

msg.no.colon.cond = \
    缺少：在条件表达式中

msg.no.paren.arg = \
    缺少参数列表后的)

msg.no.bracket.arg = \
    元素列表后缺少]

msg.bad.prop = \
    无效的属性ID

msg.no.colon.prop = \
    缺少：属性ID之后

msg.no.brace.prop = \
    属性列表后缺少}

msg.no.paren = \
    括号中的)

msg.reserved.id = \
    标识符是保留字

msg.no.paren.catch = \
    丢失(在挡块状态之前

msg.bad.catchcond = \
    无效的捕获块条件

msg.catch.unreachable = \
    不合格捕捞后的任何捕捞条款均无法达到

msg.no.brace.try = \
    尝试阻止之前缺少"{"

msg.no.brace.catchblock = \
    挡块身前缺少'{'

msg.try.no.catchfinally = \
    "尝试"而不"捕获"或"最终"

msg.no.return.value = \
  函数{0}并不总是返回值

msg.anon.no.return.value = \
  匿名函数并不总是返回值

msg.return.inconsistent = \
  return语句与以前的用法不一致

msg.generator.returns = \
  TypeError：生成器函数{0}返回一个值

msg.anon.generator.returns = \
  TypeError：匿名生成器函数返回一个值

msg.syntax = \
    语法错误

msg.unexpected.eof = \
    文件意外结束

msg.XML.bad.form = \
    非法形成的XML语法

msg.XML.not.available = \
    XML运行时不可用

msg.too.deep.parser.recursion = \
    解析时递归太深

msg.too.many.constructor.args = \
    构造函数参数过多

msg.too.many.function.args = \
    函数参数过多

msg.no.side.effects = \
    代码没有副作用

msg.extra.trailing.semi = \
    多余的尾部分号

msg.extra.trailing.comma = \
    在ECMA-262对象初始化程序中，尾部逗号不合法

msg.trailing.array.comma = \
    数组文字中的尾部逗号具有不同的跨浏览器行为

msg.equal.as.assign = \
    测试是否相等(==)输错了赋值(=)？
msg.var.hides.arg = \
    变量{0}隐藏参数

msg.destruct.assign.no.init = \
    销毁声明中缺少=

msg.no.old.octal.strict = \
    在严格模式下禁止使用旧的八进制数字.

msg.dup.obj.lit.prop.strict = \
    该对象文字中已经定义了属性"{0}".

msg.dup.param.strict = \
    此函数中已声明参数"{0}".

msg.bad.id.strict = \
    在严格模式下，"{0}"不是有效的标识符.

# ScriptRuntime

# 有更好的信息吗？
# 目前仅用作调用方，调用方和参数属性的毒药
msg.op.not.allowed = \
    不允许执行此操作.

msg.no.properties = \
    {0}没有属性.

msg.invalid.iterator = \
    无效的迭代器值

msg.iterator.primitive = \
    __iterator__返回了原始值

msg.not.iterable = \
    {0}是不可迭代的

msg.invalid.for.each = \
    每个循环均无效

msg.assn.create.strict = \
    分配给未声明的变量{0}

msg.ref.undefined.prop = \
    引用未定义的属性"{0}"

msg.prop.not.found = \
    找不到属性{0}.

msg.set.prop.no.setter = \
    无法设置仅具有吸气剂的属性{0}.

msg.invalid.type = \
    类型{0}的无效JavaScript值

msg.primitive.expected = \
    预期的原始类型(取而代之的是{0})

msg.namespace.expected = \
    命名空间对象应位于::的左侧(改为找到{0})

msg.null.to.object = \
    无法将null转换为对象.

msg.undef.to.object = \
    无法将未定义转换为对象.

msg.cyclic.value = \
    不允许循环{0}值.

msg.is.not.defined = \
    未定义"{0}".

msg.undef.prop.read = \
    无法从{0}读取属性"{1}"

msg.undef.prop.write = \
    无法将{0}的属性"{1}"设置为"{2}"

msg.undef.prop.delete = \
    无法删除{0}的属性"{1}"

msg.undef.method.call = \
    无法调用{0}的方法"{1}"

msg.undef.with = \
    无法将" with"应用于{0}

msg.isnt.function = \
    {0}不是函数，而是{1}.

msg.isnt.function.in = \
    无法调用对象{1}中的属性{0}.它不是函数，而是"{2}".

msg.function.not.found = \
    找不到功能{0}.

msg.function.not.found.in = \
    在对象{1}中找不到函数{0}.

msg.isnt.xml.object = \
    {0}不是xml对象.

msg.no.ref.to.get = \
    {0}不是读取参考值的参考.

msg.no.ref.to.set = \
    {0}不是将参考值设置为{1}的参考.

msg.no.ref.from.function = \
    函数{0}不能用作分配的左侧\
    或作为++或-运算符的操作数.

msg.bad.default.value = \
    对象的getDefaultValue()方法返回一个对象.

msg.instanceof.not.object = \
    不能在非对象上使用" instanceof".

msg.instanceof.bad.prototype = \
    {0}的" prototype"属性不是对象.

msg.in.not.object = \
    不能在非对象上使用" in".

msg.bad.radix = \
    非法基数{0}.

# ScriptableObject
msg.default.value = \
    找不到对象的默认值.

msg.zero.arg.ctor = \
    无法加载没有零参数构造函数的类"{0}".

repeat.defineClass.name = \
    无效的方法"{0}"：名称"{1}"已被使用.

msg.ctor.multiple.parms = \
    无法定义构造函数或类{0}，因为多个\
    构造函数具有多个参数.

msg.extend.scriptable = \
    {0}必须扩展ScriptableObject才能定义属性{1}.

msg.bad.getter.parms = \
    为了定义属性，getter{0}必须具有零个参数\
    或单个ScriptableObject参数.

msg.obj.getter.parms = \
    期望静态或委托的获取器{0}采用ScriptableObject参数.

msg.getter.static = \
    Getter和setter必须都是静态的，也不能都是静态的.

msg.setter.return = \
    设置器必须具有无效的返回类型：{0}

msg.setter2.parms = \
    两参数设置器必须将ScriptableObject作为其第一个参数.
msg.setter1.parms = \
    预期的{0}单参数设置器

msg.setter2.expected = \
    预期静态或委托设置器{0}采用两个参数.

msg.setter.parms = \
    设置程序需要一个或两个参数.

msg.setter.bad.type = \
    设置器"{1}"中不支持的参数类型"{0}".

msg.add.sealed = \
    无法将属性添加到密封的对象{0}.

msg.remove.sealed = \
    无法从密封的对象{0}中删除属性.

msg.modify.sealed = \
    无法修改密封对象的属性：{0}.

msg.modify.readonly = \
    无法修改只读属性：{0}.

msg.both.data.and.accessor.desc = \
    不能同时是数据和访问描述符.

msg.change.configurable.false.to.true = \
    无法将"{0}"的可配置属性从false更改为true.

msg.change.enumerable.with.configurable.false = \
    由于可配置为false，因此无法更改"{0}"的可枚举属性.

msg.change.writable.false.to.true.with.configurable.false = \
    由于可配置为false，因此无法将"{0}"的可写属性从false更改为true.

msg.change.value.with.writable.false = \
    由于writable为false，因此无法更改属性"{0}"的值.

msg.change.getter.with.configurable.false = \
    无法更改get属性"{0}"，因为可配置为false.

msg.change.setter.with.configurable.false = \
    由于可配置为false，因此无法更改"{0}"的设置属性.

msg.change.property.data.to.accessor.with.configurable.false = \
    无法将"{0}"从数据属性更改为访问器属性，因为可配置为false.

msg.change.property.accessor.to.data.with.configurable.false = \
    由于可配置为false，因此无法将"{0}"从访问者属性更改为数据属性.

msg.not.extensible = \
    由于可扩展性为false，因此无法向该对象添加属性.

msg.delete.property.with.configurable.false = \
    无法删除"{0}"属性，因为可配置为false.

# TokenStream
msg.missing.exponent = \
    缺少指数

msg.caught.nfe = \
    数字格式错误

msg.unterminated.string.lit = \
    未终止的字符串文字

msg.unterminated.comment = \
    未终止的评论

msg.unterminated.re.lit = \
    未终止的正则表达式文字

msg.invalid.re.flag = \
    正则表达式后的标志无效

msg.no.re.input.for = \
    {0}无输入

msg.illegal.character = \
    非法字符

msg.invalid.escape = \
    无效的Unicode转义序列

msg.bad.namespace = \
    不是有效的默认名称空间语句. \
    语法是：default xml namespace = EXPRESSION;

# TokensStream警告
msg.bad.octal.literal = \
    非法的八进制文字{0};解释为十进制数字

msg.reserved.keyword = \
    非法使用将来的保留关键字{0};解释为普通标识符

# LiveConnect错误
msg.java.internal.field.type = \
    内部错误：类型{0}的类型转换以分配给{2}上的{1}失败.

msg.java.conversion.implicit_method = \
    在类{1}上找不到转换器方法"{0}".

msg.java.method.assign = \
    无法将Java方法"{0}"分配给它.

msg.java.internal.private = \
    内部错误：尝试访问私有/受保护的字段"{0}".

msg.java.no_such_method = \
    找不到方法{0}.

msg.script.is.not.constructor = \
    脚本对象不是构造函数.

msg.nonjava.method = \
    调用Java方法"{0}"，并用{1}作为"此"值，无法将其转换为Java类型{2}.

msg.java.member.not.found = \
    Java类"{0}"没有名为"{1}"的公共实例字段或方法.

msg.java.array.index.out.of.bounds = \
    数组索引{0}超出范围[0 ..{1}].

msg.java.array.member.not.found = \
    Java数组没有名为"{0}"的公共实例字段或方法.

msg.pkg.int = \
    Java软件包名称可能不是数字.

msg.access.prohibited = \
    禁止访问Java类"{0}".

# ImporterTopLevel
msg.ambig.import = \
    导入不明确："{0}"和"{1}".

msg.not.pkg = \
    函数importPackage必须与包一起调用;改为使用"{0}".

msg.not.class = \
    函数importClass必须与一个类一起调用\;改为使用"{0}".

msg.not.class.not.pkg = \
    "{0}"既不是类也不是包.

msg.prop.defined = \
    由于已经定义了该名称的属性，因此无法导入"{0}".

#JavaAdapter
msg.adapter.zero.args = \
    JavaAdapter至少需要一个参数.

msg.not.java.class.arg = \
参数{0}不是Java类：{1}.

#JavaAdapter
msg.only.one.super = \
JavaAdapter只能扩展一个类.有{0}和{1}.


# 数组
msg.arraylength.bad = \
    数组长度不合适.

# 数组
msg.arraylength.too.big = \
    阵列长度{0}超出了​​支持的容量限制.

msg.empty.array.reduce = \
    减少没有初始值的空数组

# URI
msg.bad.uri = \
    URI序列格式错误.

# 号
msg.bad.precision = \
    精度{0}超出范围.
# NativeGenerator
msg.send.newborn = \
  试图将价值传递给新生代发电机

msg.already.exec.gen = \
    已经执行生成器
    
msg.StopIteration.invalid = \
    StopIteration不能更改为任意对象.

# 口译员
msg.yield.closing = \
  关闭发电机的收益

msg.Called.null.or.undefined = \
  {0} .prototype.{1}方法调用为null或未定义

msg.first.arg.not.regexp = \
  {0} .prototype.{1}的第一个参数不能为正则表达式

msg.arrowfunction.generator = \
  箭头功能无法成为生成器

# 参数
msg.arguments.not.access.strict = \
  在严格模式下，无法访问参数对象的"{0}"属性.

# 符号支持
msg.object.not.symbolscriptable = \
  对象{0}不支持符号键

msg.no.assign.symbol.strict = \
  不能在严格模式下为符号对象分配属性

msg.not.a.string = \
  该对象不是字符串

msg.not.a.number = \
  对象不是数字

msg.no.symbol.new = \
  不能使用\" new \"构造符号对象

msg.compare.symbol = \
  可能无法比较符号对象
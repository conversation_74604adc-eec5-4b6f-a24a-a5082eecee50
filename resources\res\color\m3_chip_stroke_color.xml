<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:state_enabled="false"
        android:color="?attr/colorOnSurface"
        android:alpha="@dimen/material_emphasis_disabled_background"/>
    <item
        android:state_selected="true"
        android:color="@android:color/transparent"/>
    <item
        android:state_checked="true"
        android:color="@android:color/transparent"/>
    <item
        android:state_focused="true"
        android:color="?attr/colorOnSurfaceVariant"/>
    <item android:color="?attr/colorOutline"/>
</selector>

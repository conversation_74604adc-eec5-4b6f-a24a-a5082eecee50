<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:state_selected="true"
        android:state_pressed="true"
        android:color="?attr/colorPrimary"
        android:alpha="@dimen/m3_ripple_pressed_alpha"/>
    <item
        android:state_focused="true"
        android:state_selected="true"
        android:color="?attr/colorPrimary"
        android:alpha="@dimen/m3_ripple_focused_alpha"/>
    <item
        android:state_selected="true"
        android:color="?attr/colorPrimary"
        android:alpha="@dimen/m3_ripple_hovered_alpha"
        android:state_hovered="true"/>
    <item
        android:state_pressed="true"
        android:color="?attr/colorPrimary"
        android:alpha="@dimen/m3_ripple_pressed_alpha"/>
    <item
        android:state_focused="true"
        android:color="?attr/colorOnSurfaceVariant"
        android:alpha="@dimen/m3_ripple_focused_alpha"/>
    <item
        android:color="?attr/colorOnSurfaceVariant"
        android:alpha="@dimen/m3_ripple_hovered_alpha"
        android:state_hovered="true"/>
    <item android:color="@android:color/transparent"/>
</selector>

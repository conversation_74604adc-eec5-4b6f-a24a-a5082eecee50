<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:background="?attr/colorPrimary"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"/>
    </com.google.android.material.appbar.AppBarLayout>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <Button
            android:id="@+id/copy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/text_copy_crash_info"/>
        <Button
            android:id="@+id/exit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/text_exit"/>
    </LinearLayout>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:textSize="16sp"
            android:textColor="@android:color/secondary_text_light"
            android:id="@+id/error"
            android:padding="16dp"
            android:scrollbars="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </ScrollView>
</LinearLayout>

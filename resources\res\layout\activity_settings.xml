<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@android:color/white"
    android:fitsSystemWindows="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.google.android.material.appbar.AppBarLayout
        android:theme="@style/AppTheme.AppBarOverlay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:popupTheme="@style/AppTheme.PopupOverlay"/>
    </com.google.android.material.appbar.AppBarLayout>
    <FrameLayout
        android:id="@+id/fragment_setting"
        android:background="@android:color/white"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true">
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/logList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"/>
    <LinearLayout
        android:orientation="horizontal"
        android:id="@+id/inputContainer"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp">
        <EditText
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:id="@+id/input"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:lines="1"
            android:layout_weight="1"/>
        <Button
            android:theme="@style/ConsoleTheme"
            android:textColor="@android:color/white"
            android:id="@+id/submit"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:text="@string/ok"/>
    </LinearLayout>
</LinearLayout>

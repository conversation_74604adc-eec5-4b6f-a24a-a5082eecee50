<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_gravity="center"
    android:id="@+id/material_clock_face"
    android:layout_width="256dp"
    android:layout_height="256dp">
    <com.google.android.material.timepicker.ClockHandView
        android:id="@+id/material_clock_hand"
        android:tag="skip"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:selectorSize="24dp"/>
</merge>

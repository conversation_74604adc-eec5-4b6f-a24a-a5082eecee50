<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.textfield.TextInputLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:errorIconDrawable="@null"
    app:hintEnabled="false">
    <com.google.android.material.textfield.TextInputEditText
        android:paddingBottom="@dimen/material_time_input_padding_bottom"
        android:layout_width="96dp"
        android:layout_height="80dp"
        android:textCursorDrawable="@drawable/material_cursor_drawable"/>
    <TextView
        android:id="@+id/material_label"
        android:focusable="false"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
</com.google.android.material.textfield.TextInputLayout>

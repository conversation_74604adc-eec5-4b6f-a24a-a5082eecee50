<?xml version="1.0" encoding="utf-8"?>
<com.afollestad.materialdialogs.internal.MDRootLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:md_reduce_padding_no_title_no_buttons="false">
    <include layout="@layout/md_stub_titleframe"/>
    <LinearLayout
        android:orientation="vertical"
        android:paddingLeft="@dimen/md_dialog_frame_margin"
        android:paddingTop="@dimen/md_content_padding_top"
        android:paddingRight="@dimen/md_dialog_frame_margin"
        android:paddingBottom="@dimen/md_content_padding_bottom"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <TextView
            android:textSize="@dimen/md_content_textsize"
            android:layout_gravity="center_horizontal"
            android:id="@+id/md_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="@dimen/md_content_padding_bottom"
            android:fontFamily="sans-serif"/>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <EditText
                android:textSize="@dimen/md_content_textsize"
                android:id="@android:id/input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="-2dp"
                android:layout_marginRight="-2dp"
                android:layout_marginBottom="1dp"/>
            <TextView
                android:textSize="12sp"
                android:gravity="end"
                android:id="@+id/md_minMax"
                android:paddingRight="4dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="48dp"
                android:layout_below="@android:id/input"
                android:layout_alignRight="@android:id/input"
                android:fontFamily="sans-serif"
                android:textAlignment="viewEnd"
                android:paddingEnd="4dp"
                android:layout_alignEnd="@android:id/input"/>
        </RelativeLayout>
    </LinearLayout>
    <include layout="@layout/md_stub_actionbuttons"/>
</com.afollestad.materialdialogs.internal.MDRootLayout>

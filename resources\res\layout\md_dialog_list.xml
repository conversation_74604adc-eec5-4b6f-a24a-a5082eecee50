<?xml version="1.0" encoding="utf-8"?>
<com.afollestad.materialdialogs.internal.MDRootLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <include layout="@layout/md_stub_titleframe_lesspadding"/>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ScrollView
            android:id="@+id/md_contentScrollView"
            android:clipToPadding="false"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:textSize="@dimen/md_content_textsize"
                android:id="@+id/md_content"
                android:paddingLeft="@dimen/md_dialog_frame_margin"
                android:paddingTop="@dimen/md_content_padding_top"
                android:paddingRight="@dimen/md_dialog_frame_margin"
                android:paddingBottom="@dimen/md_content_padding_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </ScrollView>
        <FrameLayout
            android:id="@+id/md_contentListViewFrame"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <androidx.recyclerview.widget.RecyclerView
                android:scrollbarStyle="outsideOverlay"
                android:id="@+id/md_contentRecyclerView"
                android:paddingTop="@dimen/md_content_padding_top"
                android:paddingBottom="@dimen/md_content_padding_bottom"
                android:scrollbars="vertical"
                android:clipToPadding="false"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </FrameLayout>
    </LinearLayout>
    <include layout="@layout/md_stub_actionbuttons"/>
</com.afollestad.materialdialogs.internal.MDRootLayout>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:background="?attr/selectableItemBackground"
    android:paddingLeft="@dimen/md_dialog_frame_margin"
    android:paddingRight="@dimen/md_dialog_frame_margin"
    android:descendantFocusability="blocksDescendants"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/md_listitem_height"
    android:paddingStart="@dimen/md_dialog_frame_margin"
    android:paddingEnd="@dimen/md_dialog_frame_margin">
    <androidx.appcompat.widget.AppCompatCheckBox
        android:gravity="center_vertical"
        android:id="@+id/md_control"
        android:background="@null"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:clickable="false"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"/>
    <TextView
        android:textSize="@dimen/md_listitem_textsize"
        android:gravity="center_vertical"
        android:id="@+id/md_title"
        android:paddingLeft="@dimen/md_listitem_control_margin"
        android:paddingTop="@dimen/md_listitem_vertical_margin_choice"
        android:paddingBottom="@dimen/md_listitem_vertical_margin_choice"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:minHeight="@dimen/md_listitem_height"
        android:paddingStart="@dimen/md_listitem_control_margin"/>
</LinearLayout>

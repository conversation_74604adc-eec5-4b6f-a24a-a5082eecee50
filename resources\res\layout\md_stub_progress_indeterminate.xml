<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="end|center_vertical"
    android:orientation="horizontal"
    android:paddingLeft="@dimen/md_dialog_frame_margin"
    android:paddingTop="@dimen/md_content_padding_top"
    android:paddingRight="@dimen/md_dialog_frame_margin"
    android:paddingBottom="@dimen/md_content_padding_top"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <ProgressBar
        android:id="@android:id/progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        style="@style/Widget.MaterialProgressBar.ProgressBar"/>
    <TextView
        android:textSize="@dimen/md_content_textsize"
        android:gravity="start"
        android:id="@+id/md_content"
        android:paddingLeft="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="sans-serif"
        android:textAlignment="viewStart"
        android:paddingStart="16dp"/>
</LinearLayout>

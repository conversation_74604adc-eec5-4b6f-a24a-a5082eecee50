<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:id="@+id/md_titleFrame"
    android:paddingLeft="@dimen/md_dialog_frame_margin"
    android:paddingTop="@dimen/md_dialog_frame_margin"
    android:paddingRight="@dimen/md_dialog_frame_margin"
    android:paddingBottom="@dimen/md_title_frame_margin_bottom_less"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <ImageView
        android:id="@+id/md_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/md_icon_margin"
        android:scaleType="fitXY"
        android:layout_marginEnd="@dimen/md_icon_margin"/>
    <TextView
        android:textSize="@dimen/md_title_textsize"
        android:id="@+id/md_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>
</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<CheckedTextView xmlns:android="http://schemas.android.com/apk/res/android"
    android:textColor="@color/spinner_item_text_color"
    android:ellipsize="marquee"
    android:gravity="center"
    android:id="@android:id/text1"
    android:paddingTop="16dp"
    android:paddingBottom="16dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:singleLine="true"
    android:paddingStart="12dp"
    android:paddingEnd="12dp"/>

<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <item
        android:id="@+id/action_switch_log_level"
        android:title="@string/text_log_level"
        app:actionViewClass="android.widget.Spinner"
        app:showAsAction="always"/>
    <item
        android:icon="@drawable/round_search_24"
        android:id="@+id/action_search"
        android:title="@string/text_search"
        android:inputType="text"
        android:imeOptions="actionSearch"
        android:iconTint="@android:color/white"
        app:actionViewClass="androidx.appcompat.widget.SearchView"
        app:iconTint="@android:color/white"
        app:showAsAction="collapseActionView|always"/>
    <item
        android:icon="@drawable/round_open_in_new_24"
        android:id="@+id/action_open_by_other_apps"
        android:title="@string/text_open_by_other_apps"
        android:iconTint="@android:color/white"
        app:iconTint="@android:color/white"
        app:showAsAction="ifRoom"/>
    <item
        android:id="@+id/settings"
        android:title="@string/text_settings"/>
    <item
        android:id="@+id/action_clear_log_file"
        android:title="@string/text_clear_log_file"/>
</menu>

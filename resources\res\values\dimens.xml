<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="abc_action_bar_content_inset_material">16dp</dimen>
    <dimen name="abc_action_bar_content_inset_with_nav">72dp</dimen>
    <dimen name="abc_action_bar_default_height_material">56dp</dimen>
    <dimen name="abc_action_bar_default_padding_end_material">0dp</dimen>
    <dimen name="abc_action_bar_default_padding_start_material">0dp</dimen>
    <dimen name="abc_action_bar_elevation_material">4dp</dimen>
    <dimen name="abc_action_bar_icon_vertical_padding_material">16dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_end_material">10dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_start_material">6dp</dimen>
    <dimen name="abc_action_bar_stacked_max_height">48dp</dimen>
    <dimen name="abc_action_bar_stacked_tab_max_width">180dp</dimen>
    <dimen name="abc_action_bar_subtitle_bottom_margin_material">5dp</dimen>
    <dimen name="abc_action_bar_subtitle_top_margin_material">-3dp</dimen>
    <dimen name="abc_action_button_min_height_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_overflow_material">36dp</dimen>
    <dimen name="abc_alert_dialog_button_bar_height">48dp</dimen>
    <dimen name="abc_alert_dialog_button_dimen">48dp</dimen>
    <dimen name="abc_button_inset_horizontal_material">@dimen/abc_control_inset_material</dimen>
    <dimen name="abc_button_inset_vertical_material">6dp</dimen>
    <dimen name="abc_button_padding_horizontal_material">8dp</dimen>
    <dimen name="abc_button_padding_vertical_material">@dimen/abc_control_padding_material</dimen>
    <dimen name="abc_cascading_menus_min_smallest_width">720dp</dimen>
    <dimen name="abc_config_prefDialogWidth">320dp</dimen>
    <dimen name="abc_control_corner_material">2dp</dimen>
    <dimen name="abc_control_inset_material">4dp</dimen>
    <dimen name="abc_control_padding_material">4dp</dimen>
    <dimen name="abc_dialog_corner_radius_material">2dp</dimen>
    <dimen name="abc_dialog_fixed_height_major">80%</dimen>
    <dimen name="abc_dialog_fixed_height_minor">100%</dimen>
    <dimen name="abc_dialog_fixed_width_major">320dp</dimen>
    <dimen name="abc_dialog_fixed_width_minor">320dp</dimen>
    <dimen name="abc_dialog_list_padding_bottom_no_buttons">8dp</dimen>
    <dimen name="abc_dialog_list_padding_top_no_title">8dp</dimen>
    <dimen name="abc_dialog_min_width_major">65%</dimen>
    <dimen name="abc_dialog_min_width_minor">95%</dimen>
    <dimen name="abc_dialog_padding_material">24dp</dimen>
    <dimen name="abc_dialog_padding_top_material">18dp</dimen>
    <dimen name="abc_dialog_title_divider_material">8dp</dimen>
    <dimen name="abc_disabled_alpha_material_dark">0.3</dimen>
    <dimen name="abc_disabled_alpha_material_light">0.26</dimen>
    <dimen name="abc_dropdownitem_icon_width">32dp</dimen>
    <dimen name="abc_dropdownitem_text_padding_left">8dp</dimen>
    <dimen name="abc_dropdownitem_text_padding_right">8dp</dimen>
    <dimen name="abc_edit_text_inset_bottom_material">7dp</dimen>
    <dimen name="abc_edit_text_inset_horizontal_material">4dp</dimen>
    <dimen name="abc_edit_text_inset_top_material">10dp</dimen>
    <dimen name="abc_floating_window_z">16dp</dimen>
    <dimen name="abc_list_item_height_large_material">80dp</dimen>
    <dimen name="abc_list_item_height_material">64dp</dimen>
    <dimen name="abc_list_item_height_small_material">48dp</dimen>
    <dimen name="abc_list_item_padding_horizontal_material">@dimen/abc_action_bar_content_inset_material</dimen>
    <dimen name="abc_panel_menu_list_width">296dp</dimen>
    <dimen name="abc_progress_bar_height_material">4dp</dimen>
    <dimen name="abc_search_view_preferred_height">48dp</dimen>
    <dimen name="abc_search_view_preferred_width">320dp</dimen>
    <dimen name="abc_seekbar_track_background_height_material">2dp</dimen>
    <dimen name="abc_seekbar_track_progress_height_material">2dp</dimen>
    <dimen name="abc_select_dialog_padding_start_material">20dp</dimen>
    <dimen name="abc_star_big">48dp</dimen>
    <dimen name="abc_star_medium">36dp</dimen>
    <dimen name="abc_star_small">16dp</dimen>
    <dimen name="abc_switch_padding">0px</dimen>
    <dimen name="abc_text_size_body_1_material">14sp</dimen>
    <dimen name="abc_text_size_body_2_material">14sp</dimen>
    <dimen name="abc_text_size_button_material">14sp</dimen>
    <dimen name="abc_text_size_caption_material">12sp</dimen>
    <dimen name="abc_text_size_display_1_material">34sp</dimen>
    <dimen name="abc_text_size_display_2_material">45sp</dimen>
    <dimen name="abc_text_size_display_3_material">56sp</dimen>
    <dimen name="abc_text_size_display_4_material">112sp</dimen>
    <dimen name="abc_text_size_headline_material">24sp</dimen>
    <dimen name="abc_text_size_large_material">22sp</dimen>
    <dimen name="abc_text_size_medium_material">18sp</dimen>
    <dimen name="abc_text_size_menu_header_material">14sp</dimen>
    <dimen name="abc_text_size_menu_material">16sp</dimen>
    <dimen name="abc_text_size_small_material">14sp</dimen>
    <dimen name="abc_text_size_subhead_material">16sp</dimen>
    <dimen name="abc_text_size_subtitle_material_toolbar">16dp</dimen>
    <dimen name="abc_text_size_title_material">20sp</dimen>
    <dimen name="abc_text_size_title_material_toolbar">20dp</dimen>
    <dimen name="appcompat_dialog_background_inset">16dp</dimen>
    <dimen name="cardview_compat_inset_shadow">1dp</dimen>
    <dimen name="cardview_default_elevation">2dp</dimen>
    <dimen name="cardview_default_radius">2dp</dimen>
    <dimen name="circular_progress_border">4dp</dimen>
    <dimen name="clock_face_margin_start">64dp</dimen>
    <dimen name="compat_button_inset_horizontal_material">4dp</dimen>
    <dimen name="compat_button_inset_vertical_material">6dp</dimen>
    <dimen name="compat_button_padding_horizontal_material">8dp</dimen>
    <dimen name="compat_button_padding_vertical_material">4dp</dimen>
    <dimen name="compat_control_corner_material">2dp</dimen>
    <dimen name="compat_notification_large_icon_max_height">320dp</dimen>
    <dimen name="compat_notification_large_icon_max_width">320dp</dimen>
    <dimen name="def_drawer_elevation">10dp</dimen>
    <dimen name="design_appbar_elevation">4dp</dimen>
    <dimen name="design_bottom_navigation_active_item_max_width">168dp</dimen>
    <dimen name="design_bottom_navigation_active_item_min_width">96dp</dimen>
    <dimen name="design_bottom_navigation_active_text_size">14sp</dimen>
    <dimen name="design_bottom_navigation_elevation">8dp</dimen>
    <dimen name="design_bottom_navigation_height">56dp</dimen>
    <dimen name="design_bottom_navigation_icon_size">24dp</dimen>
    <dimen name="design_bottom_navigation_item_max_width">96dp</dimen>
    <dimen name="design_bottom_navigation_item_min_width">56dp</dimen>
    <dimen name="design_bottom_navigation_label_padding">10dp</dimen>
    <dimen name="design_bottom_navigation_margin">8dp</dimen>
    <dimen name="design_bottom_navigation_shadow_height">1dp</dimen>
    <dimen name="design_bottom_navigation_text_size">12sp</dimen>
    <dimen name="design_bottom_sheet_elevation">8dp</dimen>
    <dimen name="design_bottom_sheet_modal_elevation">16dp</dimen>
    <dimen name="design_bottom_sheet_peek_height_min">64dp</dimen>
    <dimen name="design_fab_border_width">0.5dp</dimen>
    <dimen name="design_fab_elevation">6dp</dimen>
    <dimen name="design_fab_image_size">24dp</dimen>
    <dimen name="design_fab_size_mini">40dp</dimen>
    <dimen name="design_fab_size_normal">56dp</dimen>
    <dimen name="design_fab_translation_z_hovered_focused">6dp</dimen>
    <dimen name="design_fab_translation_z_pressed">6dp</dimen>
    <dimen name="design_navigation_elevation">16dp</dimen>
    <dimen name="design_navigation_icon_padding">32dp</dimen>
    <dimen name="design_navigation_icon_size">24dp</dimen>
    <dimen name="design_navigation_item_horizontal_padding">16dp</dimen>
    <dimen name="design_navigation_item_icon_padding">32dp</dimen>
    <dimen name="design_navigation_item_vertical_padding">4dp</dimen>
    <dimen name="design_navigation_max_width">280dp</dimen>
    <dimen name="design_navigation_padding_bottom">8dp</dimen>
    <dimen name="design_navigation_separator_vertical_padding">8dp</dimen>
    <dimen name="design_snackbar_action_inline_max_width">128dp</dimen>
    <dimen name="design_snackbar_action_text_color_alpha">1</dimen>
    <dimen name="design_snackbar_background_corner_radius">0dp</dimen>
    <dimen name="design_snackbar_elevation">6dp</dimen>
    <dimen name="design_snackbar_extra_spacing_horizontal">0dp</dimen>
    <dimen name="design_snackbar_max_width">-1px</dimen>
    <dimen name="design_snackbar_min_width">-1px</dimen>
    <dimen name="design_snackbar_padding_horizontal">12dp</dimen>
    <dimen name="design_snackbar_padding_vertical">14dp</dimen>
    <dimen name="design_snackbar_padding_vertical_2lines">16dp</dimen>
    <dimen name="design_snackbar_text_size">14sp</dimen>
    <dimen name="design_tab_max_width">264dp</dimen>
    <dimen name="design_tab_scrollable_min_width">72dp</dimen>
    <dimen name="design_tab_text_size">14sp</dimen>
    <dimen name="design_tab_text_size_2line">12sp</dimen>
    <dimen name="design_textinput_caption_translate_y">5dp</dimen>
    <dimen name="disabled_alpha_material_dark">0.3</dimen>
    <dimen name="disabled_alpha_material_light">0.26</dimen>
    <dimen name="fastscroll_default_thickness">8dp</dimen>
    <dimen name="fastscroll_margin">0dp</dimen>
    <dimen name="fastscroll_minimum_range">50dp</dimen>
    <dimen name="floaty_window_offset">12dp</dimen>
    <dimen name="highlight_alpha_material_colored">0.26</dimen>
    <dimen name="highlight_alpha_material_dark">0.2</dimen>
    <dimen name="highlight_alpha_material_light">0.12</dimen>
    <dimen name="hint_alpha_material_dark">0.5</dimen>
    <dimen name="hint_alpha_material_light">0.38</dimen>
    <dimen name="hint_pressed_alpha_material_dark">0.7</dimen>
    <dimen name="hint_pressed_alpha_material_light">0.54</dimen>
    <dimen name="item_touch_helper_max_drag_scroll_per_frame">20dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_max_velocity">800dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_velocity">120dp</dimen>
    <dimen name="m3_alert_dialog_action_bottom_padding">14dp</dimen>
    <dimen name="m3_alert_dialog_action_top_padding">14dp</dimen>
    <dimen name="m3_alert_dialog_corner_size">28dp</dimen>
    <dimen name="m3_alert_dialog_elevation">@dimen/m3_sys_elevation_level3</dimen>
    <dimen name="m3_alert_dialog_icon_margin">16dp</dimen>
    <dimen name="m3_alert_dialog_icon_size">24dp</dimen>
    <dimen name="m3_alert_dialog_title_bottom_margin">16dp</dimen>
    <dimen name="m3_appbar_expanded_title_margin_bottom">16dp</dimen>
    <dimen name="m3_appbar_expanded_title_margin_horizontal">16dp</dimen>
    <dimen name="m3_appbar_scrim_height_trigger">96dp</dimen>
    <dimen name="m3_appbar_scrim_height_trigger_large">112dp</dimen>
    <dimen name="m3_appbar_scrim_height_trigger_medium">112dp</dimen>
    <dimen name="m3_appbar_size_compact">64dp</dimen>
    <dimen name="m3_appbar_size_large">152dp</dimen>
    <dimen name="m3_appbar_size_medium">112dp</dimen>
    <dimen name="m3_badge_horizontal_offset">1.5dp</dimen>
    <dimen name="m3_badge_radius">3dp</dimen>
    <dimen name="m3_badge_vertical_offset">1.5dp</dimen>
    <dimen name="m3_badge_with_text_horizontal_offset">3dp</dimen>
    <dimen name="m3_badge_with_text_radius">7dp</dimen>
    <dimen name="m3_badge_with_text_vertical_offset">4dp</dimen>
    <dimen name="m3_bottom_nav_item_active_indicator_height">32dp</dimen>
    <dimen name="m3_bottom_nav_item_active_indicator_margin_horizontal">4dp</dimen>
    <dimen name="m3_bottom_nav_item_active_indicator_width">64dp</dimen>
    <dimen name="m3_bottom_nav_item_padding_bottom">16dp</dimen>
    <dimen name="m3_bottom_nav_item_padding_top">12dp</dimen>
    <dimen name="m3_bottom_nav_min_height">80dp</dimen>
    <dimen name="m3_bottom_sheet_drag_handle_bottom_padding">20dp</dimen>
    <dimen name="m3_bottom_sheet_elevation">@dimen/m3_sys_elevation_level2</dimen>
    <dimen name="m3_bottom_sheet_modal_elevation">@dimen/m3_sys_elevation_level3</dimen>
    <dimen name="m3_bottomappbar_fab_cradle_margin">6dp</dimen>
    <dimen name="m3_bottomappbar_fab_cradle_rounded_corner_radius">4dp</dimen>
    <dimen name="m3_bottomappbar_fab_cradle_vertical_offset">12dp</dimen>
    <dimen name="m3_bottomappbar_fab_end_margin">16dp</dimen>
    <dimen name="m3_bottomappbar_height">@dimen/m3_comp_bottom_app_bar_container_height</dimen>
    <dimen name="m3_bottomappbar_horizontal_padding">4dp</dimen>
    <dimen name="m3_btn_dialog_btn_min_width">64dp</dimen>
    <dimen name="m3_btn_dialog_btn_spacing">8dp</dimen>
    <dimen name="m3_btn_disabled_elevation">0dp</dimen>
    <dimen name="m3_btn_disabled_translation_z">0dp</dimen>
    <dimen name="m3_btn_elevated_btn_elevation">1dp</dimen>
    <dimen name="m3_btn_elevation">0dp</dimen>
    <dimen name="m3_btn_icon_btn_padding_left">16dp</dimen>
    <dimen name="m3_btn_icon_btn_padding_right">24dp</dimen>
    <dimen name="m3_btn_icon_only_default_padding">10dp</dimen>
    <dimen name="m3_btn_icon_only_default_size">20dp</dimen>
    <dimen name="m3_btn_icon_only_icon_padding">0dp</dimen>
    <dimen name="m3_btn_icon_only_min_width">20dp</dimen>
    <dimen name="m3_btn_inset">4dp</dimen>
    <dimen name="m3_btn_max_width">320dp</dimen>
    <dimen name="m3_btn_padding_bottom">6dp</dimen>
    <dimen name="m3_btn_padding_left">24dp</dimen>
    <dimen name="m3_btn_padding_right">24dp</dimen>
    <dimen name="m3_btn_padding_top">6dp</dimen>
    <dimen name="m3_btn_stroke_size">1dp</dimen>
    <dimen name="m3_btn_text_btn_icon_padding_left">12dp</dimen>
    <dimen name="m3_btn_text_btn_icon_padding_right">16dp</dimen>
    <dimen name="m3_btn_text_btn_padding_left">12dp</dimen>
    <dimen name="m3_btn_text_btn_padding_right">12dp</dimen>
    <dimen name="m3_btn_translation_z_base">0dp</dimen>
    <dimen name="m3_btn_translation_z_hovered">1dp</dimen>
    <dimen name="m3_card_dragged_z">@dimen/m3_sys_elevation_level3</dimen>
    <dimen name="m3_card_elevated_dragged_z">7dp</dimen>
    <dimen name="m3_card_elevated_elevation">@dimen/m3_sys_elevation_level1</dimen>
    <dimen name="m3_card_elevated_hovered_z">2dp</dimen>
    <dimen name="m3_card_elevation">@dimen/m3_sys_elevation_level0</dimen>
    <dimen name="m3_card_hovered_z">@dimen/m3_sys_elevation_level1</dimen>
    <dimen name="m3_card_stroke_width">1dp</dimen>
    <dimen name="m3_chip_checked_hovered_translation_z">1dp</dimen>
    <dimen name="m3_chip_corner_size">8dp</dimen>
    <dimen name="m3_chip_disabled_translation_z">-1dp</dimen>
    <dimen name="m3_chip_dragged_translation_z">7dp</dimen>
    <dimen name="m3_chip_elevated_elevation">1dp</dimen>
    <dimen name="m3_chip_hovered_translation_z">2dp</dimen>
    <dimen name="m3_chip_icon_size">18dp</dimen>
    <dimen name="m3_comp_bottom_app_bar_container_elevation">@dimen/m3_sys_elevation_level2</dimen>
    <dimen name="m3_comp_bottom_app_bar_container_height">80dp</dimen>
    <dimen name="m3_comp_extended_fab_primary_container_elevation">@dimen/m3_sys_elevation_level3</dimen>
    <dimen name="m3_comp_extended_fab_primary_container_height">56dp</dimen>
    <dimen name="m3_comp_extended_fab_primary_focus_container_elevation">@dimen/m3_sys_elevation_level3</dimen>
    <dimen name="m3_comp_extended_fab_primary_focus_state_layer_opacity">@dimen/m3_sys_state_focus_state_layer_opacity</dimen>
    <dimen name="m3_comp_extended_fab_primary_hover_container_elevation">@dimen/m3_sys_elevation_level4</dimen>
    <dimen name="m3_comp_extended_fab_primary_hover_state_layer_opacity">@dimen/m3_sys_state_hover_state_layer_opacity</dimen>
    <dimen name="m3_comp_extended_fab_primary_icon_size">24dp</dimen>
    <dimen name="m3_comp_extended_fab_primary_pressed_container_elevation">@dimen/m3_sys_elevation_level3</dimen>
    <dimen name="m3_comp_extended_fab_primary_pressed_state_layer_opacity">@dimen/m3_sys_state_pressed_state_layer_opacity</dimen>
    <dimen name="m3_comp_fab_primary_container_elevation">@dimen/m3_sys_elevation_level3</dimen>
    <dimen name="m3_comp_fab_primary_container_height">56dp</dimen>
    <dimen name="m3_comp_fab_primary_focus_state_layer_opacity">@dimen/m3_sys_state_focus_state_layer_opacity</dimen>
    <dimen name="m3_comp_fab_primary_hover_container_elevation">@dimen/m3_sys_elevation_level4</dimen>
    <dimen name="m3_comp_fab_primary_hover_state_layer_opacity">@dimen/m3_sys_state_hover_state_layer_opacity</dimen>
    <dimen name="m3_comp_fab_primary_icon_size">24dp</dimen>
    <dimen name="m3_comp_fab_primary_large_container_height">96dp</dimen>
    <dimen name="m3_comp_fab_primary_large_icon_size">36dp</dimen>
    <dimen name="m3_comp_fab_primary_pressed_container_elevation">@dimen/m3_sys_elevation_level3</dimen>
    <dimen name="m3_comp_fab_primary_pressed_state_layer_opacity">@dimen/m3_sys_state_pressed_state_layer_opacity</dimen>
    <dimen name="m3_comp_fab_primary_small_container_height">40dp</dimen>
    <dimen name="m3_comp_fab_primary_small_icon_size">24dp</dimen>
    <dimen name="m3_comp_switch_disabled_handle_elevation">@dimen/m3_sys_elevation_level0</dimen>
    <dimen name="m3_comp_switch_disabled_handle_opacity">0.38</dimen>
    <dimen name="m3_comp_switch_disabled_selected_handle_opacity">1</dimen>
    <dimen name="m3_comp_switch_disabled_selected_icon_opacity">0.38</dimen>
    <dimen name="m3_comp_switch_disabled_track_opacity">0.12</dimen>
    <dimen name="m3_comp_switch_disabled_unselected_handle_opacity">0.38</dimen>
    <dimen name="m3_comp_switch_disabled_unselected_icon_opacity">0.38</dimen>
    <dimen name="m3_comp_switch_handle_elevation">@dimen/m3_sys_elevation_level1</dimen>
    <dimen name="m3_comp_switch_selected_focus_state_layer_opacity">@dimen/m3_sys_state_focus_state_layer_opacity</dimen>
    <dimen name="m3_comp_switch_selected_hover_state_layer_opacity">@dimen/m3_sys_state_hover_state_layer_opacity</dimen>
    <dimen name="m3_comp_switch_selected_pressed_state_layer_opacity">@dimen/m3_sys_state_pressed_state_layer_opacity</dimen>
    <dimen name="m3_comp_switch_track_height">32dp</dimen>
    <dimen name="m3_comp_switch_track_width">52dp</dimen>
    <dimen name="m3_comp_switch_unselected_focus_state_layer_opacity">@dimen/m3_sys_state_focus_state_layer_opacity</dimen>
    <dimen name="m3_comp_switch_unselected_hover_state_layer_opacity">@dimen/m3_sys_state_hover_state_layer_opacity</dimen>
    <dimen name="m3_comp_switch_unselected_pressed_state_layer_opacity">@dimen/m3_sys_state_pressed_state_layer_opacity</dimen>
    <dimen name="m3_datepicker_elevation">@dimen/m3_sys_elevation_level1</dimen>
    <dimen name="m3_divider_heavy_thickness">8dp</dimen>
    <dimen name="m3_exposed_dropdown_menu_popup_elevation">@dimen/m3_sys_elevation_level2</dimen>
    <dimen name="m3_extended_fab_bottom_padding">8dp</dimen>
    <dimen name="m3_extended_fab_end_padding">20dp</dimen>
    <dimen name="m3_extended_fab_icon_padding">12dp</dimen>
    <dimen name="m3_extended_fab_min_height">56dp</dimen>
    <dimen name="m3_extended_fab_start_padding">16dp</dimen>
    <dimen name="m3_extended_fab_top_padding">8dp</dimen>
    <dimen name="m3_fab_border_width">0dp</dimen>
    <dimen name="m3_fab_corner_size">30%</dimen>
    <dimen name="m3_fab_translation_z_hovered_focused">2dp</dimen>
    <dimen name="m3_fab_translation_z_pressed">6dp</dimen>
    <dimen name="m3_large_fab_max_image_size">36dp</dimen>
    <dimen name="m3_large_fab_size">96dp</dimen>
    <dimen name="m3_menu_elevation">@dimen/m3_sys_elevation_level2</dimen>
    <dimen name="m3_navigation_drawer_layout_corner_size">16dp</dimen>
    <dimen name="m3_navigation_item_horizontal_padding">28dp</dimen>
    <dimen name="m3_navigation_item_icon_padding">12dp</dimen>
    <dimen name="m3_navigation_item_shape_inset_bottom">0dp</dimen>
    <dimen name="m3_navigation_item_shape_inset_end">12dp</dimen>
    <dimen name="m3_navigation_item_shape_inset_start">12dp</dimen>
    <dimen name="m3_navigation_item_shape_inset_top">0dp</dimen>
    <dimen name="m3_navigation_item_vertical_padding">4dp</dimen>
    <dimen name="m3_navigation_menu_divider_horizontal_padding">28dp</dimen>
    <dimen name="m3_navigation_menu_headline_horizontal_padding">28dp</dimen>
    <dimen name="m3_navigation_rail_default_width">80dp</dimen>
    <dimen name="m3_navigation_rail_item_active_indicator_height">32dp</dimen>
    <dimen name="m3_navigation_rail_item_active_indicator_margin_horizontal">4dp</dimen>
    <dimen name="m3_navigation_rail_item_active_indicator_width">56dp</dimen>
    <dimen name="m3_navigation_rail_item_min_height">60dp</dimen>
    <dimen name="m3_navigation_rail_item_padding_bottom">8dp</dimen>
    <dimen name="m3_navigation_rail_item_padding_top">4dp</dimen>
    <dimen name="m3_ripple_default_alpha">@dimen/m3_sys_state_dragged_state_layer_opacity</dimen>
    <dimen name="m3_ripple_focused_alpha">@dimen/m3_sys_state_focus_state_layer_opacity</dimen>
    <dimen name="m3_ripple_hovered_alpha">@dimen/m3_sys_state_hover_state_layer_opacity</dimen>
    <dimen name="m3_ripple_pressed_alpha">@dimen/m3_sys_state_pressed_state_layer_opacity</dimen>
    <dimen name="m3_ripple_selectable_pressed_alpha">@dimen/m3_sys_state_pressed_state_layer_opacity</dimen>
    <dimen name="m3_simple_item_color_hovered_alpha">0.08</dimen>
    <dimen name="m3_simple_item_color_selected_alpha">0.12</dimen>
    <dimen name="m3_slider_thumb_elevation">2dp</dimen>
    <dimen name="m3_small_fab_max_image_size">24dp</dimen>
    <dimen name="m3_small_fab_size">40dp</dimen>
    <dimen name="m3_snackbar_action_text_color_alpha">1</dimen>
    <dimen name="m3_snackbar_margin">8dp</dimen>
    <dimen name="m3_sys_elevation_level0">0dp</dimen>
    <dimen name="m3_sys_elevation_level1">1dp</dimen>
    <dimen name="m3_sys_elevation_level2">3dp</dimen>
    <dimen name="m3_sys_elevation_level3">6dp</dimen>
    <dimen name="m3_sys_elevation_level4">8dp</dimen>
    <dimen name="m3_sys_elevation_level5">12dp</dimen>
    <dimen name="m3_sys_motion_easing_emphasized_accelerate_control_x1">0.3</dimen>
    <dimen name="m3_sys_motion_easing_emphasized_accelerate_control_x2">0.8</dimen>
    <dimen name="m3_sys_motion_easing_emphasized_accelerate_control_y1">0</dimen>
    <dimen name="m3_sys_motion_easing_emphasized_accelerate_control_y2">0.2</dimen>
    <dimen name="m3_sys_motion_easing_emphasized_decelerate_control_x1">0.1</dimen>
    <dimen name="m3_sys_motion_easing_emphasized_decelerate_control_x2">0.1</dimen>
    <dimen name="m3_sys_motion_easing_emphasized_decelerate_control_y1">0.7</dimen>
    <dimen name="m3_sys_motion_easing_emphasized_decelerate_control_y2">1</dimen>
    <dimen name="m3_sys_motion_easing_legacy_accelerate_control_x1">0.4</dimen>
    <dimen name="m3_sys_motion_easing_legacy_accelerate_control_x2">1</dimen>
    <dimen name="m3_sys_motion_easing_legacy_accelerate_control_y1">0</dimen>
    <dimen name="m3_sys_motion_easing_legacy_accelerate_control_y2">1</dimen>
    <dimen name="m3_sys_motion_easing_legacy_control_x1">0.4</dimen>
    <dimen name="m3_sys_motion_easing_legacy_control_x2">0.2</dimen>
    <dimen name="m3_sys_motion_easing_legacy_control_y1">0</dimen>
    <dimen name="m3_sys_motion_easing_legacy_control_y2">1</dimen>
    <dimen name="m3_sys_motion_easing_legacy_decelerate_control_x1">0</dimen>
    <dimen name="m3_sys_motion_easing_legacy_decelerate_control_x2">0.2</dimen>
    <dimen name="m3_sys_motion_easing_legacy_decelerate_control_y1">0</dimen>
    <dimen name="m3_sys_motion_easing_legacy_decelerate_control_y2">1</dimen>
    <dimen name="m3_sys_motion_easing_linear_control_x1">0</dimen>
    <dimen name="m3_sys_motion_easing_linear_control_x2">1</dimen>
    <dimen name="m3_sys_motion_easing_linear_control_y1">0</dimen>
    <dimen name="m3_sys_motion_easing_linear_control_y2">1</dimen>
    <dimen name="m3_sys_motion_easing_standard_accelerate_control_x1">0.3</dimen>
    <dimen name="m3_sys_motion_easing_standard_accelerate_control_x2">1</dimen>
    <dimen name="m3_sys_motion_easing_standard_accelerate_control_y1">0</dimen>
    <dimen name="m3_sys_motion_easing_standard_accelerate_control_y2">1</dimen>
    <dimen name="m3_sys_motion_easing_standard_control_x1">0.2</dimen>
    <dimen name="m3_sys_motion_easing_standard_control_x2">0</dimen>
    <dimen name="m3_sys_motion_easing_standard_control_y1">0</dimen>
    <dimen name="m3_sys_motion_easing_standard_control_y2">1</dimen>
    <dimen name="m3_sys_motion_easing_standard_decelerate_control_x1">0</dimen>
    <dimen name="m3_sys_motion_easing_standard_decelerate_control_x2">0</dimen>
    <dimen name="m3_sys_motion_easing_standard_decelerate_control_y1">0</dimen>
    <dimen name="m3_sys_motion_easing_standard_decelerate_control_y2">1</dimen>
    <dimen name="m3_sys_state_dragged_state_layer_opacity">0.32</dimen>
    <dimen name="m3_sys_state_focus_state_layer_opacity">0.24</dimen>
    <dimen name="m3_sys_state_hover_state_layer_opacity">0.16</dimen>
    <dimen name="m3_sys_state_pressed_state_layer_opacity">0.24</dimen>
    <dimen name="m3_timepicker_display_stroke_width">2dp</dimen>
    <dimen name="m3_timepicker_window_elevation">@dimen/m3_sys_elevation_level3</dimen>
    <dimen name="material_bottom_sheet_max_width">640dp</dimen>
    <dimen name="material_clock_display_padding">24dp</dimen>
    <dimen name="material_clock_face_margin_top">44dp</dimen>
    <dimen name="material_clock_hand_center_dot_radius">4dp</dimen>
    <dimen name="material_clock_hand_padding">4dp</dimen>
    <dimen name="material_clock_hand_stroke_width">2dp</dimen>
    <dimen name="material_clock_number_text_padding">20dp</dimen>
    <dimen name="material_clock_number_text_size">15dp</dimen>
    <dimen name="material_clock_period_toggle_height">96dp</dimen>
    <dimen name="material_clock_period_toggle_margin_left">12dp</dimen>
    <dimen name="material_clock_period_toggle_width">52dp</dimen>
    <dimen name="material_clock_size">256dp</dimen>
    <dimen name="material_cursor_inset_bottom">-6dp</dimen>
    <dimen name="material_cursor_inset_top">-12dp</dimen>
    <dimen name="material_cursor_width">2dp</dimen>
    <dimen name="material_divider_thickness">1dp</dimen>
    <dimen name="material_emphasis_disabled">0.38</dimen>
    <dimen name="material_emphasis_disabled_background">0.12</dimen>
    <dimen name="material_emphasis_high_type">0.87</dimen>
    <dimen name="material_emphasis_medium">0.6</dimen>
    <dimen name="material_filled_edittext_font_1_3_padding_bottom">12dp</dimen>
    <dimen name="material_filled_edittext_font_1_3_padding_top">23dp</dimen>
    <dimen name="material_filled_edittext_font_2_0_padding_bottom">8dp</dimen>
    <dimen name="material_filled_edittext_font_2_0_padding_top">32dp</dimen>
    <dimen name="material_font_1_3_box_collapsed_padding_top">4dp</dimen>
    <dimen name="material_font_2_0_box_collapsed_padding_top">8dp</dimen>
    <dimen name="material_helper_text_default_padding_top">4dp</dimen>
    <dimen name="material_helper_text_font_1_3_padding_horizontal">12dp</dimen>
    <dimen name="material_helper_text_font_1_3_padding_top">8dp</dimen>
    <dimen name="material_input_text_to_prefix_suffix_padding">2dp</dimen>
    <dimen name="material_textinput_default_width">245dp</dimen>
    <dimen name="material_textinput_max_width">488dp</dimen>
    <dimen name="material_textinput_min_width">56dp</dimen>
    <dimen name="material_time_input_padding_bottom">6dp</dimen>
    <dimen name="material_time_picker_minimum_screen_height">560dp</dimen>
    <dimen name="material_time_picker_minimum_screen_width">340dp</dimen>
    <dimen name="material_timepicker_dialog_buttons_margin_top">24dp</dimen>
    <dimen name="md_action_corner_radius">2dp</dimen>
    <dimen name="md_bg_corner_radius">2dp</dimen>
    <dimen name="md_button_frame_vertical_padding">8dp</dimen>
    <dimen name="md_button_height">48dp</dimen>
    <dimen name="md_button_inset_horizontal">4dp</dimen>
    <dimen name="md_button_inset_vertical">6dp</dimen>
    <dimen name="md_button_min_width">72dp</dimen>
    <dimen name="md_button_padding_frame_side">12dp</dimen>
    <dimen name="md_button_padding_horizontal">8dp</dimen>
    <dimen name="md_button_padding_horizontal_internalexternal">32dp</dimen>
    <dimen name="md_button_padding_vertical">4dp</dimen>
    <dimen name="md_button_textpadding_horizontal">1dp</dimen>
    <dimen name="md_button_textsize">14sp</dimen>
    <dimen name="md_content_padding_bottom">8dp</dimen>
    <dimen name="md_content_padding_top">8dp</dimen>
    <dimen name="md_content_textsize">16sp</dimen>
    <dimen name="md_dialog_frame_margin">24dp</dimen>
    <dimen name="md_dialog_horizontal_margin">28dp</dimen>
    <dimen name="md_dialog_max_width">356dp</dimen>
    <dimen name="md_dialog_vertical_margin">52dp</dimen>
    <dimen name="md_divider_height">1dp</dimen>
    <dimen name="md_icon_margin">16dp</dimen>
    <dimen name="md_icon_max_size">48dp</dimen>
    <dimen name="md_listitem_control_margin">6dp</dimen>
    <dimen name="md_listitem_height">48dp</dimen>
    <dimen name="md_listitem_margin_left">24dp</dimen>
    <dimen name="md_listitem_textsize">16sp</dimen>
    <dimen name="md_listitem_vertical_margin">12dp</dimen>
    <dimen name="md_listitem_vertical_margin_choice">8dp</dimen>
    <dimen name="md_neutral_button_margin">12dp</dimen>
    <dimen name="md_notitle_vertical_padding">16dp</dimen>
    <dimen name="md_notitle_vertical_padding_more">20dp</dimen>
    <dimen name="md_simplelistitem_padding_top">8dp</dimen>
    <dimen name="md_title_frame_margin_bottom">12dp</dimen>
    <dimen name="md_title_frame_margin_bottom_less">6dp</dimen>
    <dimen name="md_title_textsize">20sp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_bottom">80dp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_end">24dp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_start">24dp</dimen>
    <dimen name="mtrl_alert_dialog_background_inset_top">80dp</dimen>
    <dimen name="mtrl_alert_dialog_picker_background_inset">24dp</dimen>
    <dimen name="mtrl_badge_horizontal_edge_offset">4dp</dimen>
    <dimen name="mtrl_badge_long_text_horizontal_padding">4dp</dimen>
    <dimen name="mtrl_badge_radius">4dp</dimen>
    <dimen name="mtrl_badge_text_horizontal_edge_offset">6dp</dimen>
    <dimen name="mtrl_badge_text_size">10sp</dimen>
    <dimen name="mtrl_badge_toolbar_action_menu_item_horizontal_offset">12dp</dimen>
    <dimen name="mtrl_badge_toolbar_action_menu_item_vertical_offset">12dp</dimen>
    <dimen name="mtrl_badge_with_text_radius">8dp</dimen>
    <dimen name="mtrl_bottomappbar_fabOffsetEndMode">60dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_bottom_margin">16dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_margin">5dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius">8dp</dimen>
    <dimen name="mtrl_bottomappbar_fab_cradle_vertical_offset">0dp</dimen>
    <dimen name="mtrl_bottomappbar_height">56dp</dimen>
    <dimen name="mtrl_btn_corner_radius">4dp</dimen>
    <dimen name="mtrl_btn_dialog_btn_min_width">64dp</dimen>
    <dimen name="mtrl_btn_disabled_elevation">0dp</dimen>
    <dimen name="mtrl_btn_disabled_z">0dp</dimen>
    <dimen name="mtrl_btn_elevation">2dp</dimen>
    <dimen name="mtrl_btn_focused_z">2dp</dimen>
    <dimen name="mtrl_btn_hovered_z">2dp</dimen>
    <dimen name="mtrl_btn_icon_btn_padding_left">12dp</dimen>
    <dimen name="mtrl_btn_icon_padding">8dp</dimen>
    <dimen name="mtrl_btn_inset">6dp</dimen>
    <dimen name="mtrl_btn_letter_spacing">0.07</dimen>
    <dimen name="mtrl_btn_max_width">320dp</dimen>
    <dimen name="mtrl_btn_padding_bottom">4dp</dimen>
    <dimen name="mtrl_btn_padding_left">16dp</dimen>
    <dimen name="mtrl_btn_padding_right">16dp</dimen>
    <dimen name="mtrl_btn_padding_top">4dp</dimen>
    <dimen name="mtrl_btn_pressed_z">6dp</dimen>
    <dimen name="mtrl_btn_snackbar_margin_horizontal">8dp</dimen>
    <dimen name="mtrl_btn_stroke_size">1dp</dimen>
    <dimen name="mtrl_btn_text_btn_icon_padding">4dp</dimen>
    <dimen name="mtrl_btn_text_btn_padding_left">8dp</dimen>
    <dimen name="mtrl_btn_text_btn_padding_right">8dp</dimen>
    <dimen name="mtrl_btn_text_size">14sp</dimen>
    <dimen name="mtrl_btn_z">0dp</dimen>
    <dimen name="mtrl_calendar_action_confirm_button_min_width">64dp</dimen>
    <dimen name="mtrl_calendar_action_height">52dp</dimen>
    <dimen name="mtrl_calendar_action_padding">8dp</dimen>
    <dimen name="mtrl_calendar_bottom_padding">0dp</dimen>
    <dimen name="mtrl_calendar_content_padding">12dp</dimen>
    <dimen name="mtrl_calendar_day_corner">15dp</dimen>
    <dimen name="mtrl_calendar_day_height">32dp</dimen>
    <dimen name="mtrl_calendar_day_horizontal_padding">3dp</dimen>
    <dimen name="mtrl_calendar_day_today_stroke">1dp</dimen>
    <dimen name="mtrl_calendar_day_vertical_padding">1dp</dimen>
    <dimen name="mtrl_calendar_day_width">36dp</dimen>
    <dimen name="mtrl_calendar_days_of_week_height">24dp</dimen>
    <dimen name="mtrl_calendar_dialog_background_inset">16dp</dimen>
    <dimen name="mtrl_calendar_header_content_padding">12dp</dimen>
    <dimen name="mtrl_calendar_header_content_padding_fullscreen">4dp</dimen>
    <dimen name="mtrl_calendar_header_divider_thickness">1dp</dimen>
    <dimen name="mtrl_calendar_header_height">120dp</dimen>
    <dimen name="mtrl_calendar_header_height_fullscreen">128dp</dimen>
    <dimen name="mtrl_calendar_header_selection_line_height">32dp</dimen>
    <dimen name="mtrl_calendar_header_text_padding">12dp</dimen>
    <dimen name="mtrl_calendar_header_toggle_margin_bottom">8dp</dimen>
    <dimen name="mtrl_calendar_header_toggle_margin_top">24dp</dimen>
    <dimen name="mtrl_calendar_landscape_header_width">0dp</dimen>
    <dimen name="mtrl_calendar_maximum_default_fullscreen_minor_axis">480dp</dimen>
    <dimen name="mtrl_calendar_month_horizontal_padding">2dp</dimen>
    <dimen name="mtrl_calendar_month_vertical_padding">0dp</dimen>
    <dimen name="mtrl_calendar_navigation_bottom_padding">4dp</dimen>
    <dimen name="mtrl_calendar_navigation_height">48dp</dimen>
    <dimen name="mtrl_calendar_navigation_top_padding">4dp</dimen>
    <dimen name="mtrl_calendar_pre_l_text_clip_padding">0dp</dimen>
    <dimen name="mtrl_calendar_selection_baseline_to_top_fullscreen">104dp</dimen>
    <dimen name="mtrl_calendar_selection_text_baseline_to_bottom">20dp</dimen>
    <dimen name="mtrl_calendar_selection_text_baseline_to_bottom_fullscreen">24dp</dimen>
    <dimen name="mtrl_calendar_selection_text_baseline_to_top">100dp</dimen>
    <dimen name="mtrl_calendar_text_input_padding_top">16dp</dimen>
    <dimen name="mtrl_calendar_title_baseline_to_top">28dp</dimen>
    <dimen name="mtrl_calendar_title_baseline_to_top_fullscreen">68dp</dimen>
    <dimen name="mtrl_calendar_year_corner">18dp</dimen>
    <dimen name="mtrl_calendar_year_height">52dp</dimen>
    <dimen name="mtrl_calendar_year_horizontal_padding">8dp</dimen>
    <dimen name="mtrl_calendar_year_vertical_padding">8dp</dimen>
    <dimen name="mtrl_calendar_year_width">88dp</dimen>
    <dimen name="mtrl_card_checked_icon_margin">8dp</dimen>
    <dimen name="mtrl_card_checked_icon_size">24dp</dimen>
    <dimen name="mtrl_card_corner_radius">4dp</dimen>
    <dimen name="mtrl_card_dragged_z">5dp</dimen>
    <dimen name="mtrl_card_elevation">1dp</dimen>
    <dimen name="mtrl_card_spacing">8dp</dimen>
    <dimen name="mtrl_chip_pressed_translation_z">3dp</dimen>
    <dimen name="mtrl_chip_text_size">14sp</dimen>
    <dimen name="mtrl_exposed_dropdown_menu_popup_elevation">8dp</dimen>
    <dimen name="mtrl_exposed_dropdown_menu_popup_vertical_offset">1dp</dimen>
    <dimen name="mtrl_exposed_dropdown_menu_popup_vertical_padding">8dp</dimen>
    <dimen name="mtrl_extended_fab_bottom_padding">12dp</dimen>
    <dimen name="mtrl_extended_fab_disabled_elevation">0dp</dimen>
    <dimen name="mtrl_extended_fab_disabled_translation_z">0dp</dimen>
    <dimen name="mtrl_extended_fab_elevation">6dp</dimen>
    <dimen name="mtrl_extended_fab_end_padding">20dp</dimen>
    <dimen name="mtrl_extended_fab_end_padding_icon">20dp</dimen>
    <dimen name="mtrl_extended_fab_icon_size">24dp</dimen>
    <dimen name="mtrl_extended_fab_icon_text_spacing">12dp</dimen>
    <dimen name="mtrl_extended_fab_min_height">48dp</dimen>
    <dimen name="mtrl_extended_fab_min_width">120dp</dimen>
    <dimen name="mtrl_extended_fab_start_padding">20dp</dimen>
    <dimen name="mtrl_extended_fab_start_padding_icon">12dp</dimen>
    <dimen name="mtrl_extended_fab_top_padding">12dp</dimen>
    <dimen name="mtrl_extended_fab_translation_z_base">0dp</dimen>
    <dimen name="mtrl_extended_fab_translation_z_hovered_focused">2dp</dimen>
    <dimen name="mtrl_extended_fab_translation_z_pressed">6dp</dimen>
    <dimen name="mtrl_fab_elevation">6dp</dimen>
    <dimen name="mtrl_fab_min_touch_target">48dp</dimen>
    <dimen name="mtrl_fab_translation_z_hovered_focused">2dp</dimen>
    <dimen name="mtrl_fab_translation_z_pressed">6dp</dimen>
    <dimen name="mtrl_high_ripple_default_alpha">0.48</dimen>
    <dimen name="mtrl_high_ripple_focused_alpha">0.48</dimen>
    <dimen name="mtrl_high_ripple_hovered_alpha">0.16</dimen>
    <dimen name="mtrl_high_ripple_pressed_alpha">0.48</dimen>
    <dimen name="mtrl_low_ripple_default_alpha">0.24</dimen>
    <dimen name="mtrl_low_ripple_focused_alpha">0.24</dimen>
    <dimen name="mtrl_low_ripple_hovered_alpha">0.08</dimen>
    <dimen name="mtrl_low_ripple_pressed_alpha">0.24</dimen>
    <dimen name="mtrl_min_touch_target_size">48dp</dimen>
    <dimen name="mtrl_navigation_bar_item_default_icon_size">24dp</dimen>
    <dimen name="mtrl_navigation_bar_item_default_margin">8dp</dimen>
    <dimen name="mtrl_navigation_elevation">0dp</dimen>
    <dimen name="mtrl_navigation_item_horizontal_padding">22dp</dimen>
    <dimen name="mtrl_navigation_item_icon_padding">14dp</dimen>
    <dimen name="mtrl_navigation_item_icon_size">24dp</dimen>
    <dimen name="mtrl_navigation_item_shape_horizontal_margin">8dp</dimen>
    <dimen name="mtrl_navigation_item_shape_vertical_margin">4dp</dimen>
    <dimen name="mtrl_navigation_rail_active_text_size">14dp</dimen>
    <dimen name="mtrl_navigation_rail_compact_width">56dp</dimen>
    <dimen name="mtrl_navigation_rail_default_width">72dp</dimen>
    <dimen name="mtrl_navigation_rail_elevation">8dp</dimen>
    <dimen name="mtrl_navigation_rail_icon_margin">14dp</dimen>
    <dimen name="mtrl_navigation_rail_icon_size">24dp</dimen>
    <dimen name="mtrl_navigation_rail_margin">8dp</dimen>
    <dimen name="mtrl_navigation_rail_text_bottom_margin">16dp</dimen>
    <dimen name="mtrl_navigation_rail_text_size">12dp</dimen>
    <dimen name="mtrl_progress_circular_inset">4dp</dimen>
    <dimen name="mtrl_progress_circular_inset_extra_small">2dp</dimen>
    <dimen name="mtrl_progress_circular_inset_medium">4dp</dimen>
    <dimen name="mtrl_progress_circular_inset_small">4dp</dimen>
    <dimen name="mtrl_progress_circular_radius">18dp</dimen>
    <dimen name="mtrl_progress_circular_size">40dp</dimen>
    <dimen name="mtrl_progress_circular_size_extra_small">20dp</dimen>
    <dimen name="mtrl_progress_circular_size_medium">40dp</dimen>
    <dimen name="mtrl_progress_circular_size_small">28dp</dimen>
    <dimen name="mtrl_progress_circular_track_thickness_extra_small">2.5dp</dimen>
    <dimen name="mtrl_progress_circular_track_thickness_medium">4dp</dimen>
    <dimen name="mtrl_progress_circular_track_thickness_small">3dp</dimen>
    <dimen name="mtrl_progress_indicator_full_rounded_corner_radius">2dp</dimen>
    <dimen name="mtrl_progress_track_thickness">4dp</dimen>
    <dimen name="mtrl_shape_corner_size_large_component">0dp</dimen>
    <dimen name="mtrl_shape_corner_size_medium_component">4dp</dimen>
    <dimen name="mtrl_shape_corner_size_small_component">4dp</dimen>
    <dimen name="mtrl_slider_halo_radius">24dp</dimen>
    <dimen name="mtrl_slider_label_padding">4dp</dimen>
    <dimen name="mtrl_slider_label_radius">13dp</dimen>
    <dimen name="mtrl_slider_label_square_side">26dp</dimen>
    <dimen name="mtrl_slider_thumb_elevation">1dp</dimen>
    <dimen name="mtrl_slider_thumb_radius">10dp</dimen>
    <dimen name="mtrl_slider_track_height">4dp</dimen>
    <dimen name="mtrl_slider_track_side_padding">16dp</dimen>
    <dimen name="mtrl_slider_widget_height">48dp</dimen>
    <dimen name="mtrl_snackbar_action_text_color_alpha">0.5</dimen>
    <dimen name="mtrl_snackbar_background_corner_radius">4dp</dimen>
    <dimen name="mtrl_snackbar_background_overlay_color_alpha">0.8</dimen>
    <dimen name="mtrl_snackbar_margin">8dp</dimen>
    <dimen name="mtrl_snackbar_message_margin_horizontal">8dp</dimen>
    <dimen name="mtrl_snackbar_padding_horizontal">8dp</dimen>
    <dimen name="mtrl_switch_text_padding">16dp</dimen>
    <dimen name="mtrl_switch_thumb_elevation">4dp</dimen>
    <dimen name="mtrl_switch_thumb_size">32dp</dimen>
    <dimen name="mtrl_switch_track_height">@dimen/m3_comp_switch_track_height</dimen>
    <dimen name="mtrl_switch_track_width">@dimen/m3_comp_switch_track_width</dimen>
    <dimen name="mtrl_textinput_box_corner_radius_medium">4dp</dimen>
    <dimen name="mtrl_textinput_box_corner_radius_small">0dp</dimen>
    <dimen name="mtrl_textinput_box_label_cutout_padding">4dp</dimen>
    <dimen name="mtrl_textinput_box_stroke_width_default">1dp</dimen>
    <dimen name="mtrl_textinput_box_stroke_width_focused">2dp</dimen>
    <dimen name="mtrl_textinput_counter_margin_start">16dp</dimen>
    <dimen name="mtrl_textinput_end_icon_margin_start">4dp</dimen>
    <dimen name="mtrl_textinput_outline_box_expanded_padding">16dp</dimen>
    <dimen name="mtrl_textinput_start_icon_margin_end">4dp</dimen>
    <dimen name="mtrl_toolbar_default_height">56dp</dimen>
    <dimen name="mtrl_tooltip_arrowSize">14dp</dimen>
    <dimen name="mtrl_tooltip_cornerSize">4dp</dimen>
    <dimen name="mtrl_tooltip_minHeight">32dp</dimen>
    <dimen name="mtrl_tooltip_minWidth">32dp</dimen>
    <dimen name="mtrl_tooltip_padding">12dp</dimen>
    <dimen name="mtrl_transition_shared_axis_slide_distance">30dp</dimen>
    <dimen name="notification_action_icon_size">32dp</dimen>
    <dimen name="notification_action_text_size">13sp</dimen>
    <dimen name="notification_big_circle_margin">12dp</dimen>
    <dimen name="notification_content_margin_start">0dp</dimen>
    <dimen name="notification_large_icon_height">64dp</dimen>
    <dimen name="notification_large_icon_width">64dp</dimen>
    <dimen name="notification_main_column_padding_top">0dp</dimen>
    <dimen name="notification_media_narrow_margin">12dp</dimen>
    <dimen name="notification_right_icon_size">16dp</dimen>
    <dimen name="notification_right_side_padding_top">4dp</dimen>
    <dimen name="notification_small_icon_background_padding">3dp</dimen>
    <dimen name="notification_small_icon_size_as_large">24dp</dimen>
    <dimen name="notification_subtext_size">13sp</dimen>
    <dimen name="notification_top_pad">10dp</dimen>
    <dimen name="notification_top_pad_large_text">5dp</dimen>
    <dimen name="tooltip_corner_radius">2dp</dimen>
    <dimen name="tooltip_horizontal_padding">16dp</dimen>
    <dimen name="tooltip_margin">8dp</dimen>
    <dimen name="tooltip_precise_anchor_extra_offset">8dp</dimen>
    <dimen name="tooltip_precise_anchor_threshold">96dp</dimen>
    <dimen name="tooltip_vertical_padding">6.5dp</dimen>
    <dimen name="tooltip_y_offset_non_touch">0dp</dimen>
    <dimen name="tooltip_y_offset_touch">16dp</dimen>
</resources>

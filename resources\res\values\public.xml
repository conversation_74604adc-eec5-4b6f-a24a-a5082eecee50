<?xml version="1.0" encoding="utf-8"?>
<resources>
    <public type="anim" name="abc_fade_in" id="0x7f010000" />
    <public type="anim" name="abc_fade_out" id="0x7f010001" />
    <public type="anim" name="abc_grow_fade_in_from_bottom" id="0x7f010002" />
    <public type="anim" name="abc_popup_enter" id="0x7f010003" />
    <public type="anim" name="abc_popup_exit" id="0x7f010004" />
    <public type="anim" name="abc_shrink_fade_out_from_bottom" id="0x7f010005" />
    <public type="anim" name="abc_slide_in_bottom" id="0x7f010006" />
    <public type="anim" name="abc_slide_in_top" id="0x7f010007" />
    <public type="anim" name="abc_slide_out_bottom" id="0x7f010008" />
    <public type="anim" name="abc_slide_out_top" id="0x7f010009" />
    <public type="anim" name="abc_tooltip_enter" id="0x7f01000a" />
    <public type="anim" name="abc_tooltip_exit" id="0x7f01000b" />
    <public type="anim" name="btn_checkbox_to_checked_box_inner_merged_animation" id="0x7f01000c" />
    <public type="anim" name="btn_checkbox_to_checked_box_outer_merged_animation" id="0x7f01000d" />
    <public type="anim" name="btn_checkbox_to_checked_icon_null_animation" id="0x7f01000e" />
    <public type="anim" name="btn_checkbox_to_unchecked_box_inner_merged_animation" id="0x7f01000f" />
    <public type="anim" name="btn_checkbox_to_unchecked_check_path_merged_animation" id="0x7f010010" />
    <public type="anim" name="btn_checkbox_to_unchecked_icon_null_animation" id="0x7f010011" />
    <public type="anim" name="btn_radio_to_off_mtrl_dot_group_animation" id="0x7f010012" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_animation" id="0x7f010013" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_path_animation" id="0x7f010014" />
    <public type="anim" name="btn_radio_to_on_mtrl_dot_group_animation" id="0x7f010015" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_animation" id="0x7f010016" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_path_animation" id="0x7f010017" />
    <public type="anim" name="decelerate_cubic" id="0x7f010018" />
    <public type="anim" name="design_bottom_sheet_slide_in" id="0x7f010019" />
    <public type="anim" name="design_bottom_sheet_slide_out" id="0x7f01001a" />
    <public type="anim" name="design_snackbar_in" id="0x7f01001b" />
    <public type="anim" name="design_snackbar_out" id="0x7f01001c" />
    <public type="anim" name="fragment_fast_out_extra_slow_in" id="0x7f01001d" />
    <public type="anim" name="linear_indeterminate_line1_head_interpolator" id="0x7f01001e" />
    <public type="anim" name="linear_indeterminate_line1_tail_interpolator" id="0x7f01001f" />
    <public type="anim" name="linear_indeterminate_line2_head_interpolator" id="0x7f010020" />
    <public type="anim" name="linear_indeterminate_line2_tail_interpolator" id="0x7f010021" />
    <public type="anim" name="mtrl_bottom_sheet_slide_in" id="0x7f010022" />
    <public type="anim" name="mtrl_bottom_sheet_slide_out" id="0x7f010023" />
    <public type="anim" name="mtrl_card_lowers_interpolator" id="0x7f010024" />
    <public type="anim" name="popup_enter" id="0x7f010025" />
    <public type="anim" name="popup_exit" id="0x7f010026" />
    <public type="animator" name="design_appbar_state_list_animator" id="0x7f020000" />
    <public type="animator" name="design_fab_hide_motion_spec" id="0x7f020001" />
    <public type="animator" name="design_fab_show_motion_spec" id="0x7f020002" />
    <public type="animator" name="fragment_close_enter" id="0x7f020003" />
    <public type="animator" name="fragment_close_exit" id="0x7f020004" />
    <public type="animator" name="fragment_fade_enter" id="0x7f020005" />
    <public type="animator" name="fragment_fade_exit" id="0x7f020006" />
    <public type="animator" name="fragment_open_enter" id="0x7f020007" />
    <public type="animator" name="fragment_open_exit" id="0x7f020008" />
    <public type="animator" name="m3_btn_elevated_btn_state_list_anim" id="0x7f020009" />
    <public type="animator" name="m3_btn_state_list_anim" id="0x7f02000a" />
    <public type="animator" name="m3_card_elevated_state_list_anim" id="0x7f02000b" />
    <public type="animator" name="m3_card_state_list_anim" id="0x7f02000c" />
    <public type="animator" name="m3_chip_state_list_anim" id="0x7f02000d" />
    <public type="animator" name="m3_elevated_chip_state_list_anim" id="0x7f02000e" />
    <public type="animator" name="m3_extended_fab_hide_motion_spec" id="0x7f02000f" />
    <public type="animator" name="m3_extended_fab_show_motion_spec" id="0x7f020010" />
    <public type="animator" name="m3_extended_fab_state_list_animator" id="0x7f020011" />
    <public type="animator" name="mtrl_btn_state_list_anim" id="0x7f020012" />
    <public type="animator" name="mtrl_btn_unelevated_state_list_anim" id="0x7f020013" />
    <public type="animator" name="mtrl_card_state_list_anim" id="0x7f020014" />
    <public type="animator" name="mtrl_chip_state_list_anim" id="0x7f020015" />
    <public type="animator" name="mtrl_extended_fab_change_size_collapse_motion_spec" id="0x7f020016" />
    <public type="animator" name="mtrl_extended_fab_change_size_expand_motion_spec" id="0x7f020017" />
    <public type="animator" name="mtrl_extended_fab_hide_motion_spec" id="0x7f020018" />
    <public type="animator" name="mtrl_extended_fab_show_motion_spec" id="0x7f020019" />
    <public type="animator" name="mtrl_extended_fab_state_list_animator" id="0x7f02001a" />
    <public type="animator" name="mtrl_fab_hide_motion_spec" id="0x7f02001b" />
    <public type="animator" name="mtrl_fab_show_motion_spec" id="0x7f02001c" />
    <public type="animator" name="mtrl_fab_transformation_sheet_collapse_spec" id="0x7f02001d" />
    <public type="animator" name="mtrl_fab_transformation_sheet_expand_spec" id="0x7f02001e" />
    <public type="array" name="root_record_out_file_type_keys" id="0x7f030000" />
    <public type="array" name="root_record_out_file_type_values" id="0x7f030001" />
    <public type="attr" name="SharedValue" id="0x7f040000" />
    <public type="attr" name="SharedValueId" id="0x7f040001" />
    <public type="attr" name="actionBarDivider" id="0x7f040002" />
    <public type="attr" name="actionBarItemBackground" id="0x7f040003" />
    <public type="attr" name="actionBarPopupTheme" id="0x7f040004" />
    <public type="attr" name="actionBarSize" id="0x7f040005" />
    <public type="attr" name="actionBarSplitStyle" id="0x7f040006" />
    <public type="attr" name="actionBarStyle" id="0x7f040007" />
    <public type="attr" name="actionBarTabBarStyle" id="0x7f040008" />
    <public type="attr" name="actionBarTabStyle" id="0x7f040009" />
    <public type="attr" name="actionBarTabTextStyle" id="0x7f04000a" />
    <public type="attr" name="actionBarTheme" id="0x7f04000b" />
    <public type="attr" name="actionBarWidgetTheme" id="0x7f04000c" />
    <public type="attr" name="actionButtonStyle" id="0x7f04000d" />
    <public type="attr" name="actionDropDownStyle" id="0x7f04000e" />
    <public type="attr" name="actionLayout" id="0x7f04000f" />
    <public type="attr" name="actionMenuTextAppearance" id="0x7f040010" />
    <public type="attr" name="actionMenuTextColor" id="0x7f040011" />
    <public type="attr" name="actionModeBackground" id="0x7f040012" />
    <public type="attr" name="actionModeCloseButtonStyle" id="0x7f040013" />
    <public type="attr" name="actionModeCloseContentDescription" id="0x7f040014" />
    <public type="attr" name="actionModeCloseDrawable" id="0x7f040015" />
    <public type="attr" name="actionModeCopyDrawable" id="0x7f040016" />
    <public type="attr" name="actionModeCutDrawable" id="0x7f040017" />
    <public type="attr" name="actionModeFindDrawable" id="0x7f040018" />
    <public type="attr" name="actionModePasteDrawable" id="0x7f040019" />
    <public type="attr" name="actionModePopupWindowStyle" id="0x7f04001a" />
    <public type="attr" name="actionModeSelectAllDrawable" id="0x7f04001b" />
    <public type="attr" name="actionModeShareDrawable" id="0x7f04001c" />
    <public type="attr" name="actionModeSplitBackground" id="0x7f04001d" />
    <public type="attr" name="actionModeStyle" id="0x7f04001e" />
    <public type="attr" name="actionModeTheme" id="0x7f04001f" />
    <public type="attr" name="actionModeWebSearchDrawable" id="0x7f040020" />
    <public type="attr" name="actionOverflowButtonStyle" id="0x7f040021" />
    <public type="attr" name="actionOverflowMenuStyle" id="0x7f040022" />
    <public type="attr" name="actionProviderClass" id="0x7f040023" />
    <public type="attr" name="actionTextColorAlpha" id="0x7f040024" />
    <public type="attr" name="actionViewClass" id="0x7f040025" />
    <public type="attr" name="activityChooserViewStyle" id="0x7f040026" />
    <public type="attr" name="alertDialogButtonGroupStyle" id="0x7f040027" />
    <public type="attr" name="alertDialogCenterButtons" id="0x7f040028" />
    <public type="attr" name="alertDialogStyle" id="0x7f040029" />
    <public type="attr" name="alertDialogTheme" id="0x7f04002a" />
    <public type="attr" name="allowStacking" id="0x7f04002b" />
    <public type="attr" name="alpha" id="0x7f04002c" />
    <public type="attr" name="alphabeticModifiers" id="0x7f04002d" />
    <public type="attr" name="altSrc" id="0x7f04002e" />
    <public type="attr" name="animateCircleAngleTo" id="0x7f04002f" />
    <public type="attr" name="animateRelativeTo" id="0x7f040030" />
    <public type="attr" name="animationMode" id="0x7f040031" />
    <public type="attr" name="appBarLayoutStyle" id="0x7f040032" />
    <public type="attr" name="applyMotionScene" id="0x7f040033" />
    <public type="attr" name="arcMode" id="0x7f040034" />
    <public type="attr" name="arrowHeadLength" id="0x7f040035" />
    <public type="attr" name="arrowShaftLength" id="0x7f040036" />
    <public type="attr" name="attributeName" id="0x7f040037" />
    <public type="attr" name="autoCompleteMode" id="0x7f040038" />
    <public type="attr" name="autoCompleteTextViewStyle" id="0x7f040039" />
    <public type="attr" name="autoSizeMaxTextSize" id="0x7f04003a" />
    <public type="attr" name="autoSizeMinTextSize" id="0x7f04003b" />
    <public type="attr" name="autoSizePresetSizes" id="0x7f04003c" />
    <public type="attr" name="autoSizeStepGranularity" id="0x7f04003d" />
    <public type="attr" name="autoSizeTextType" id="0x7f04003e" />
    <public type="attr" name="autoTransition" id="0x7f04003f" />
    <public type="attr" name="background" id="0x7f040040" />
    <public type="attr" name="backgroundColor" id="0x7f040041" />
    <public type="attr" name="backgroundInsetBottom" id="0x7f040042" />
    <public type="attr" name="backgroundInsetEnd" id="0x7f040043" />
    <public type="attr" name="backgroundInsetStart" id="0x7f040044" />
    <public type="attr" name="backgroundInsetTop" id="0x7f040045" />
    <public type="attr" name="backgroundOverlayColorAlpha" id="0x7f040046" />
    <public type="attr" name="backgroundSplit" id="0x7f040047" />
    <public type="attr" name="backgroundStacked" id="0x7f040048" />
    <public type="attr" name="backgroundTint" id="0x7f040049" />
    <public type="attr" name="backgroundTintMode" id="0x7f04004a" />
    <public type="attr" name="badgeGravity" id="0x7f04004b" />
    <public type="attr" name="badgeRadius" id="0x7f04004c" />
    <public type="attr" name="badgeStyle" id="0x7f04004d" />
    <public type="attr" name="badgeTextColor" id="0x7f04004e" />
    <public type="attr" name="badgeWidePadding" id="0x7f04004f" />
    <public type="attr" name="badgeWithTextRadius" id="0x7f040050" />
    <public type="attr" name="barLength" id="0x7f040051" />
    <public type="attr" name="barrierAllowsGoneWidgets" id="0x7f040052" />
    <public type="attr" name="barrierDirection" id="0x7f040053" />
    <public type="attr" name="barrierMargin" id="0x7f040054" />
    <public type="attr" name="behavior_autoHide" id="0x7f040055" />
    <public type="attr" name="behavior_autoShrink" id="0x7f040056" />
    <public type="attr" name="behavior_draggable" id="0x7f040057" />
    <public type="attr" name="behavior_expandedOffset" id="0x7f040058" />
    <public type="attr" name="behavior_fitToContents" id="0x7f040059" />
    <public type="attr" name="behavior_halfExpandedRatio" id="0x7f04005a" />
    <public type="attr" name="behavior_hideable" id="0x7f04005b" />
    <public type="attr" name="behavior_overlapTop" id="0x7f04005c" />
    <public type="attr" name="behavior_peekHeight" id="0x7f04005d" />
    <public type="attr" name="behavior_saveFlags" id="0x7f04005e" />
    <public type="attr" name="behavior_skipCollapsed" id="0x7f04005f" />
    <public type="attr" name="blendSrc" id="0x7f040060" />
    <public type="attr" name="borderRound" id="0x7f040061" />
    <public type="attr" name="borderRoundPercent" id="0x7f040062" />
    <public type="attr" name="borderWidth" id="0x7f040063" />
    <public type="attr" name="borderlessButtonStyle" id="0x7f040064" />
    <public type="attr" name="bottomAppBarStyle" id="0x7f040065" />
    <public type="attr" name="bottomInsetScrimEnabled" id="0x7f040066" />
    <public type="attr" name="bottomNavigationStyle" id="0x7f040067" />
    <public type="attr" name="bottomSheetDialogTheme" id="0x7f040068" />
    <public type="attr" name="bottomSheetDragHandleStyle" id="0x7f040069" />
    <public type="attr" name="bottomSheetStyle" id="0x7f04006a" />
    <public type="attr" name="boxBackgroundColor" id="0x7f04006b" />
    <public type="attr" name="boxBackgroundMode" id="0x7f04006c" />
    <public type="attr" name="boxCollapsedPaddingTop" id="0x7f04006d" />
    <public type="attr" name="boxCornerRadiusBottomEnd" id="0x7f04006e" />
    <public type="attr" name="boxCornerRadiusBottomStart" id="0x7f04006f" />
    <public type="attr" name="boxCornerRadiusTopEnd" id="0x7f040070" />
    <public type="attr" name="boxCornerRadiusTopStart" id="0x7f040071" />
    <public type="attr" name="boxStrokeColor" id="0x7f040072" />
    <public type="attr" name="boxStrokeErrorColor" id="0x7f040073" />
    <public type="attr" name="boxStrokeWidth" id="0x7f040074" />
    <public type="attr" name="boxStrokeWidthFocused" id="0x7f040075" />
    <public type="attr" name="brightness" id="0x7f040076" />
    <public type="attr" name="buttonBarButtonStyle" id="0x7f040077" />
    <public type="attr" name="buttonBarNegativeButtonStyle" id="0x7f040078" />
    <public type="attr" name="buttonBarNeutralButtonStyle" id="0x7f040079" />
    <public type="attr" name="buttonBarPositiveButtonStyle" id="0x7f04007a" />
    <public type="attr" name="buttonBarStyle" id="0x7f04007b" />
    <public type="attr" name="buttonCompat" id="0x7f04007c" />
    <public type="attr" name="buttonGravity" id="0x7f04007d" />
    <public type="attr" name="buttonIcon" id="0x7f04007e" />
    <public type="attr" name="buttonIconDimen" id="0x7f04007f" />
    <public type="attr" name="buttonIconTint" id="0x7f040080" />
    <public type="attr" name="buttonIconTintMode" id="0x7f040081" />
    <public type="attr" name="buttonPanelSideLayout" id="0x7f040082" />
    <public type="attr" name="buttonStyle" id="0x7f040083" />
    <public type="attr" name="buttonStyleSmall" id="0x7f040084" />
    <public type="attr" name="buttonTint" id="0x7f040085" />
    <public type="attr" name="buttonTintMode" id="0x7f040086" />
    <public type="attr" name="camera_id" id="0x7f040087" />
    <public type="attr" name="cardBackgroundColor" id="0x7f040088" />
    <public type="attr" name="cardCornerRadius" id="0x7f040089" />
    <public type="attr" name="cardElevation" id="0x7f04008a" />
    <public type="attr" name="cardForegroundColor" id="0x7f04008b" />
    <public type="attr" name="cardMaxElevation" id="0x7f04008c" />
    <public type="attr" name="cardPreventCornerOverlap" id="0x7f04008d" />
    <public type="attr" name="cardUseCompatPadding" id="0x7f04008e" />
    <public type="attr" name="cardViewStyle" id="0x7f04008f" />
    <public type="attr" name="carousel_backwardTransition" id="0x7f040090" />
    <public type="attr" name="carousel_emptyViewsBehavior" id="0x7f040091" />
    <public type="attr" name="carousel_firstView" id="0x7f040092" />
    <public type="attr" name="carousel_forwardTransition" id="0x7f040093" />
    <public type="attr" name="carousel_infinite" id="0x7f040094" />
    <public type="attr" name="carousel_nextState" id="0x7f040095" />
    <public type="attr" name="carousel_previousState" id="0x7f040096" />
    <public type="attr" name="carousel_touchUpMode" id="0x7f040097" />
    <public type="attr" name="carousel_touchUp_dampeningFactor" id="0x7f040098" />
    <public type="attr" name="carousel_touchUp_velocityThreshold" id="0x7f040099" />
    <public type="attr" name="centerIfNoTextEnabled" id="0x7f04009a" />
    <public type="attr" name="chainUseRtl" id="0x7f04009b" />
    <public type="attr" name="checkMarkCompat" id="0x7f04009c" />
    <public type="attr" name="checkMarkTint" id="0x7f04009d" />
    <public type="attr" name="checkMarkTintMode" id="0x7f04009e" />
    <public type="attr" name="checkboxStyle" id="0x7f04009f" />
    <public type="attr" name="checkedButton" id="0x7f0400a0" />
    <public type="attr" name="checkedChip" id="0x7f0400a1" />
    <public type="attr" name="checkedIcon" id="0x7f0400a2" />
    <public type="attr" name="checkedIconEnabled" id="0x7f0400a3" />
    <public type="attr" name="checkedIconGravity" id="0x7f0400a4" />
    <public type="attr" name="checkedIconMargin" id="0x7f0400a5" />
    <public type="attr" name="checkedIconSize" id="0x7f0400a6" />
    <public type="attr" name="checkedIconTint" id="0x7f0400a7" />
    <public type="attr" name="checkedIconVisible" id="0x7f0400a8" />
    <public type="attr" name="checkedState" id="0x7f0400a9" />
    <public type="attr" name="checkedTextViewStyle" id="0x7f0400aa" />
    <public type="attr" name="chipBackgroundColor" id="0x7f0400ab" />
    <public type="attr" name="chipCornerRadius" id="0x7f0400ac" />
    <public type="attr" name="chipEndPadding" id="0x7f0400ad" />
    <public type="attr" name="chipGroupStyle" id="0x7f0400ae" />
    <public type="attr" name="chipIcon" id="0x7f0400af" />
    <public type="attr" name="chipIconEnabled" id="0x7f0400b0" />
    <public type="attr" name="chipIconSize" id="0x7f0400b1" />
    <public type="attr" name="chipIconTint" id="0x7f0400b2" />
    <public type="attr" name="chipIconVisible" id="0x7f0400b3" />
    <public type="attr" name="chipMinHeight" id="0x7f0400b4" />
    <public type="attr" name="chipMinTouchTargetSize" id="0x7f0400b5" />
    <public type="attr" name="chipSpacing" id="0x7f0400b6" />
    <public type="attr" name="chipSpacingHorizontal" id="0x7f0400b7" />
    <public type="attr" name="chipSpacingVertical" id="0x7f0400b8" />
    <public type="attr" name="chipStandaloneStyle" id="0x7f0400b9" />
    <public type="attr" name="chipStartPadding" id="0x7f0400ba" />
    <public type="attr" name="chipStrokeColor" id="0x7f0400bb" />
    <public type="attr" name="chipStrokeWidth" id="0x7f0400bc" />
    <public type="attr" name="chipStyle" id="0x7f0400bd" />
    <public type="attr" name="chipSurfaceColor" id="0x7f0400be" />
    <public type="attr" name="circleRadius" id="0x7f0400bf" />
    <public type="attr" name="circularProgressIndicatorStyle" id="0x7f0400c0" />
    <public type="attr" name="circularflow_angles" id="0x7f0400c1" />
    <public type="attr" name="circularflow_defaultAngle" id="0x7f0400c2" />
    <public type="attr" name="circularflow_defaultRadius" id="0x7f0400c3" />
    <public type="attr" name="circularflow_radiusInDP" id="0x7f0400c4" />
    <public type="attr" name="circularflow_viewCenter" id="0x7f0400c5" />
    <public type="attr" name="clearsTag" id="0x7f0400c6" />
    <public type="attr" name="clickAction" id="0x7f0400c7" />
    <public type="attr" name="clockFaceBackgroundColor" id="0x7f0400c8" />
    <public type="attr" name="clockHandColor" id="0x7f0400c9" />
    <public type="attr" name="clockIcon" id="0x7f0400ca" />
    <public type="attr" name="clockNumberTextColor" id="0x7f0400cb" />
    <public type="attr" name="closeIcon" id="0x7f0400cc" />
    <public type="attr" name="closeIconEnabled" id="0x7f0400cd" />
    <public type="attr" name="closeIconEndPadding" id="0x7f0400ce" />
    <public type="attr" name="closeIconSize" id="0x7f0400cf" />
    <public type="attr" name="closeIconStartPadding" id="0x7f0400d0" />
    <public type="attr" name="closeIconTint" id="0x7f0400d1" />
    <public type="attr" name="closeIconVisible" id="0x7f0400d2" />
    <public type="attr" name="closeItemLayout" id="0x7f0400d3" />
    <public type="attr" name="collapseContentDescription" id="0x7f0400d4" />
    <public type="attr" name="collapseIcon" id="0x7f0400d5" />
    <public type="attr" name="collapsedSize" id="0x7f0400d6" />
    <public type="attr" name="collapsedTitleGravity" id="0x7f0400d7" />
    <public type="attr" name="collapsedTitleTextAppearance" id="0x7f0400d8" />
    <public type="attr" name="collapsedTitleTextColor" id="0x7f0400d9" />
    <public type="attr" name="collapsingToolbarLayoutLargeSize" id="0x7f0400da" />
    <public type="attr" name="collapsingToolbarLayoutLargeStyle" id="0x7f0400db" />
    <public type="attr" name="collapsingToolbarLayoutMediumSize" id="0x7f0400dc" />
    <public type="attr" name="collapsingToolbarLayoutMediumStyle" id="0x7f0400dd" />
    <public type="attr" name="collapsingToolbarLayoutStyle" id="0x7f0400de" />
    <public type="attr" name="color" id="0x7f0400df" />
    <public type="attr" name="colorAccent" id="0x7f0400e0" />
    <public type="attr" name="colorBackgroundFloating" id="0x7f0400e1" />
    <public type="attr" name="colorButtonNormal" id="0x7f0400e2" />
    <public type="attr" name="colorContainer" id="0x7f0400e3" />
    <public type="attr" name="colorControlActivated" id="0x7f0400e4" />
    <public type="attr" name="colorControlHighlight" id="0x7f0400e5" />
    <public type="attr" name="colorControlNormal" id="0x7f0400e6" />
    <public type="attr" name="colorError" id="0x7f0400e7" />
    <public type="attr" name="colorErrorContainer" id="0x7f0400e8" />
    <public type="attr" name="colorOnBackground" id="0x7f0400e9" />
    <public type="attr" name="colorOnContainer" id="0x7f0400ea" />
    <public type="attr" name="colorOnContainerUnchecked" id="0x7f0400eb" />
    <public type="attr" name="colorOnError" id="0x7f0400ec" />
    <public type="attr" name="colorOnErrorContainer" id="0x7f0400ed" />
    <public type="attr" name="colorOnPrimary" id="0x7f0400ee" />
    <public type="attr" name="colorOnPrimaryContainer" id="0x7f0400ef" />
    <public type="attr" name="colorOnPrimarySurface" id="0x7f0400f0" />
    <public type="attr" name="colorOnSecondary" id="0x7f0400f1" />
    <public type="attr" name="colorOnSecondaryContainer" id="0x7f0400f2" />
    <public type="attr" name="colorOnSurface" id="0x7f0400f3" />
    <public type="attr" name="colorOnSurfaceInverse" id="0x7f0400f4" />
    <public type="attr" name="colorOnSurfaceVariant" id="0x7f0400f5" />
    <public type="attr" name="colorOnTertiary" id="0x7f0400f6" />
    <public type="attr" name="colorOnTertiaryContainer" id="0x7f0400f7" />
    <public type="attr" name="colorOutline" id="0x7f0400f8" />
    <public type="attr" name="colorPrimary" id="0x7f0400f9" />
    <public type="attr" name="colorPrimaryContainer" id="0x7f0400fa" />
    <public type="attr" name="colorPrimaryDark" id="0x7f0400fb" />
    <public type="attr" name="colorPrimaryInverse" id="0x7f0400fc" />
    <public type="attr" name="colorPrimarySurface" id="0x7f0400fd" />
    <public type="attr" name="colorPrimaryVariant" id="0x7f0400fe" />
    <public type="attr" name="colorSecondary" id="0x7f0400ff" />
    <public type="attr" name="colorSecondaryContainer" id="0x7f040100" />
    <public type="attr" name="colorSecondaryVariant" id="0x7f040101" />
    <public type="attr" name="colorSurface" id="0x7f040102" />
    <public type="attr" name="colorSurfaceInverse" id="0x7f040103" />
    <public type="attr" name="colorSurfaceVariant" id="0x7f040104" />
    <public type="attr" name="colorSwitchThumbNormal" id="0x7f040105" />
    <public type="attr" name="colorTertiary" id="0x7f040106" />
    <public type="attr" name="colorTertiaryContainer" id="0x7f040107" />
    <public type="attr" name="color_assert" id="0x7f040108" />
    <public type="attr" name="color_debug" id="0x7f040109" />
    <public type="attr" name="color_error" id="0x7f04010a" />
    <public type="attr" name="color_info" id="0x7f04010b" />
    <public type="attr" name="color_verbose" id="0x7f04010c" />
    <public type="attr" name="color_warn" id="0x7f04010d" />
    <public type="attr" name="commitIcon" id="0x7f04010e" />
    <public type="attr" name="constraintRotate" id="0x7f04010f" />
    <public type="attr" name="constraintSet" id="0x7f040110" />
    <public type="attr" name="constraintSetEnd" id="0x7f040111" />
    <public type="attr" name="constraintSetStart" id="0x7f040112" />
    <public type="attr" name="constraint_referenced_ids" id="0x7f040113" />
    <public type="attr" name="constraint_referenced_tags" id="0x7f040114" />
    <public type="attr" name="constraints" id="0x7f040115" />
    <public type="attr" name="content" id="0x7f040116" />
    <public type="attr" name="contentDescription" id="0x7f040117" />
    <public type="attr" name="contentInsetEnd" id="0x7f040118" />
    <public type="attr" name="contentInsetEndWithActions" id="0x7f040119" />
    <public type="attr" name="contentInsetLeft" id="0x7f04011a" />
    <public type="attr" name="contentInsetRight" id="0x7f04011b" />
    <public type="attr" name="contentInsetStart" id="0x7f04011c" />
    <public type="attr" name="contentInsetStartWithNavigation" id="0x7f04011d" />
    <public type="attr" name="contentPadding" id="0x7f04011e" />
    <public type="attr" name="contentPaddingBottom" id="0x7f04011f" />
    <public type="attr" name="contentPaddingEnd" id="0x7f040120" />
    <public type="attr" name="contentPaddingLeft" id="0x7f040121" />
    <public type="attr" name="contentPaddingRight" id="0x7f040122" />
    <public type="attr" name="contentPaddingStart" id="0x7f040123" />
    <public type="attr" name="contentPaddingTop" id="0x7f040124" />
    <public type="attr" name="contentScrim" id="0x7f040125" />
    <public type="attr" name="contrast" id="0x7f040126" />
    <public type="attr" name="controlBackground" id="0x7f040127" />
    <public type="attr" name="coordinatorLayoutStyle" id="0x7f040128" />
    <public type="attr" name="cornerFamily" id="0x7f040129" />
    <public type="attr" name="cornerFamilyBottomLeft" id="0x7f04012a" />
    <public type="attr" name="cornerFamilyBottomRight" id="0x7f04012b" />
    <public type="attr" name="cornerFamilyTopLeft" id="0x7f04012c" />
    <public type="attr" name="cornerFamilyTopRight" id="0x7f04012d" />
    <public type="attr" name="cornerRadius" id="0x7f04012e" />
    <public type="attr" name="cornerSize" id="0x7f04012f" />
    <public type="attr" name="cornerSizeBottomLeft" id="0x7f040130" />
    <public type="attr" name="cornerSizeBottomRight" id="0x7f040131" />
    <public type="attr" name="cornerSizeTopLeft" id="0x7f040132" />
    <public type="attr" name="cornerSizeTopRight" id="0x7f040133" />
    <public type="attr" name="counterEnabled" id="0x7f040134" />
    <public type="attr" name="counterMaxLength" id="0x7f040135" />
    <public type="attr" name="counterOverflowTextAppearance" id="0x7f040136" />
    <public type="attr" name="counterOverflowTextColor" id="0x7f040137" />
    <public type="attr" name="counterTextAppearance" id="0x7f040138" />
    <public type="attr" name="counterTextColor" id="0x7f040139" />
    <public type="attr" name="crossfade" id="0x7f04013a" />
    <public type="attr" name="currentState" id="0x7f04013b" />
    <public type="attr" name="curveFit" id="0x7f04013c" />
    <public type="attr" name="customBoolean" id="0x7f04013d" />
    <public type="attr" name="customColorDrawableValue" id="0x7f04013e" />
    <public type="attr" name="customColorValue" id="0x7f04013f" />
    <public type="attr" name="customDimension" id="0x7f040140" />
    <public type="attr" name="customFloatValue" id="0x7f040141" />
    <public type="attr" name="customIntegerValue" id="0x7f040142" />
    <public type="attr" name="customNavigationLayout" id="0x7f040143" />
    <public type="attr" name="customPixelDimension" id="0x7f040144" />
    <public type="attr" name="customReference" id="0x7f040145" />
    <public type="attr" name="customStringValue" id="0x7f040146" />
    <public type="attr" name="dayInvalidStyle" id="0x7f040147" />
    <public type="attr" name="daySelectedStyle" id="0x7f040148" />
    <public type="attr" name="dayStyle" id="0x7f040149" />
    <public type="attr" name="dayTodayStyle" id="0x7f04014a" />
    <public type="attr" name="defaultDuration" id="0x7f04014b" />
    <public type="attr" name="defaultQueryHint" id="0x7f04014c" />
    <public type="attr" name="defaultState" id="0x7f04014d" />
    <public type="attr" name="deltaPolarAngle" id="0x7f04014e" />
    <public type="attr" name="deltaPolarRadius" id="0x7f04014f" />
    <public type="attr" name="deriveConstraintsFrom" id="0x7f040150" />
    <public type="attr" name="dialogCornerRadius" id="0x7f040151" />
    <public type="attr" name="dialogPreferredPadding" id="0x7f040152" />
    <public type="attr" name="dialogTheme" id="0x7f040153" />
    <public type="attr" name="displayOptions" id="0x7f040154" />
    <public type="attr" name="divider" id="0x7f040155" />
    <public type="attr" name="dividerColor" id="0x7f040156" />
    <public type="attr" name="dividerHorizontal" id="0x7f040157" />
    <public type="attr" name="dividerInsetEnd" id="0x7f040158" />
    <public type="attr" name="dividerInsetStart" id="0x7f040159" />
    <public type="attr" name="dividerPadding" id="0x7f04015a" />
    <public type="attr" name="dividerThickness" id="0x7f04015b" />
    <public type="attr" name="dividerVertical" id="0x7f04015c" />
    <public type="attr" name="dragDirection" id="0x7f04015d" />
    <public type="attr" name="dragScale" id="0x7f04015e" />
    <public type="attr" name="dragThreshold" id="0x7f04015f" />
    <public type="attr" name="drawPath" id="0x7f040160" />
    <public type="attr" name="drawableBottomCompat" id="0x7f040161" />
    <public type="attr" name="drawableEndCompat" id="0x7f040162" />
    <public type="attr" name="drawableLeftCompat" id="0x7f040163" />
    <public type="attr" name="drawableRightCompat" id="0x7f040164" />
    <public type="attr" name="drawableSize" id="0x7f040165" />
    <public type="attr" name="drawableStartCompat" id="0x7f040166" />
    <public type="attr" name="drawableTint" id="0x7f040167" />
    <public type="attr" name="drawableTintMode" id="0x7f040168" />
    <public type="attr" name="drawableTopCompat" id="0x7f040169" />
    <public type="attr" name="drawerArrowStyle" id="0x7f04016a" />
    <public type="attr" name="drawerLayoutCornerSize" id="0x7f04016b" />
    <public type="attr" name="drawerLayoutStyle" id="0x7f04016c" />
    <public type="attr" name="dropDownListViewStyle" id="0x7f04016d" />
    <public type="attr" name="dropdownListPreferredItemHeight" id="0x7f04016e" />
    <public type="attr" name="duration" id="0x7f04016f" />
    <public type="attr" name="dynamicColorThemeOverlay" id="0x7f040170" />
    <public type="attr" name="editTextBackground" id="0x7f040171" />
    <public type="attr" name="editTextColor" id="0x7f040172" />
    <public type="attr" name="editTextStyle" id="0x7f040173" />
    <public type="attr" name="elevation" id="0x7f040174" />
    <public type="attr" name="elevationOverlayAccentColor" id="0x7f040175" />
    <public type="attr" name="elevationOverlayColor" id="0x7f040176" />
    <public type="attr" name="elevationOverlayEnabled" id="0x7f040177" />
    <public type="attr" name="emojiCompatEnabled" id="0x7f040178" />
    <public type="attr" name="enableEdgeToEdge" id="0x7f040179" />
    <public type="attr" name="endIconCheckable" id="0x7f04017a" />
    <public type="attr" name="endIconContentDescription" id="0x7f04017b" />
    <public type="attr" name="endIconDrawable" id="0x7f04017c" />
    <public type="attr" name="endIconMode" id="0x7f04017d" />
    <public type="attr" name="endIconTint" id="0x7f04017e" />
    <public type="attr" name="endIconTintMode" id="0x7f04017f" />
    <public type="attr" name="enforceMaterialTheme" id="0x7f040180" />
    <public type="attr" name="enforceTextAppearance" id="0x7f040181" />
    <public type="attr" name="ensureMinTouchTargetSize" id="0x7f040182" />
    <public type="attr" name="errorAccessibilityLabel" id="0x7f040183" />
    <public type="attr" name="errorContentDescription" id="0x7f040184" />
    <public type="attr" name="errorEnabled" id="0x7f040185" />
    <public type="attr" name="errorIconDrawable" id="0x7f040186" />
    <public type="attr" name="errorIconTint" id="0x7f040187" />
    <public type="attr" name="errorIconTintMode" id="0x7f040188" />
    <public type="attr" name="errorShown" id="0x7f040189" />
    <public type="attr" name="errorTextAppearance" id="0x7f04018a" />
    <public type="attr" name="errorTextColor" id="0x7f04018b" />
    <public type="attr" name="expandActivityOverflowButtonDrawable" id="0x7f04018c" />
    <public type="attr" name="expanded" id="0x7f04018d" />
    <public type="attr" name="expandedHintEnabled" id="0x7f04018e" />
    <public type="attr" name="expandedTitleGravity" id="0x7f04018f" />
    <public type="attr" name="expandedTitleMargin" id="0x7f040190" />
    <public type="attr" name="expandedTitleMarginBottom" id="0x7f040191" />
    <public type="attr" name="expandedTitleMarginEnd" id="0x7f040192" />
    <public type="attr" name="expandedTitleMarginStart" id="0x7f040193" />
    <public type="attr" name="expandedTitleMarginTop" id="0x7f040194" />
    <public type="attr" name="expandedTitleTextAppearance" id="0x7f040195" />
    <public type="attr" name="expandedTitleTextColor" id="0x7f040196" />
    <public type="attr" name="extendMotionSpec" id="0x7f040197" />
    <public type="attr" name="extendedFloatingActionButtonPrimaryStyle" id="0x7f040198" />
    <public type="attr" name="extendedFloatingActionButtonSecondaryStyle" id="0x7f040199" />
    <public type="attr" name="extendedFloatingActionButtonStyle" id="0x7f04019a" />
    <public type="attr" name="extendedFloatingActionButtonSurfaceStyle" id="0x7f04019b" />
    <public type="attr" name="extendedFloatingActionButtonTertiaryStyle" id="0x7f04019c" />
    <public type="attr" name="extraMultilineHeightEnabled" id="0x7f04019d" />
    <public type="attr" name="fabAlignmentMode" id="0x7f04019e" />
    <public type="attr" name="fabAlignmentModeEndMargin" id="0x7f04019f" />
    <public type="attr" name="fabAnchorMode" id="0x7f0401a0" />
    <public type="attr" name="fabAnimationMode" id="0x7f0401a1" />
    <public type="attr" name="fabCradleMargin" id="0x7f0401a2" />
    <public type="attr" name="fabCradleRoundedCornerRadius" id="0x7f0401a3" />
    <public type="attr" name="fabCradleVerticalOffset" id="0x7f0401a4" />
    <public type="attr" name="fabCustomSize" id="0x7f0401a5" />
    <public type="attr" name="fabSize" id="0x7f0401a6" />
    <public type="attr" name="fastScrollEnabled" id="0x7f0401a7" />
    <public type="attr" name="fastScrollHorizontalThumbDrawable" id="0x7f0401a8" />
    <public type="attr" name="fastScrollHorizontalTrackDrawable" id="0x7f0401a9" />
    <public type="attr" name="fastScrollVerticalThumbDrawable" id="0x7f0401aa" />
    <public type="attr" name="fastScrollVerticalTrackDrawable" id="0x7f0401ab" />
    <public type="attr" name="firstBaselineToTopHeight" id="0x7f0401ac" />
    <public type="attr" name="floatingActionButtonLargePrimaryStyle" id="0x7f0401ad" />
    <public type="attr" name="floatingActionButtonLargeSecondaryStyle" id="0x7f0401ae" />
    <public type="attr" name="floatingActionButtonLargeStyle" id="0x7f0401af" />
    <public type="attr" name="floatingActionButtonLargeSurfaceStyle" id="0x7f0401b0" />
    <public type="attr" name="floatingActionButtonLargeTertiaryStyle" id="0x7f0401b1" />
    <public type="attr" name="floatingActionButtonPrimaryStyle" id="0x7f0401b2" />
    <public type="attr" name="floatingActionButtonSecondaryStyle" id="0x7f0401b3" />
    <public type="attr" name="floatingActionButtonSmallPrimaryStyle" id="0x7f0401b4" />
    <public type="attr" name="floatingActionButtonSmallSecondaryStyle" id="0x7f0401b5" />
    <public type="attr" name="floatingActionButtonSmallStyle" id="0x7f0401b6" />
    <public type="attr" name="floatingActionButtonSmallSurfaceStyle" id="0x7f0401b7" />
    <public type="attr" name="floatingActionButtonSmallTertiaryStyle" id="0x7f0401b8" />
    <public type="attr" name="floatingActionButtonStyle" id="0x7f0401b9" />
    <public type="attr" name="floatingActionButtonSurfaceStyle" id="0x7f0401ba" />
    <public type="attr" name="floatingActionButtonTertiaryStyle" id="0x7f0401bb" />
    <public type="attr" name="flow_firstHorizontalBias" id="0x7f0401bc" />
    <public type="attr" name="flow_firstHorizontalStyle" id="0x7f0401bd" />
    <public type="attr" name="flow_firstVerticalBias" id="0x7f0401be" />
    <public type="attr" name="flow_firstVerticalStyle" id="0x7f0401bf" />
    <public type="attr" name="flow_horizontalAlign" id="0x7f0401c0" />
    <public type="attr" name="flow_horizontalBias" id="0x7f0401c1" />
    <public type="attr" name="flow_horizontalGap" id="0x7f0401c2" />
    <public type="attr" name="flow_horizontalStyle" id="0x7f0401c3" />
    <public type="attr" name="flow_lastHorizontalBias" id="0x7f0401c4" />
    <public type="attr" name="flow_lastHorizontalStyle" id="0x7f0401c5" />
    <public type="attr" name="flow_lastVerticalBias" id="0x7f0401c6" />
    <public type="attr" name="flow_lastVerticalStyle" id="0x7f0401c7" />
    <public type="attr" name="flow_maxElementsWrap" id="0x7f0401c8" />
    <public type="attr" name="flow_padding" id="0x7f0401c9" />
    <public type="attr" name="flow_verticalAlign" id="0x7f0401ca" />
    <public type="attr" name="flow_verticalBias" id="0x7f0401cb" />
    <public type="attr" name="flow_verticalGap" id="0x7f0401cc" />
    <public type="attr" name="flow_verticalStyle" id="0x7f0401cd" />
    <public type="attr" name="flow_wrapMode" id="0x7f0401ce" />
    <public type="attr" name="font" id="0x7f0401cf" />
    <public type="attr" name="fontFamily" id="0x7f0401d0" />
    <public type="attr" name="fontProviderAuthority" id="0x7f0401d1" />
    <public type="attr" name="fontProviderCerts" id="0x7f0401d2" />
    <public type="attr" name="fontProviderFetchStrategy" id="0x7f0401d3" />
    <public type="attr" name="fontProviderFetchTimeout" id="0x7f0401d4" />
    <public type="attr" name="fontProviderPackage" id="0x7f0401d5" />
    <public type="attr" name="fontProviderQuery" id="0x7f0401d6" />
    <public type="attr" name="fontProviderSystemFontFamily" id="0x7f0401d7" />
    <public type="attr" name="fontStyle" id="0x7f0401d8" />
    <public type="attr" name="fontVariationSettings" id="0x7f0401d9" />
    <public type="attr" name="fontWeight" id="0x7f0401da" />
    <public type="attr" name="forceApplySystemWindowInsetTop" id="0x7f0401db" />
    <public type="attr" name="foregroundInsidePadding" id="0x7f0401dc" />
    <public type="attr" name="framePosition" id="0x7f0401dd" />
    <public type="attr" name="gapBetweenBars" id="0x7f0401de" />
    <public type="attr" name="gestureInsetBottomIgnored" id="0x7f0401df" />
    <public type="attr" name="goIcon" id="0x7f0401e0" />
    <public type="attr" name="guidelineUseRtl" id="0x7f0401e1" />
    <public type="attr" name="haloColor" id="0x7f0401e2" />
    <public type="attr" name="haloRadius" id="0x7f0401e3" />
    <public type="attr" name="headerLayout" id="0x7f0401e4" />
    <public type="attr" name="height" id="0x7f0401e5" />
    <public type="attr" name="helperText" id="0x7f0401e6" />
    <public type="attr" name="helperTextEnabled" id="0x7f0401e7" />
    <public type="attr" name="helperTextTextAppearance" id="0x7f0401e8" />
    <public type="attr" name="helperTextTextColor" id="0x7f0401e9" />
    <public type="attr" name="hideAnimationBehavior" id="0x7f0401ea" />
    <public type="attr" name="hideMotionSpec" id="0x7f0401eb" />
    <public type="attr" name="hideOnContentScroll" id="0x7f0401ec" />
    <public type="attr" name="hideOnScroll" id="0x7f0401ed" />
    <public type="attr" name="hintAnimationEnabled" id="0x7f0401ee" />
    <public type="attr" name="hintEnabled" id="0x7f0401ef" />
    <public type="attr" name="hintTextAppearance" id="0x7f0401f0" />
    <public type="attr" name="hintTextColor" id="0x7f0401f1" />
    <public type="attr" name="homeAsUpIndicator" id="0x7f0401f2" />
    <public type="attr" name="homeLayout" id="0x7f0401f3" />
    <public type="attr" name="horizontalOffset" id="0x7f0401f4" />
    <public type="attr" name="horizontalOffsetWithText" id="0x7f0401f5" />
    <public type="attr" name="hoveredFocusedTranslationZ" id="0x7f0401f6" />
    <public type="attr" name="icon" id="0x7f0401f7" />
    <public type="attr" name="iconEndPadding" id="0x7f0401f8" />
    <public type="attr" name="iconGravity" id="0x7f0401f9" />
    <public type="attr" name="iconPadding" id="0x7f0401fa" />
    <public type="attr" name="iconSize" id="0x7f0401fb" />
    <public type="attr" name="iconStartPadding" id="0x7f0401fc" />
    <public type="attr" name="iconTint" id="0x7f0401fd" />
    <public type="attr" name="iconTintMode" id="0x7f0401fe" />
    <public type="attr" name="iconifiedByDefault" id="0x7f0401ff" />
    <public type="attr" name="ifTagNotSet" id="0x7f040200" />
    <public type="attr" name="ifTagSet" id="0x7f040201" />
    <public type="attr" name="imageButtonStyle" id="0x7f040202" />
    <public type="attr" name="imagePanX" id="0x7f040203" />
    <public type="attr" name="imagePanY" id="0x7f040204" />
    <public type="attr" name="imageRotate" id="0x7f040205" />
    <public type="attr" name="imageZoom" id="0x7f040206" />
    <public type="attr" name="indeterminateAnimationType" id="0x7f040207" />
    <public type="attr" name="indeterminateProgressStyle" id="0x7f040208" />
    <public type="attr" name="indicatorColor" id="0x7f040209" />
    <public type="attr" name="indicatorDirectionCircular" id="0x7f04020a" />
    <public type="attr" name="indicatorDirectionLinear" id="0x7f04020b" />
    <public type="attr" name="indicatorInset" id="0x7f04020c" />
    <public type="attr" name="indicatorSize" id="0x7f04020d" />
    <public type="attr" name="initialActivityCount" id="0x7f04020e" />
    <public type="attr" name="insetForeground" id="0x7f04020f" />
    <public type="attr" name="isLightTheme" id="0x7f040210" />
    <public type="attr" name="isMaterial3DynamicColorApplied" id="0x7f040211" />
    <public type="attr" name="isMaterial3Theme" id="0x7f040212" />
    <public type="attr" name="isMaterialTheme" id="0x7f040213" />
    <public type="attr" name="itemActiveIndicatorStyle" id="0x7f040214" />
    <public type="attr" name="itemBackground" id="0x7f040215" />
    <public type="attr" name="itemFillColor" id="0x7f040216" />
    <public type="attr" name="itemHorizontalPadding" id="0x7f040217" />
    <public type="attr" name="itemHorizontalTranslationEnabled" id="0x7f040218" />
    <public type="attr" name="itemIconPadding" id="0x7f040219" />
    <public type="attr" name="itemIconSize" id="0x7f04021a" />
    <public type="attr" name="itemIconTint" id="0x7f04021b" />
    <public type="attr" name="itemMaxLines" id="0x7f04021c" />
    <public type="attr" name="itemMinHeight" id="0x7f04021d" />
    <public type="attr" name="itemPadding" id="0x7f04021e" />
    <public type="attr" name="itemPaddingBottom" id="0x7f04021f" />
    <public type="attr" name="itemPaddingTop" id="0x7f040220" />
    <public type="attr" name="itemRippleColor" id="0x7f040221" />
    <public type="attr" name="itemShapeAppearance" id="0x7f040222" />
    <public type="attr" name="itemShapeAppearanceOverlay" id="0x7f040223" />
    <public type="attr" name="itemShapeFillColor" id="0x7f040224" />
    <public type="attr" name="itemShapeInsetBottom" id="0x7f040225" />
    <public type="attr" name="itemShapeInsetEnd" id="0x7f040226" />
    <public type="attr" name="itemShapeInsetStart" id="0x7f040227" />
    <public type="attr" name="itemShapeInsetTop" id="0x7f040228" />
    <public type="attr" name="itemSpacing" id="0x7f040229" />
    <public type="attr" name="itemStrokeColor" id="0x7f04022a" />
    <public type="attr" name="itemStrokeWidth" id="0x7f04022b" />
    <public type="attr" name="itemTextAppearance" id="0x7f04022c" />
    <public type="attr" name="itemTextAppearanceActive" id="0x7f04022d" />
    <public type="attr" name="itemTextAppearanceInactive" id="0x7f04022e" />
    <public type="attr" name="itemTextColor" id="0x7f04022f" />
    <public type="attr" name="itemVerticalPadding" id="0x7f040230" />
    <public type="attr" name="keyPositionType" id="0x7f040231" />
    <public type="attr" name="keyboardIcon" id="0x7f040232" />
    <public type="attr" name="keylines" id="0x7f040233" />
    <public type="attr" name="lStar" id="0x7f040234" />
    <public type="attr" name="labelBehavior" id="0x7f040235" />
    <public type="attr" name="labelStyle" id="0x7f040236" />
    <public type="attr" name="labelVisibilityMode" id="0x7f040237" />
    <public type="attr" name="lastBaselineToBottomHeight" id="0x7f040238" />
    <public type="attr" name="lastItemDecorated" id="0x7f040239" />
    <public type="attr" name="layout" id="0x7f04023a" />
    <public type="attr" name="layoutDescription" id="0x7f04023b" />
    <public type="attr" name="layoutDuringTransition" id="0x7f04023c" />
    <public type="attr" name="layoutManager" id="0x7f04023d" />
    <public type="attr" name="layout_anchor" id="0x7f04023e" />
    <public type="attr" name="layout_anchorGravity" id="0x7f04023f" />
    <public type="attr" name="layout_behavior" id="0x7f040240" />
    <public type="attr" name="layout_collapseMode" id="0x7f040241" />
    <public type="attr" name="layout_collapseParallaxMultiplier" id="0x7f040242" />
    <public type="attr" name="layout_constrainedHeight" id="0x7f040243" />
    <public type="attr" name="layout_constrainedWidth" id="0x7f040244" />
    <public type="attr" name="layout_constraintBaseline_creator" id="0x7f040245" />
    <public type="attr" name="layout_constraintBaseline_toBaselineOf" id="0x7f040246" />
    <public type="attr" name="layout_constraintBaseline_toBottomOf" id="0x7f040247" />
    <public type="attr" name="layout_constraintBaseline_toTopOf" id="0x7f040248" />
    <public type="attr" name="layout_constraintBottom_creator" id="0x7f040249" />
    <public type="attr" name="layout_constraintBottom_toBottomOf" id="0x7f04024a" />
    <public type="attr" name="layout_constraintBottom_toTopOf" id="0x7f04024b" />
    <public type="attr" name="layout_constraintCircle" id="0x7f04024c" />
    <public type="attr" name="layout_constraintCircleAngle" id="0x7f04024d" />
    <public type="attr" name="layout_constraintCircleRadius" id="0x7f04024e" />
    <public type="attr" name="layout_constraintDimensionRatio" id="0x7f04024f" />
    <public type="attr" name="layout_constraintEnd_toEndOf" id="0x7f040250" />
    <public type="attr" name="layout_constraintEnd_toStartOf" id="0x7f040251" />
    <public type="attr" name="layout_constraintGuide_begin" id="0x7f040252" />
    <public type="attr" name="layout_constraintGuide_end" id="0x7f040253" />
    <public type="attr" name="layout_constraintGuide_percent" id="0x7f040254" />
    <public type="attr" name="layout_constraintHeight" id="0x7f040255" />
    <public type="attr" name="layout_constraintHeight_default" id="0x7f040256" />
    <public type="attr" name="layout_constraintHeight_max" id="0x7f040257" />
    <public type="attr" name="layout_constraintHeight_min" id="0x7f040258" />
    <public type="attr" name="layout_constraintHeight_percent" id="0x7f040259" />
    <public type="attr" name="layout_constraintHorizontal_bias" id="0x7f04025a" />
    <public type="attr" name="layout_constraintHorizontal_chainStyle" id="0x7f04025b" />
    <public type="attr" name="layout_constraintHorizontal_weight" id="0x7f04025c" />
    <public type="attr" name="layout_constraintLeft_creator" id="0x7f04025d" />
    <public type="attr" name="layout_constraintLeft_toLeftOf" id="0x7f04025e" />
    <public type="attr" name="layout_constraintLeft_toRightOf" id="0x7f04025f" />
    <public type="attr" name="layout_constraintRight_creator" id="0x7f040260" />
    <public type="attr" name="layout_constraintRight_toLeftOf" id="0x7f040261" />
    <public type="attr" name="layout_constraintRight_toRightOf" id="0x7f040262" />
    <public type="attr" name="layout_constraintStart_toEndOf" id="0x7f040263" />
    <public type="attr" name="layout_constraintStart_toStartOf" id="0x7f040264" />
    <public type="attr" name="layout_constraintTag" id="0x7f040265" />
    <public type="attr" name="layout_constraintTop_creator" id="0x7f040266" />
    <public type="attr" name="layout_constraintTop_toBottomOf" id="0x7f040267" />
    <public type="attr" name="layout_constraintTop_toTopOf" id="0x7f040268" />
    <public type="attr" name="layout_constraintVertical_bias" id="0x7f040269" />
    <public type="attr" name="layout_constraintVertical_chainStyle" id="0x7f04026a" />
    <public type="attr" name="layout_constraintVertical_weight" id="0x7f04026b" />
    <public type="attr" name="layout_constraintWidth" id="0x7f04026c" />
    <public type="attr" name="layout_constraintWidth_default" id="0x7f04026d" />
    <public type="attr" name="layout_constraintWidth_max" id="0x7f04026e" />
    <public type="attr" name="layout_constraintWidth_min" id="0x7f04026f" />
    <public type="attr" name="layout_constraintWidth_percent" id="0x7f040270" />
    <public type="attr" name="layout_dodgeInsetEdges" id="0x7f040271" />
    <public type="attr" name="layout_editor_absoluteX" id="0x7f040272" />
    <public type="attr" name="layout_editor_absoluteY" id="0x7f040273" />
    <public type="attr" name="layout_goneMarginBaseline" id="0x7f040274" />
    <public type="attr" name="layout_goneMarginBottom" id="0x7f040275" />
    <public type="attr" name="layout_goneMarginEnd" id="0x7f040276" />
    <public type="attr" name="layout_goneMarginLeft" id="0x7f040277" />
    <public type="attr" name="layout_goneMarginRight" id="0x7f040278" />
    <public type="attr" name="layout_goneMarginStart" id="0x7f040279" />
    <public type="attr" name="layout_goneMarginTop" id="0x7f04027a" />
    <public type="attr" name="layout_insetEdge" id="0x7f04027b" />
    <public type="attr" name="layout_keyline" id="0x7f04027c" />
    <public type="attr" name="layout_marginBaseline" id="0x7f04027d" />
    <public type="attr" name="layout_optimizationLevel" id="0x7f04027e" />
    <public type="attr" name="layout_scrollEffect" id="0x7f04027f" />
    <public type="attr" name="layout_scrollFlags" id="0x7f040280" />
    <public type="attr" name="layout_scrollInterpolator" id="0x7f040281" />
    <public type="attr" name="layout_wrapBehaviorInParent" id="0x7f040282" />
    <public type="attr" name="liftOnScroll" id="0x7f040283" />
    <public type="attr" name="liftOnScrollTargetViewId" id="0x7f040284" />
    <public type="attr" name="limitBoundsTo" id="0x7f040285" />
    <public type="attr" name="lineHeight" id="0x7f040286" />
    <public type="attr" name="lineSpacing" id="0x7f040287" />
    <public type="attr" name="linearProgressIndicatorStyle" id="0x7f040288" />
    <public type="attr" name="listChoiceBackgroundIndicator" id="0x7f040289" />
    <public type="attr" name="listChoiceIndicatorMultipleAnimated" id="0x7f04028a" />
    <public type="attr" name="listChoiceIndicatorSingleAnimated" id="0x7f04028b" />
    <public type="attr" name="listDividerAlertDialog" id="0x7f04028c" />
    <public type="attr" name="listItemLayout" id="0x7f04028d" />
    <public type="attr" name="listLayout" id="0x7f04028e" />
    <public type="attr" name="listMenuViewStyle" id="0x7f04028f" />
    <public type="attr" name="listPopupWindowStyle" id="0x7f040290" />
    <public type="attr" name="listPreferredItemHeight" id="0x7f040291" />
    <public type="attr" name="listPreferredItemHeightLarge" id="0x7f040292" />
    <public type="attr" name="listPreferredItemHeightSmall" id="0x7f040293" />
    <public type="attr" name="listPreferredItemPaddingEnd" id="0x7f040294" />
    <public type="attr" name="listPreferredItemPaddingLeft" id="0x7f040295" />
    <public type="attr" name="listPreferredItemPaddingRight" id="0x7f040296" />
    <public type="attr" name="listPreferredItemPaddingStart" id="0x7f040297" />
    <public type="attr" name="logo" id="0x7f040298" />
    <public type="attr" name="logoAdjustViewBounds" id="0x7f040299" />
    <public type="attr" name="logoDescription" id="0x7f04029a" />
    <public type="attr" name="logoScaleType" id="0x7f04029b" />
    <public type="attr" name="marginHorizontal" id="0x7f04029c" />
    <public type="attr" name="marginLeftSystemWindowInsets" id="0x7f04029d" />
    <public type="attr" name="marginRightSystemWindowInsets" id="0x7f04029e" />
    <public type="attr" name="marginTopSystemWindowInsets" id="0x7f04029f" />
    <public type="attr" name="materialAlertDialogBodyTextStyle" id="0x7f0402a0" />
    <public type="attr" name="materialAlertDialogButtonSpacerVisibility" id="0x7f0402a1" />
    <public type="attr" name="materialAlertDialogTheme" id="0x7f0402a2" />
    <public type="attr" name="materialAlertDialogTitleIconStyle" id="0x7f0402a3" />
    <public type="attr" name="materialAlertDialogTitlePanelStyle" id="0x7f0402a4" />
    <public type="attr" name="materialAlertDialogTitleTextStyle" id="0x7f0402a5" />
    <public type="attr" name="materialButtonOutlinedStyle" id="0x7f0402a6" />
    <public type="attr" name="materialButtonStyle" id="0x7f0402a7" />
    <public type="attr" name="materialButtonToggleGroupStyle" id="0x7f0402a8" />
    <public type="attr" name="materialCalendarDay" id="0x7f0402a9" />
    <public type="attr" name="materialCalendarDayOfWeekLabel" id="0x7f0402aa" />
    <public type="attr" name="materialCalendarFullscreenTheme" id="0x7f0402ab" />
    <public type="attr" name="materialCalendarHeaderCancelButton" id="0x7f0402ac" />
    <public type="attr" name="materialCalendarHeaderConfirmButton" id="0x7f0402ad" />
    <public type="attr" name="materialCalendarHeaderDivider" id="0x7f0402ae" />
    <public type="attr" name="materialCalendarHeaderLayout" id="0x7f0402af" />
    <public type="attr" name="materialCalendarHeaderSelection" id="0x7f0402b0" />
    <public type="attr" name="materialCalendarHeaderTitle" id="0x7f0402b1" />
    <public type="attr" name="materialCalendarHeaderToggleButton" id="0x7f0402b2" />
    <public type="attr" name="materialCalendarMonth" id="0x7f0402b3" />
    <public type="attr" name="materialCalendarMonthNavigationButton" id="0x7f0402b4" />
    <public type="attr" name="materialCalendarStyle" id="0x7f0402b5" />
    <public type="attr" name="materialCalendarTheme" id="0x7f0402b6" />
    <public type="attr" name="materialCalendarYearNavigationButton" id="0x7f0402b7" />
    <public type="attr" name="materialCardViewElevatedStyle" id="0x7f0402b8" />
    <public type="attr" name="materialCardViewFilledStyle" id="0x7f0402b9" />
    <public type="attr" name="materialCardViewOutlinedStyle" id="0x7f0402ba" />
    <public type="attr" name="materialCardViewStyle" id="0x7f0402bb" />
    <public type="attr" name="materialCircleRadius" id="0x7f0402bc" />
    <public type="attr" name="materialClockStyle" id="0x7f0402bd" />
    <public type="attr" name="materialDisplayDividerStyle" id="0x7f0402be" />
    <public type="attr" name="materialDividerHeavyStyle" id="0x7f0402bf" />
    <public type="attr" name="materialDividerStyle" id="0x7f0402c0" />
    <public type="attr" name="materialIconButtonFilledStyle" id="0x7f0402c1" />
    <public type="attr" name="materialIconButtonFilledTonalStyle" id="0x7f0402c2" />
    <public type="attr" name="materialIconButtonOutlinedStyle" id="0x7f0402c3" />
    <public type="attr" name="materialIconButtonStyle" id="0x7f0402c4" />
    <public type="attr" name="materialSwitchStyle" id="0x7f0402c5" />
    <public type="attr" name="materialThemeOverlay" id="0x7f0402c6" />
    <public type="attr" name="materialTimePickerStyle" id="0x7f0402c7" />
    <public type="attr" name="materialTimePickerTheme" id="0x7f0402c8" />
    <public type="attr" name="materialTimePickerTitleStyle" id="0x7f0402c9" />
    <public type="attr" name="maxAcceleration" id="0x7f0402ca" />
    <public type="attr" name="maxActionInlineWidth" id="0x7f0402cb" />
    <public type="attr" name="maxButtonHeight" id="0x7f0402cc" />
    <public type="attr" name="maxCharacterCount" id="0x7f0402cd" />
    <public type="attr" name="maxHeight" id="0x7f0402ce" />
    <public type="attr" name="maxImageSize" id="0x7f0402cf" />
    <public type="attr" name="maxLines" id="0x7f0402d0" />
    <public type="attr" name="maxVelocity" id="0x7f0402d1" />
    <public type="attr" name="maxWidth" id="0x7f0402d2" />
    <public type="attr" name="md_background_color" id="0x7f0402d3" />
    <public type="attr" name="md_btn_negative_selector" id="0x7f0402d4" />
    <public type="attr" name="md_btn_neutral_selector" id="0x7f0402d5" />
    <public type="attr" name="md_btn_positive_selector" id="0x7f0402d6" />
    <public type="attr" name="md_btn_ripple_color" id="0x7f0402d7" />
    <public type="attr" name="md_btn_stacked_selector" id="0x7f0402d8" />
    <public type="attr" name="md_btnstacked_gravity" id="0x7f0402d9" />
    <public type="attr" name="md_buttons_gravity" id="0x7f0402da" />
    <public type="attr" name="md_content_color" id="0x7f0402db" />
    <public type="attr" name="md_content_gravity" id="0x7f0402dc" />
    <public type="attr" name="md_dark_theme" id="0x7f0402dd" />
    <public type="attr" name="md_divider" id="0x7f0402de" />
    <public type="attr" name="md_divider_color" id="0x7f0402df" />
    <public type="attr" name="md_icon" id="0x7f0402e0" />
    <public type="attr" name="md_icon_limit_icon_to_default_size" id="0x7f0402e1" />
    <public type="attr" name="md_icon_max_size" id="0x7f0402e2" />
    <public type="attr" name="md_item_color" id="0x7f0402e3" />
    <public type="attr" name="md_items_gravity" id="0x7f0402e4" />
    <public type="attr" name="md_link_color" id="0x7f0402e5" />
    <public type="attr" name="md_list_selector" id="0x7f0402e6" />
    <public type="attr" name="md_medium_font" id="0x7f0402e7" />
    <public type="attr" name="md_negative_color" id="0x7f0402e8" />
    <public type="attr" name="md_neutral_color" id="0x7f0402e9" />
    <public type="attr" name="md_positive_color" id="0x7f0402ea" />
    <public type="attr" name="md_reduce_padding_no_title_no_buttons" id="0x7f0402eb" />
    <public type="attr" name="md_regular_font" id="0x7f0402ec" />
    <public type="attr" name="md_title_color" id="0x7f0402ed" />
    <public type="attr" name="md_title_gravity" id="0x7f0402ee" />
    <public type="attr" name="md_widget_color" id="0x7f0402ef" />
    <public type="attr" name="measureWithLargestChild" id="0x7f0402f0" />
    <public type="attr" name="menu" id="0x7f0402f1" />
    <public type="attr" name="menuAlignmentMode" id="0x7f0402f2" />
    <public type="attr" name="menuGravity" id="0x7f0402f3" />
    <public type="attr" name="methodName" id="0x7f0402f4" />
    <public type="attr" name="minHeight" id="0x7f0402f5" />
    <public type="attr" name="minHideDelay" id="0x7f0402f6" />
    <public type="attr" name="minSeparation" id="0x7f0402f7" />
    <public type="attr" name="minTouchTargetSize" id="0x7f0402f8" />
    <public type="attr" name="minWidth" id="0x7f0402f9" />
    <public type="attr" name="mock_diagonalsColor" id="0x7f0402fa" />
    <public type="attr" name="mock_label" id="0x7f0402fb" />
    <public type="attr" name="mock_labelBackgroundColor" id="0x7f0402fc" />
    <public type="attr" name="mock_labelColor" id="0x7f0402fd" />
    <public type="attr" name="mock_showDiagonals" id="0x7f0402fe" />
    <public type="attr" name="mock_showLabel" id="0x7f0402ff" />
    <public type="attr" name="motionDebug" id="0x7f040300" />
    <public type="attr" name="motionDurationExtraLong1" id="0x7f040301" />
    <public type="attr" name="motionDurationExtraLong2" id="0x7f040302" />
    <public type="attr" name="motionDurationExtraLong3" id="0x7f040303" />
    <public type="attr" name="motionDurationExtraLong4" id="0x7f040304" />
    <public type="attr" name="motionDurationLong1" id="0x7f040305" />
    <public type="attr" name="motionDurationLong2" id="0x7f040306" />
    <public type="attr" name="motionDurationLong3" id="0x7f040307" />
    <public type="attr" name="motionDurationLong4" id="0x7f040308" />
    <public type="attr" name="motionDurationMedium1" id="0x7f040309" />
    <public type="attr" name="motionDurationMedium2" id="0x7f04030a" />
    <public type="attr" name="motionDurationMedium3" id="0x7f04030b" />
    <public type="attr" name="motionDurationMedium4" id="0x7f04030c" />
    <public type="attr" name="motionDurationShort1" id="0x7f04030d" />
    <public type="attr" name="motionDurationShort2" id="0x7f04030e" />
    <public type="attr" name="motionDurationShort3" id="0x7f04030f" />
    <public type="attr" name="motionDurationShort4" id="0x7f040310" />
    <public type="attr" name="motionEasingAccelerated" id="0x7f040311" />
    <public type="attr" name="motionEasingDecelerated" id="0x7f040312" />
    <public type="attr" name="motionEasingEmphasized" id="0x7f040313" />
    <public type="attr" name="motionEasingEmphasizedAccelerateInterpolator" id="0x7f040314" />
    <public type="attr" name="motionEasingEmphasizedDecelerateInterpolator" id="0x7f040315" />
    <public type="attr" name="motionEasingEmphasizedInterpolator" id="0x7f040316" />
    <public type="attr" name="motionEasingLinear" id="0x7f040317" />
    <public type="attr" name="motionEasingLinearInterpolator" id="0x7f040318" />
    <public type="attr" name="motionEasingStandard" id="0x7f040319" />
    <public type="attr" name="motionEasingStandardAccelerateInterpolator" id="0x7f04031a" />
    <public type="attr" name="motionEasingStandardDecelerateInterpolator" id="0x7f04031b" />
    <public type="attr" name="motionEasingStandardInterpolator" id="0x7f04031c" />
    <public type="attr" name="motionEffect_alpha" id="0x7f04031d" />
    <public type="attr" name="motionEffect_end" id="0x7f04031e" />
    <public type="attr" name="motionEffect_move" id="0x7f04031f" />
    <public type="attr" name="motionEffect_start" id="0x7f040320" />
    <public type="attr" name="motionEffect_strict" id="0x7f040321" />
    <public type="attr" name="motionEffect_translationX" id="0x7f040322" />
    <public type="attr" name="motionEffect_translationY" id="0x7f040323" />
    <public type="attr" name="motionEffect_viewTransition" id="0x7f040324" />
    <public type="attr" name="motionInterpolator" id="0x7f040325" />
    <public type="attr" name="motionPath" id="0x7f040326" />
    <public type="attr" name="motionPathRotate" id="0x7f040327" />
    <public type="attr" name="motionProgress" id="0x7f040328" />
    <public type="attr" name="motionStagger" id="0x7f040329" />
    <public type="attr" name="motionTarget" id="0x7f04032a" />
    <public type="attr" name="motion_postLayoutCollision" id="0x7f04032b" />
    <public type="attr" name="motion_triggerOnCollision" id="0x7f04032c" />
    <public type="attr" name="moveWhenScrollAtTop" id="0x7f04032d" />
    <public type="attr" name="mpb_indeterminateTint" id="0x7f04032e" />
    <public type="attr" name="mpb_indeterminateTintMode" id="0x7f04032f" />
    <public type="attr" name="mpb_progressBackgroundTint" id="0x7f040330" />
    <public type="attr" name="mpb_progressBackgroundTintMode" id="0x7f040331" />
    <public type="attr" name="mpb_progressStyle" id="0x7f040332" />
    <public type="attr" name="mpb_progressTint" id="0x7f040333" />
    <public type="attr" name="mpb_progressTintMode" id="0x7f040334" />
    <public type="attr" name="mpb_secondaryProgressTint" id="0x7f040335" />
    <public type="attr" name="mpb_secondaryProgressTintMode" id="0x7f040336" />
    <public type="attr" name="mpb_setBothDrawables" id="0x7f040337" />
    <public type="attr" name="mpb_showProgressBackground" id="0x7f040338" />
    <public type="attr" name="mpb_useIntrinsicPadding" id="0x7f040339" />
    <public type="attr" name="multiChoiceItemLayout" id="0x7f04033a" />
    <public type="attr" name="navigationContentDescription" id="0x7f04033b" />
    <public type="attr" name="navigationIcon" id="0x7f04033c" />
    <public type="attr" name="navigationIconTint" id="0x7f04033d" />
    <public type="attr" name="navigationMode" id="0x7f04033e" />
    <public type="attr" name="navigationRailStyle" id="0x7f04033f" />
    <public type="attr" name="navigationViewStyle" id="0x7f040340" />
    <public type="attr" name="nestedScrollFlags" id="0x7f040341" />
    <public type="attr" name="nestedScrollViewStyle" id="0x7f040342" />
    <public type="attr" name="nestedScrollable" id="0x7f040343" />
    <public type="attr" name="number" id="0x7f040344" />
    <public type="attr" name="numericModifiers" id="0x7f040345" />
    <public type="attr" name="onCross" id="0x7f040346" />
    <public type="attr" name="onHide" id="0x7f040347" />
    <public type="attr" name="onNegativeCross" id="0x7f040348" />
    <public type="attr" name="onPositiveCross" id="0x7f040349" />
    <public type="attr" name="onShow" id="0x7f04034a" />
    <public type="attr" name="onStateTransition" id="0x7f04034b" />
    <public type="attr" name="onTouchUp" id="0x7f04034c" />
    <public type="attr" name="overlapAnchor" id="0x7f04034d" />
    <public type="attr" name="overlay" id="0x7f04034e" />
    <public type="attr" name="paddingBottomNoButtons" id="0x7f04034f" />
    <public type="attr" name="paddingBottomSystemWindowInsets" id="0x7f040350" />
    <public type="attr" name="paddingEnd" id="0x7f040351" />
    <public type="attr" name="paddingLeftSystemWindowInsets" id="0x7f040352" />
    <public type="attr" name="paddingRightSystemWindowInsets" id="0x7f040353" />
    <public type="attr" name="paddingStart" id="0x7f040354" />
    <public type="attr" name="paddingTopNoTitle" id="0x7f040355" />
    <public type="attr" name="paddingTopSystemWindowInsets" id="0x7f040356" />
    <public type="attr" name="panelBackground" id="0x7f040357" />
    <public type="attr" name="panelMenuListTheme" id="0x7f040358" />
    <public type="attr" name="panelMenuListWidth" id="0x7f040359" />
    <public type="attr" name="passwordToggleContentDescription" id="0x7f04035a" />
    <public type="attr" name="passwordToggleDrawable" id="0x7f04035b" />
    <public type="attr" name="passwordToggleEnabled" id="0x7f04035c" />
    <public type="attr" name="passwordToggleTint" id="0x7f04035d" />
    <public type="attr" name="passwordToggleTintMode" id="0x7f04035e" />
    <public type="attr" name="pathMotionArc" id="0x7f04035f" />
    <public type="attr" name="path_percent" id="0x7f040360" />
    <public type="attr" name="percentHeight" id="0x7f040361" />
    <public type="attr" name="percentWidth" id="0x7f040362" />
    <public type="attr" name="percentX" id="0x7f040363" />
    <public type="attr" name="percentY" id="0x7f040364" />
    <public type="attr" name="perpendicularPath_percent" id="0x7f040365" />
    <public type="attr" name="pivotAnchor" id="0x7f040366" />
    <public type="attr" name="placeholderText" id="0x7f040367" />
    <public type="attr" name="placeholderTextAppearance" id="0x7f040368" />
    <public type="attr" name="placeholderTextColor" id="0x7f040369" />
    <public type="attr" name="placeholder_emptyVisibility" id="0x7f04036a" />
    <public type="attr" name="polarRelativeTo" id="0x7f04036b" />
    <public type="attr" name="popupMenuBackground" id="0x7f04036c" />
    <public type="attr" name="popupMenuStyle" id="0x7f04036d" />
    <public type="attr" name="popupTheme" id="0x7f04036e" />
    <public type="attr" name="popupWindowStyle" id="0x7f04036f" />
    <public type="attr" name="prefixText" id="0x7f040370" />
    <public type="attr" name="prefixTextAppearance" id="0x7f040371" />
    <public type="attr" name="prefixTextColor" id="0x7f040372" />
    <public type="attr" name="preserveIconSpacing" id="0x7f040373" />
    <public type="attr" name="pressedTranslationZ" id="0x7f040374" />
    <public type="attr" name="progressBarPadding" id="0x7f040375" />
    <public type="attr" name="progressBarStyle" id="0x7f040376" />
    <public type="attr" name="quantizeMotionInterpolator" id="0x7f040377" />
    <public type="attr" name="quantizeMotionPhase" id="0x7f040378" />
    <public type="attr" name="quantizeMotionSteps" id="0x7f040379" />
    <public type="attr" name="queryBackground" id="0x7f04037a" />
    <public type="attr" name="queryHint" id="0x7f04037b" />
    <public type="attr" name="queryPatterns" id="0x7f04037c" />
    <public type="attr" name="radioButtonStyle" id="0x7f04037d" />
    <public type="attr" name="rangeFillColor" id="0x7f04037e" />
    <public type="attr" name="ratingBarStyle" id="0x7f04037f" />
    <public type="attr" name="ratingBarStyleIndicator" id="0x7f040380" />
    <public type="attr" name="ratingBarStyleSmall" id="0x7f040381" />
    <public type="attr" name="reactiveGuide_animateChange" id="0x7f040382" />
    <public type="attr" name="reactiveGuide_applyToAllConstraintSets" id="0x7f040383" />
    <public type="attr" name="reactiveGuide_applyToConstraintSet" id="0x7f040384" />
    <public type="attr" name="reactiveGuide_valueId" id="0x7f040385" />
    <public type="attr" name="recyclerViewStyle" id="0x7f040386" />
    <public type="attr" name="region_heightLessThan" id="0x7f040387" />
    <public type="attr" name="region_heightMoreThan" id="0x7f040388" />
    <public type="attr" name="region_widthLessThan" id="0x7f040389" />
    <public type="attr" name="region_widthMoreThan" id="0x7f04038a" />
    <public type="attr" name="removeEmbeddedFabElevation" id="0x7f04038b" />
    <public type="attr" name="reverseLayout" id="0x7f04038c" />
    <public type="attr" name="rippleColor" id="0x7f04038d" />
    <public type="attr" name="riv_border_color" id="0x7f04038e" />
    <public type="attr" name="riv_border_width" id="0x7f04038f" />
    <public type="attr" name="riv_corner_radius" id="0x7f040390" />
    <public type="attr" name="riv_corner_radius_bottom_left" id="0x7f040391" />
    <public type="attr" name="riv_corner_radius_bottom_right" id="0x7f040392" />
    <public type="attr" name="riv_corner_radius_top_left" id="0x7f040393" />
    <public type="attr" name="riv_corner_radius_top_right" id="0x7f040394" />
    <public type="attr" name="riv_mutate_background" id="0x7f040395" />
    <public type="attr" name="riv_oval" id="0x7f040396" />
    <public type="attr" name="riv_tile_mode" id="0x7f040397" />
    <public type="attr" name="riv_tile_mode_x" id="0x7f040398" />
    <public type="attr" name="riv_tile_mode_y" id="0x7f040399" />
    <public type="attr" name="rotationCenterId" id="0x7f04039a" />
    <public type="attr" name="round" id="0x7f04039b" />
    <public type="attr" name="roundPercent" id="0x7f04039c" />
    <public type="attr" name="saturation" id="0x7f04039d" />
    <public type="attr" name="scaleFromTextSize" id="0x7f04039e" />
    <public type="attr" name="scrimAnimationDuration" id="0x7f04039f" />
    <public type="attr" name="scrimBackground" id="0x7f0403a0" />
    <public type="attr" name="scrimVisibleHeightTrigger" id="0x7f0403a1" />
    <public type="attr" name="searchHintIcon" id="0x7f0403a2" />
    <public type="attr" name="searchIcon" id="0x7f0403a3" />
    <public type="attr" name="searchViewStyle" id="0x7f0403a4" />
    <public type="attr" name="seekBarStyle" id="0x7f0403a5" />
    <public type="attr" name="selectableItemBackground" id="0x7f0403a6" />
    <public type="attr" name="selectableItemBackgroundBorderless" id="0x7f0403a7" />
    <public type="attr" name="selectionRequired" id="0x7f0403a8" />
    <public type="attr" name="selectorSize" id="0x7f0403a9" />
    <public type="attr" name="setsTag" id="0x7f0403aa" />
    <public type="attr" name="shapeAppearance" id="0x7f0403ab" />
    <public type="attr" name="shapeAppearanceCornerExtraLarge" id="0x7f0403ac" />
    <public type="attr" name="shapeAppearanceCornerExtraSmall" id="0x7f0403ad" />
    <public type="attr" name="shapeAppearanceCornerLarge" id="0x7f0403ae" />
    <public type="attr" name="shapeAppearanceCornerMedium" id="0x7f0403af" />
    <public type="attr" name="shapeAppearanceCornerSmall" id="0x7f0403b0" />
    <public type="attr" name="shapeAppearanceLargeComponent" id="0x7f0403b1" />
    <public type="attr" name="shapeAppearanceMediumComponent" id="0x7f0403b2" />
    <public type="attr" name="shapeAppearanceOverlay" id="0x7f0403b3" />
    <public type="attr" name="shapeAppearanceSmallComponent" id="0x7f0403b4" />
    <public type="attr" name="shapeCornerFamily" id="0x7f0403b5" />
    <public type="attr" name="shortcutMatchRequired" id="0x7f0403b6" />
    <public type="attr" name="showAnimationBehavior" id="0x7f0403b7" />
    <public type="attr" name="showAsAction" id="0x7f0403b8" />
    <public type="attr" name="showDelay" id="0x7f0403b9" />
    <public type="attr" name="showDividers" id="0x7f0403ba" />
    <public type="attr" name="showMotionSpec" id="0x7f0403bb" />
    <public type="attr" name="showPaths" id="0x7f0403bc" />
    <public type="attr" name="showText" id="0x7f0403bd" />
    <public type="attr" name="showTitle" id="0x7f0403be" />
    <public type="attr" name="show_fps" id="0x7f0403bf" />
    <public type="attr" name="shrinkMotionSpec" id="0x7f0403c0" />
    <public type="attr" name="simpleItemLayout" id="0x7f0403c1" />
    <public type="attr" name="simpleItemSelectedColor" id="0x7f0403c2" />
    <public type="attr" name="simpleItemSelectedRippleColor" id="0x7f0403c3" />
    <public type="attr" name="simpleItems" id="0x7f0403c4" />
    <public type="attr" name="singleChoiceItemLayout" id="0x7f0403c5" />
    <public type="attr" name="singleLine" id="0x7f0403c6" />
    <public type="attr" name="singleSelection" id="0x7f0403c7" />
    <public type="attr" name="sizePercent" id="0x7f0403c8" />
    <public type="attr" name="sliderStyle" id="0x7f0403c9" />
    <public type="attr" name="snackbarButtonStyle" id="0x7f0403ca" />
    <public type="attr" name="snackbarStyle" id="0x7f0403cb" />
    <public type="attr" name="snackbarTextViewStyle" id="0x7f0403cc" />
    <public type="attr" name="spanCount" id="0x7f0403cd" />
    <public type="attr" name="spinBars" id="0x7f0403ce" />
    <public type="attr" name="spinnerDropDownItemStyle" id="0x7f0403cf" />
    <public type="attr" name="spinnerStyle" id="0x7f0403d0" />
    <public type="attr" name="splitTrack" id="0x7f0403d1" />
    <public type="attr" name="springBoundary" id="0x7f0403d2" />
    <public type="attr" name="springDamping" id="0x7f0403d3" />
    <public type="attr" name="springMass" id="0x7f0403d4" />
    <public type="attr" name="springStiffness" id="0x7f0403d5" />
    <public type="attr" name="springStopThreshold" id="0x7f0403d6" />
    <public type="attr" name="srcCompat" id="0x7f0403d7" />
    <public type="attr" name="stackFromEnd" id="0x7f0403d8" />
    <public type="attr" name="staggered" id="0x7f0403d9" />
    <public type="attr" name="startIconCheckable" id="0x7f0403da" />
    <public type="attr" name="startIconContentDescription" id="0x7f0403db" />
    <public type="attr" name="startIconDrawable" id="0x7f0403dc" />
    <public type="attr" name="startIconTint" id="0x7f0403dd" />
    <public type="attr" name="startIconTintMode" id="0x7f0403de" />
    <public type="attr" name="state_above_anchor" id="0x7f0403df" />
    <public type="attr" name="state_collapsed" id="0x7f0403e0" />
    <public type="attr" name="state_collapsible" id="0x7f0403e1" />
    <public type="attr" name="state_dragged" id="0x7f0403e2" />
    <public type="attr" name="state_error" id="0x7f0403e3" />
    <public type="attr" name="state_indeterminate" id="0x7f0403e4" />
    <public type="attr" name="state_liftable" id="0x7f0403e5" />
    <public type="attr" name="state_lifted" id="0x7f0403e6" />
    <public type="attr" name="state_with_icon" id="0x7f0403e7" />
    <public type="attr" name="statusBarBackground" id="0x7f0403e8" />
    <public type="attr" name="statusBarForeground" id="0x7f0403e9" />
    <public type="attr" name="statusBarScrim" id="0x7f0403ea" />
    <public type="attr" name="strokeColor" id="0x7f0403eb" />
    <public type="attr" name="strokeWidth" id="0x7f0403ec" />
    <public type="attr" name="subMenuArrow" id="0x7f0403ed" />
    <public type="attr" name="subheaderColor" id="0x7f0403ee" />
    <public type="attr" name="subheaderInsetEnd" id="0x7f0403ef" />
    <public type="attr" name="subheaderInsetStart" id="0x7f0403f0" />
    <public type="attr" name="subheaderTextAppearance" id="0x7f0403f1" />
    <public type="attr" name="submitBackground" id="0x7f0403f2" />
    <public type="attr" name="subtitle" id="0x7f0403f3" />
    <public type="attr" name="subtitleCentered" id="0x7f0403f4" />
    <public type="attr" name="subtitleTextAppearance" id="0x7f0403f5" />
    <public type="attr" name="subtitleTextColor" id="0x7f0403f6" />
    <public type="attr" name="subtitleTextStyle" id="0x7f0403f7" />
    <public type="attr" name="suffixText" id="0x7f0403f8" />
    <public type="attr" name="suffixTextAppearance" id="0x7f0403f9" />
    <public type="attr" name="suffixTextColor" id="0x7f0403fa" />
    <public type="attr" name="suggestionRowLayout" id="0x7f0403fb" />
    <public type="attr" name="swipeRefreshLayoutProgressSpinnerBackgroundColor" id="0x7f0403fc" />
    <public type="attr" name="switchMinWidth" id="0x7f0403fd" />
    <public type="attr" name="switchPadding" id="0x7f0403fe" />
    <public type="attr" name="switchStyle" id="0x7f0403ff" />
    <public type="attr" name="switchTextAppearance" id="0x7f040400" />
    <public type="attr" name="tabBackground" id="0x7f040401" />
    <public type="attr" name="tabContentStart" id="0x7f040402" />
    <public type="attr" name="tabGravity" id="0x7f040403" />
    <public type="attr" name="tabIconTint" id="0x7f040404" />
    <public type="attr" name="tabIconTintMode" id="0x7f040405" />
    <public type="attr" name="tabIndicator" id="0x7f040406" />
    <public type="attr" name="tabIndicatorAnimationDuration" id="0x7f040407" />
    <public type="attr" name="tabIndicatorAnimationMode" id="0x7f040408" />
    <public type="attr" name="tabIndicatorColor" id="0x7f040409" />
    <public type="attr" name="tabIndicatorFullWidth" id="0x7f04040a" />
    <public type="attr" name="tabIndicatorGravity" id="0x7f04040b" />
    <public type="attr" name="tabIndicatorHeight" id="0x7f04040c" />
    <public type="attr" name="tabInlineLabel" id="0x7f04040d" />
    <public type="attr" name="tabMaxWidth" id="0x7f04040e" />
    <public type="attr" name="tabMinWidth" id="0x7f04040f" />
    <public type="attr" name="tabMode" id="0x7f040410" />
    <public type="attr" name="tabPadding" id="0x7f040411" />
    <public type="attr" name="tabPaddingBottom" id="0x7f040412" />
    <public type="attr" name="tabPaddingEnd" id="0x7f040413" />
    <public type="attr" name="tabPaddingStart" id="0x7f040414" />
    <public type="attr" name="tabPaddingTop" id="0x7f040415" />
    <public type="attr" name="tabRippleColor" id="0x7f040416" />
    <public type="attr" name="tabSecondaryStyle" id="0x7f040417" />
    <public type="attr" name="tabSelectedTextColor" id="0x7f040418" />
    <public type="attr" name="tabStyle" id="0x7f040419" />
    <public type="attr" name="tabTextAppearance" id="0x7f04041a" />
    <public type="attr" name="tabTextColor" id="0x7f04041b" />
    <public type="attr" name="tabUnboundedRipple" id="0x7f04041c" />
    <public type="attr" name="targetId" id="0x7f04041d" />
    <public type="attr" name="telltales_tailColor" id="0x7f04041e" />
    <public type="attr" name="telltales_tailScale" id="0x7f04041f" />
    <public type="attr" name="telltales_velocityMode" id="0x7f040420" />
    <public type="attr" name="textAllCaps" id="0x7f040421" />
    <public type="attr" name="textAppearanceBody1" id="0x7f040422" />
    <public type="attr" name="textAppearanceBody2" id="0x7f040423" />
    <public type="attr" name="textAppearanceBodyLarge" id="0x7f040424" />
    <public type="attr" name="textAppearanceBodyMedium" id="0x7f040425" />
    <public type="attr" name="textAppearanceBodySmall" id="0x7f040426" />
    <public type="attr" name="textAppearanceButton" id="0x7f040427" />
    <public type="attr" name="textAppearanceCaption" id="0x7f040428" />
    <public type="attr" name="textAppearanceDisplayLarge" id="0x7f040429" />
    <public type="attr" name="textAppearanceDisplayMedium" id="0x7f04042a" />
    <public type="attr" name="textAppearanceDisplaySmall" id="0x7f04042b" />
    <public type="attr" name="textAppearanceHeadline1" id="0x7f04042c" />
    <public type="attr" name="textAppearanceHeadline2" id="0x7f04042d" />
    <public type="attr" name="textAppearanceHeadline3" id="0x7f04042e" />
    <public type="attr" name="textAppearanceHeadline4" id="0x7f04042f" />
    <public type="attr" name="textAppearanceHeadline5" id="0x7f040430" />
    <public type="attr" name="textAppearanceHeadline6" id="0x7f040431" />
    <public type="attr" name="textAppearanceHeadlineLarge" id="0x7f040432" />
    <public type="attr" name="textAppearanceHeadlineMedium" id="0x7f040433" />
    <public type="attr" name="textAppearanceHeadlineSmall" id="0x7f040434" />
    <public type="attr" name="textAppearanceLabelLarge" id="0x7f040435" />
    <public type="attr" name="textAppearanceLabelMedium" id="0x7f040436" />
    <public type="attr" name="textAppearanceLabelSmall" id="0x7f040437" />
    <public type="attr" name="textAppearanceLargePopupMenu" id="0x7f040438" />
    <public type="attr" name="textAppearanceLineHeightEnabled" id="0x7f040439" />
    <public type="attr" name="textAppearanceListItem" id="0x7f04043a" />
    <public type="attr" name="textAppearanceListItemSecondary" id="0x7f04043b" />
    <public type="attr" name="textAppearanceListItemSmall" id="0x7f04043c" />
    <public type="attr" name="textAppearanceOverline" id="0x7f04043d" />
    <public type="attr" name="textAppearancePopupMenuHeader" id="0x7f04043e" />
    <public type="attr" name="textAppearanceSearchResultSubtitle" id="0x7f04043f" />
    <public type="attr" name="textAppearanceSearchResultTitle" id="0x7f040440" />
    <public type="attr" name="textAppearanceSmallPopupMenu" id="0x7f040441" />
    <public type="attr" name="textAppearanceSubtitle1" id="0x7f040442" />
    <public type="attr" name="textAppearanceSubtitle2" id="0x7f040443" />
    <public type="attr" name="textAppearanceTitleLarge" id="0x7f040444" />
    <public type="attr" name="textAppearanceTitleMedium" id="0x7f040445" />
    <public type="attr" name="textAppearanceTitleSmall" id="0x7f040446" />
    <public type="attr" name="textBackground" id="0x7f040447" />
    <public type="attr" name="textBackgroundPanX" id="0x7f040448" />
    <public type="attr" name="textBackgroundPanY" id="0x7f040449" />
    <public type="attr" name="textBackgroundRotate" id="0x7f04044a" />
    <public type="attr" name="textBackgroundZoom" id="0x7f04044b" />
    <public type="attr" name="textColorAlertDialogListItem" id="0x7f04044c" />
    <public type="attr" name="textColorSearchUrl" id="0x7f04044d" />
    <public type="attr" name="textEndPadding" id="0x7f04044e" />
    <public type="attr" name="textFillColor" id="0x7f04044f" />
    <public type="attr" name="textInputFilledDenseStyle" id="0x7f040450" />
    <public type="attr" name="textInputFilledExposedDropdownMenuStyle" id="0x7f040451" />
    <public type="attr" name="textInputFilledStyle" id="0x7f040452" />
    <public type="attr" name="textInputLayoutFocusedRectEnabled" id="0x7f040453" />
    <public type="attr" name="textInputOutlinedDenseStyle" id="0x7f040454" />
    <public type="attr" name="textInputOutlinedExposedDropdownMenuStyle" id="0x7f040455" />
    <public type="attr" name="textInputOutlinedStyle" id="0x7f040456" />
    <public type="attr" name="textInputStyle" id="0x7f040457" />
    <public type="attr" name="textLocale" id="0x7f040458" />
    <public type="attr" name="textOutlineColor" id="0x7f040459" />
    <public type="attr" name="textOutlineThickness" id="0x7f04045a" />
    <public type="attr" name="textPanX" id="0x7f04045b" />
    <public type="attr" name="textPanY" id="0x7f04045c" />
    <public type="attr" name="textStartPadding" id="0x7f04045d" />
    <public type="attr" name="textureBlurFactor" id="0x7f04045e" />
    <public type="attr" name="textureEffect" id="0x7f04045f" />
    <public type="attr" name="textureHeight" id="0x7f040460" />
    <public type="attr" name="textureWidth" id="0x7f040461" />
    <public type="attr" name="theme" id="0x7f040462" />
    <public type="attr" name="thickness" id="0x7f040463" />
    <public type="attr" name="thumbColor" id="0x7f040464" />
    <public type="attr" name="thumbElevation" id="0x7f040465" />
    <public type="attr" name="thumbIcon" id="0x7f040466" />
    <public type="attr" name="thumbIconTint" id="0x7f040467" />
    <public type="attr" name="thumbIconTintMode" id="0x7f040468" />
    <public type="attr" name="thumbRadius" id="0x7f040469" />
    <public type="attr" name="thumbStrokeColor" id="0x7f04046a" />
    <public type="attr" name="thumbStrokeWidth" id="0x7f04046b" />
    <public type="attr" name="thumbTextPadding" id="0x7f04046c" />
    <public type="attr" name="thumbTint" id="0x7f04046d" />
    <public type="attr" name="thumbTintMode" id="0x7f04046e" />
    <public type="attr" name="tickColor" id="0x7f04046f" />
    <public type="attr" name="tickColorActive" id="0x7f040470" />
    <public type="attr" name="tickColorInactive" id="0x7f040471" />
    <public type="attr" name="tickMark" id="0x7f040472" />
    <public type="attr" name="tickMarkTint" id="0x7f040473" />
    <public type="attr" name="tickMarkTintMode" id="0x7f040474" />
    <public type="attr" name="tickVisible" id="0x7f040475" />
    <public type="attr" name="tint" id="0x7f040476" />
    <public type="attr" name="tintMode" id="0x7f040477" />
    <public type="attr" name="title" id="0x7f040478" />
    <public type="attr" name="titleCentered" id="0x7f040479" />
    <public type="attr" name="titleCollapseMode" id="0x7f04047a" />
    <public type="attr" name="titleEnabled" id="0x7f04047b" />
    <public type="attr" name="titleMargin" id="0x7f04047c" />
    <public type="attr" name="titleMarginBottom" id="0x7f04047d" />
    <public type="attr" name="titleMarginEnd" id="0x7f04047e" />
    <public type="attr" name="titleMarginStart" id="0x7f04047f" />
    <public type="attr" name="titleMarginTop" id="0x7f040480" />
    <public type="attr" name="titleMargins" id="0x7f040481" />
    <public type="attr" name="titlePositionInterpolator" id="0x7f040482" />
    <public type="attr" name="titleTextAppearance" id="0x7f040483" />
    <public type="attr" name="titleTextColor" id="0x7f040484" />
    <public type="attr" name="titleTextEllipsize" id="0x7f040485" />
    <public type="attr" name="titleTextStyle" id="0x7f040486" />
    <public type="attr" name="toggleCheckedStateOnClick" id="0x7f040487" />
    <public type="attr" name="toolbarId" id="0x7f040488" />
    <public type="attr" name="toolbarNavigationButtonStyle" id="0x7f040489" />
    <public type="attr" name="toolbarStyle" id="0x7f04048a" />
    <public type="attr" name="toolbarSurfaceStyle" id="0x7f04048b" />
    <public type="attr" name="tooltipForegroundColor" id="0x7f04048c" />
    <public type="attr" name="tooltipFrameBackground" id="0x7f04048d" />
    <public type="attr" name="tooltipStyle" id="0x7f04048e" />
    <public type="attr" name="tooltipText" id="0x7f04048f" />
    <public type="attr" name="topInsetScrimEnabled" id="0x7f040490" />
    <public type="attr" name="touchAnchorId" id="0x7f040491" />
    <public type="attr" name="touchAnchorSide" id="0x7f040492" />
    <public type="attr" name="touchRegionId" id="0x7f040493" />
    <public type="attr" name="track" id="0x7f040494" />
    <public type="attr" name="trackColor" id="0x7f040495" />
    <public type="attr" name="trackColorActive" id="0x7f040496" />
    <public type="attr" name="trackColorInactive" id="0x7f040497" />
    <public type="attr" name="trackCornerRadius" id="0x7f040498" />
    <public type="attr" name="trackDecoration" id="0x7f040499" />
    <public type="attr" name="trackDecorationTint" id="0x7f04049a" />
    <public type="attr" name="trackDecorationTintMode" id="0x7f04049b" />
    <public type="attr" name="trackHeight" id="0x7f04049c" />
    <public type="attr" name="trackThickness" id="0x7f04049d" />
    <public type="attr" name="trackTint" id="0x7f04049e" />
    <public type="attr" name="trackTintMode" id="0x7f04049f" />
    <public type="attr" name="transformPivotTarget" id="0x7f0404a0" />
    <public type="attr" name="transitionDisable" id="0x7f0404a1" />
    <public type="attr" name="transitionEasing" id="0x7f0404a2" />
    <public type="attr" name="transitionFlags" id="0x7f0404a3" />
    <public type="attr" name="transitionPathRotate" id="0x7f0404a4" />
    <public type="attr" name="transitionShapeAppearance" id="0x7f0404a5" />
    <public type="attr" name="triggerId" id="0x7f0404a6" />
    <public type="attr" name="triggerReceiver" id="0x7f0404a7" />
    <public type="attr" name="triggerSlack" id="0x7f0404a8" />
    <public type="attr" name="ttcIndex" id="0x7f0404a9" />
    <public type="attr" name="upDuration" id="0x7f0404aa" />
    <public type="attr" name="useCompatPadding" id="0x7f0404ab" />
    <public type="attr" name="useMaterialThemeColors" id="0x7f0404ac" />
    <public type="attr" name="values" id="0x7f0404ad" />
    <public type="attr" name="verticalOffset" id="0x7f0404ae" />
    <public type="attr" name="verticalOffsetWithText" id="0x7f0404af" />
    <public type="attr" name="viewInflaterClass" id="0x7f0404b0" />
    <public type="attr" name="viewTransitionMode" id="0x7f0404b1" />
    <public type="attr" name="viewTransitionOnCross" id="0x7f0404b2" />
    <public type="attr" name="viewTransitionOnNegativeCross" id="0x7f0404b3" />
    <public type="attr" name="viewTransitionOnPositiveCross" id="0x7f0404b4" />
    <public type="attr" name="visibilityMode" id="0x7f0404b5" />
    <public type="attr" name="voiceIcon" id="0x7f0404b6" />
    <public type="attr" name="warmth" id="0x7f0404b7" />
    <public type="attr" name="waveDecay" id="0x7f0404b8" />
    <public type="attr" name="waveOffset" id="0x7f0404b9" />
    <public type="attr" name="wavePeriod" id="0x7f0404ba" />
    <public type="attr" name="wavePhase" id="0x7f0404bb" />
    <public type="attr" name="waveShape" id="0x7f0404bc" />
    <public type="attr" name="waveVariesBy" id="0x7f0404bd" />
    <public type="attr" name="windowActionBar" id="0x7f0404be" />
    <public type="attr" name="windowActionBarOverlay" id="0x7f0404bf" />
    <public type="attr" name="windowActionModeOverlay" id="0x7f0404c0" />
    <public type="attr" name="windowFixedHeightMajor" id="0x7f0404c1" />
    <public type="attr" name="windowFixedHeightMinor" id="0x7f0404c2" />
    <public type="attr" name="windowFixedWidthMajor" id="0x7f0404c3" />
    <public type="attr" name="windowFixedWidthMinor" id="0x7f0404c4" />
    <public type="attr" name="windowMinWidthMajor" id="0x7f0404c5" />
    <public type="attr" name="windowMinWidthMinor" id="0x7f0404c6" />
    <public type="attr" name="windowNoTitle" id="0x7f0404c7" />
    <public type="attr" name="yearSelectedStyle" id="0x7f0404c8" />
    <public type="attr" name="yearStyle" id="0x7f0404c9" />
    <public type="attr" name="yearTodayStyle" id="0x7f0404ca" />
    <public type="bool" name="abc_action_bar_embed_tabs" id="0x7f050000" />
    <public type="bool" name="abc_config_actionMenuItemAllCaps" id="0x7f050001" />
    <public type="bool" name="md_is_tablet" id="0x7f050002" />
    <public type="bool" name="mtrl_btn_textappearance_all_caps" id="0x7f050003" />
    <public type="color" name="abc_background_cache_hint_selector_material_dark" id="0x7f060000" />
    <public type="color" name="abc_background_cache_hint_selector_material_light" id="0x7f060001" />
    <public type="color" name="abc_btn_colored_borderless_text_material" id="0x7f060002" />
    <public type="color" name="abc_btn_colored_text_material" id="0x7f060003" />
    <public type="color" name="abc_color_highlight_material" id="0x7f060004" />
    <public type="color" name="abc_decor_view_status_guard" id="0x7f060005" />
    <public type="color" name="abc_decor_view_status_guard_light" id="0x7f060006" />
    <public type="color" name="abc_hint_foreground_material_dark" id="0x7f060007" />
    <public type="color" name="abc_hint_foreground_material_light" id="0x7f060008" />
    <public type="color" name="abc_primary_text_disable_only_material_dark" id="0x7f060009" />
    <public type="color" name="abc_primary_text_disable_only_material_light" id="0x7f06000a" />
    <public type="color" name="abc_primary_text_material_dark" id="0x7f06000b" />
    <public type="color" name="abc_primary_text_material_light" id="0x7f06000c" />
    <public type="color" name="abc_search_url_text" id="0x7f06000d" />
    <public type="color" name="abc_search_url_text_normal" id="0x7f06000e" />
    <public type="color" name="abc_search_url_text_pressed" id="0x7f06000f" />
    <public type="color" name="abc_search_url_text_selected" id="0x7f060010" />
    <public type="color" name="abc_secondary_text_material_dark" id="0x7f060011" />
    <public type="color" name="abc_secondary_text_material_light" id="0x7f060012" />
    <public type="color" name="abc_tint_btn_checkable" id="0x7f060013" />
    <public type="color" name="abc_tint_default" id="0x7f060014" />
    <public type="color" name="abc_tint_edittext" id="0x7f060015" />
    <public type="color" name="abc_tint_seek_thumb" id="0x7f060016" />
    <public type="color" name="abc_tint_spinner" id="0x7f060017" />
    <public type="color" name="abc_tint_switch_track" id="0x7f060018" />
    <public type="color" name="accent_material_dark" id="0x7f060019" />
    <public type="color" name="accent_material_light" id="0x7f06001a" />
    <public type="color" name="androidx_core_ripple_material_light" id="0x7f06001b" />
    <public type="color" name="androidx_core_secondary_text_default_material_light" id="0x7f06001c" />
    <public type="color" name="background_floating_material_dark" id="0x7f06001d" />
    <public type="color" name="background_floating_material_light" id="0x7f06001e" />
    <public type="color" name="background_material_dark" id="0x7f06001f" />
    <public type="color" name="background_material_light" id="0x7f060020" />
    <public type="color" name="bright_foreground_disabled_material_dark" id="0x7f060021" />
    <public type="color" name="bright_foreground_disabled_material_light" id="0x7f060022" />
    <public type="color" name="bright_foreground_inverse_material_dark" id="0x7f060023" />
    <public type="color" name="bright_foreground_inverse_material_light" id="0x7f060024" />
    <public type="color" name="bright_foreground_material_dark" id="0x7f060025" />
    <public type="color" name="bright_foreground_material_light" id="0x7f060026" />
    <public type="color" name="button_material_dark" id="0x7f060027" />
    <public type="color" name="button_material_light" id="0x7f060028" />
    <public type="color" name="cardview_dark_background" id="0x7f060029" />
    <public type="color" name="cardview_light_background" id="0x7f06002a" />
    <public type="color" name="cardview_shadow_end_color" id="0x7f06002b" />
    <public type="color" name="cardview_shadow_start_color" id="0x7f06002c" />
    <public type="color" name="design_bottom_navigation_shadow_color" id="0x7f06002d" />
    <public type="color" name="design_box_stroke_color" id="0x7f06002e" />
    <public type="color" name="design_dark_default_color_background" id="0x7f06002f" />
    <public type="color" name="design_dark_default_color_error" id="0x7f060030" />
    <public type="color" name="design_dark_default_color_on_background" id="0x7f060031" />
    <public type="color" name="design_dark_default_color_on_error" id="0x7f060032" />
    <public type="color" name="design_dark_default_color_on_primary" id="0x7f060033" />
    <public type="color" name="design_dark_default_color_on_secondary" id="0x7f060034" />
    <public type="color" name="design_dark_default_color_on_surface" id="0x7f060035" />
    <public type="color" name="design_dark_default_color_primary" id="0x7f060036" />
    <public type="color" name="design_dark_default_color_primary_dark" id="0x7f060037" />
    <public type="color" name="design_dark_default_color_primary_variant" id="0x7f060038" />
    <public type="color" name="design_dark_default_color_secondary" id="0x7f060039" />
    <public type="color" name="design_dark_default_color_secondary_variant" id="0x7f06003a" />
    <public type="color" name="design_dark_default_color_surface" id="0x7f06003b" />
    <public type="color" name="design_default_color_background" id="0x7f06003c" />
    <public type="color" name="design_default_color_error" id="0x7f06003d" />
    <public type="color" name="design_default_color_on_background" id="0x7f06003e" />
    <public type="color" name="design_default_color_on_error" id="0x7f06003f" />
    <public type="color" name="design_default_color_on_primary" id="0x7f060040" />
    <public type="color" name="design_default_color_on_secondary" id="0x7f060041" />
    <public type="color" name="design_default_color_on_surface" id="0x7f060042" />
    <public type="color" name="design_default_color_primary" id="0x7f060043" />
    <public type="color" name="design_default_color_primary_dark" id="0x7f060044" />
    <public type="color" name="design_default_color_primary_variant" id="0x7f060045" />
    <public type="color" name="design_default_color_secondary" id="0x7f060046" />
    <public type="color" name="design_default_color_secondary_variant" id="0x7f060047" />
    <public type="color" name="design_default_color_surface" id="0x7f060048" />
    <public type="color" name="design_error" id="0x7f060049" />
    <public type="color" name="design_fab_shadow_end_color" id="0x7f06004a" />
    <public type="color" name="design_fab_shadow_mid_color" id="0x7f06004b" />
    <public type="color" name="design_fab_shadow_start_color" id="0x7f06004c" />
    <public type="color" name="design_fab_stroke_end_inner_color" id="0x7f06004d" />
    <public type="color" name="design_fab_stroke_end_outer_color" id="0x7f06004e" />
    <public type="color" name="design_fab_stroke_top_inner_color" id="0x7f06004f" />
    <public type="color" name="design_fab_stroke_top_outer_color" id="0x7f060050" />
    <public type="color" name="design_icon_tint" id="0x7f060051" />
    <public type="color" name="design_snackbar_background_color" id="0x7f060052" />
    <public type="color" name="dim_foreground_disabled_material_dark" id="0x7f060053" />
    <public type="color" name="dim_foreground_disabled_material_light" id="0x7f060054" />
    <public type="color" name="dim_foreground_material_dark" id="0x7f060055" />
    <public type="color" name="dim_foreground_material_light" id="0x7f060056" />
    <public type="color" name="error_color_material_dark" id="0x7f060057" />
    <public type="color" name="error_color_material_light" id="0x7f060058" />
    <public type="color" name="foreground_material_dark" id="0x7f060059" />
    <public type="color" name="foreground_material_light" id="0x7f06005a" />
    <public type="color" name="highlighted_text_material_dark" id="0x7f06005b" />
    <public type="color" name="highlighted_text_material_light" id="0x7f06005c" />
    <public type="color" name="m3_appbar_overlay_color" id="0x7f06005d" />
    <public type="color" name="m3_assist_chip_icon_tint_color" id="0x7f06005e" />
    <public type="color" name="m3_assist_chip_stroke_color" id="0x7f06005f" />
    <public type="color" name="m3_button_background_color_selector" id="0x7f060060" />
    <public type="color" name="m3_button_foreground_color_selector" id="0x7f060061" />
    <public type="color" name="m3_button_outline_color_selector" id="0x7f060062" />
    <public type="color" name="m3_button_ripple_color" id="0x7f060063" />
    <public type="color" name="m3_button_ripple_color_selector" id="0x7f060064" />
    <public type="color" name="m3_calendar_item_disabled_text" id="0x7f060065" />
    <public type="color" name="m3_calendar_item_stroke_color" id="0x7f060066" />
    <public type="color" name="m3_card_foreground_color" id="0x7f060067" />
    <public type="color" name="m3_card_ripple_color" id="0x7f060068" />
    <public type="color" name="m3_card_stroke_color" id="0x7f060069" />
    <public type="color" name="m3_checkbox_button_icon_tint" id="0x7f06006a" />
    <public type="color" name="m3_checkbox_button_tint" id="0x7f06006b" />
    <public type="color" name="m3_chip_assist_text_color" id="0x7f06006c" />
    <public type="color" name="m3_chip_background_color" id="0x7f06006d" />
    <public type="color" name="m3_chip_ripple_color" id="0x7f06006e" />
    <public type="color" name="m3_chip_stroke_color" id="0x7f06006f" />
    <public type="color" name="m3_chip_text_color" id="0x7f060070" />
    <public type="color" name="m3_dark_default_color_primary_text" id="0x7f060071" />
    <public type="color" name="m3_dark_default_color_secondary_text" id="0x7f060072" />
    <public type="color" name="m3_dark_highlighted_text" id="0x7f060073" />
    <public type="color" name="m3_dark_hint_foreground" id="0x7f060074" />
    <public type="color" name="m3_dark_primary_text_disable_only" id="0x7f060075" />
    <public type="color" name="m3_default_color_primary_text" id="0x7f060076" />
    <public type="color" name="m3_default_color_secondary_text" id="0x7f060077" />
    <public type="color" name="m3_dynamic_dark_default_color_primary_text" id="0x7f060078" />
    <public type="color" name="m3_dynamic_dark_default_color_secondary_text" id="0x7f060079" />
    <public type="color" name="m3_dynamic_dark_highlighted_text" id="0x7f06007a" />
    <public type="color" name="m3_dynamic_dark_hint_foreground" id="0x7f06007b" />
    <public type="color" name="m3_dynamic_dark_primary_text_disable_only" id="0x7f06007c" />
    <public type="color" name="m3_dynamic_default_color_primary_text" id="0x7f06007d" />
    <public type="color" name="m3_dynamic_default_color_secondary_text" id="0x7f06007e" />
    <public type="color" name="m3_dynamic_highlighted_text" id="0x7f06007f" />
    <public type="color" name="m3_dynamic_hint_foreground" id="0x7f060080" />
    <public type="color" name="m3_dynamic_primary_text_disable_only" id="0x7f060081" />
    <public type="color" name="m3_efab_ripple_color_selector" id="0x7f060082" />
    <public type="color" name="m3_elevated_chip_background_color" id="0x7f060083" />
    <public type="color" name="m3_fab_efab_background_color_selector" id="0x7f060084" />
    <public type="color" name="m3_fab_efab_foreground_color_selector" id="0x7f060085" />
    <public type="color" name="m3_fab_ripple_color_selector" id="0x7f060086" />
    <public type="color" name="m3_filled_icon_button_container_color_selector" id="0x7f060087" />
    <public type="color" name="m3_highlighted_text" id="0x7f060088" />
    <public type="color" name="m3_hint_foreground" id="0x7f060089" />
    <public type="color" name="m3_icon_button_icon_color_selector" id="0x7f06008a" />
    <public type="color" name="m3_navigation_bar_item_with_indicator_icon_tint" id="0x7f06008b" />
    <public type="color" name="m3_navigation_bar_item_with_indicator_label_tint" id="0x7f06008c" />
    <public type="color" name="m3_navigation_bar_ripple_color_selector" id="0x7f06008d" />
    <public type="color" name="m3_navigation_item_background_color" id="0x7f06008e" />
    <public type="color" name="m3_navigation_item_icon_tint" id="0x7f06008f" />
    <public type="color" name="m3_navigation_item_ripple_color" id="0x7f060090" />
    <public type="color" name="m3_navigation_item_text_color" id="0x7f060091" />
    <public type="color" name="m3_popupmenu_overlay_color" id="0x7f060092" />
    <public type="color" name="m3_primary_text_disable_only" id="0x7f060093" />
    <public type="color" name="m3_radiobutton_button_tint" id="0x7f060094" />
    <public type="color" name="m3_radiobutton_ripple_tint" id="0x7f060095" />
    <public type="color" name="m3_ref_palette_black" id="0x7f060096" />
    <public type="color" name="m3_ref_palette_dynamic_neutral0" id="0x7f060097" />
    <public type="color" name="m3_ref_palette_dynamic_neutral10" id="0x7f060098" />
    <public type="color" name="m3_ref_palette_dynamic_neutral100" id="0x7f060099" />
    <public type="color" name="m3_ref_palette_dynamic_neutral20" id="0x7f06009a" />
    <public type="color" name="m3_ref_palette_dynamic_neutral30" id="0x7f06009b" />
    <public type="color" name="m3_ref_palette_dynamic_neutral40" id="0x7f06009c" />
    <public type="color" name="m3_ref_palette_dynamic_neutral50" id="0x7f06009d" />
    <public type="color" name="m3_ref_palette_dynamic_neutral60" id="0x7f06009e" />
    <public type="color" name="m3_ref_palette_dynamic_neutral70" id="0x7f06009f" />
    <public type="color" name="m3_ref_palette_dynamic_neutral80" id="0x7f0600a0" />
    <public type="color" name="m3_ref_palette_dynamic_neutral90" id="0x7f0600a1" />
    <public type="color" name="m3_ref_palette_dynamic_neutral95" id="0x7f0600a2" />
    <public type="color" name="m3_ref_palette_dynamic_neutral99" id="0x7f0600a3" />
    <public type="color" name="m3_ref_palette_dynamic_neutral_variant0" id="0x7f0600a4" />
    <public type="color" name="m3_ref_palette_dynamic_neutral_variant10" id="0x7f0600a5" />
    <public type="color" name="m3_ref_palette_dynamic_neutral_variant100" id="0x7f0600a6" />
    <public type="color" name="m3_ref_palette_dynamic_neutral_variant20" id="0x7f0600a7" />
    <public type="color" name="m3_ref_palette_dynamic_neutral_variant30" id="0x7f0600a8" />
    <public type="color" name="m3_ref_palette_dynamic_neutral_variant40" id="0x7f0600a9" />
    <public type="color" name="m3_ref_palette_dynamic_neutral_variant50" id="0x7f0600aa" />
    <public type="color" name="m3_ref_palette_dynamic_neutral_variant60" id="0x7f0600ab" />
    <public type="color" name="m3_ref_palette_dynamic_neutral_variant70" id="0x7f0600ac" />
    <public type="color" name="m3_ref_palette_dynamic_neutral_variant80" id="0x7f0600ad" />
    <public type="color" name="m3_ref_palette_dynamic_neutral_variant90" id="0x7f0600ae" />
    <public type="color" name="m3_ref_palette_dynamic_neutral_variant95" id="0x7f0600af" />
    <public type="color" name="m3_ref_palette_dynamic_neutral_variant99" id="0x7f0600b0" />
    <public type="color" name="m3_ref_palette_dynamic_primary0" id="0x7f0600b1" />
    <public type="color" name="m3_ref_palette_dynamic_primary10" id="0x7f0600b2" />
    <public type="color" name="m3_ref_palette_dynamic_primary100" id="0x7f0600b3" />
    <public type="color" name="m3_ref_palette_dynamic_primary20" id="0x7f0600b4" />
    <public type="color" name="m3_ref_palette_dynamic_primary30" id="0x7f0600b5" />
    <public type="color" name="m3_ref_palette_dynamic_primary40" id="0x7f0600b6" />
    <public type="color" name="m3_ref_palette_dynamic_primary50" id="0x7f0600b7" />
    <public type="color" name="m3_ref_palette_dynamic_primary60" id="0x7f0600b8" />
    <public type="color" name="m3_ref_palette_dynamic_primary70" id="0x7f0600b9" />
    <public type="color" name="m3_ref_palette_dynamic_primary80" id="0x7f0600ba" />
    <public type="color" name="m3_ref_palette_dynamic_primary90" id="0x7f0600bb" />
    <public type="color" name="m3_ref_palette_dynamic_primary95" id="0x7f0600bc" />
    <public type="color" name="m3_ref_palette_dynamic_primary99" id="0x7f0600bd" />
    <public type="color" name="m3_ref_palette_dynamic_secondary0" id="0x7f0600be" />
    <public type="color" name="m3_ref_palette_dynamic_secondary10" id="0x7f0600bf" />
    <public type="color" name="m3_ref_palette_dynamic_secondary100" id="0x7f0600c0" />
    <public type="color" name="m3_ref_palette_dynamic_secondary20" id="0x7f0600c1" />
    <public type="color" name="m3_ref_palette_dynamic_secondary30" id="0x7f0600c2" />
    <public type="color" name="m3_ref_palette_dynamic_secondary40" id="0x7f0600c3" />
    <public type="color" name="m3_ref_palette_dynamic_secondary50" id="0x7f0600c4" />
    <public type="color" name="m3_ref_palette_dynamic_secondary60" id="0x7f0600c5" />
    <public type="color" name="m3_ref_palette_dynamic_secondary70" id="0x7f0600c6" />
    <public type="color" name="m3_ref_palette_dynamic_secondary80" id="0x7f0600c7" />
    <public type="color" name="m3_ref_palette_dynamic_secondary90" id="0x7f0600c8" />
    <public type="color" name="m3_ref_palette_dynamic_secondary95" id="0x7f0600c9" />
    <public type="color" name="m3_ref_palette_dynamic_secondary99" id="0x7f0600ca" />
    <public type="color" name="m3_ref_palette_dynamic_tertiary0" id="0x7f0600cb" />
    <public type="color" name="m3_ref_palette_dynamic_tertiary10" id="0x7f0600cc" />
    <public type="color" name="m3_ref_palette_dynamic_tertiary100" id="0x7f0600cd" />
    <public type="color" name="m3_ref_palette_dynamic_tertiary20" id="0x7f0600ce" />
    <public type="color" name="m3_ref_palette_dynamic_tertiary30" id="0x7f0600cf" />
    <public type="color" name="m3_ref_palette_dynamic_tertiary40" id="0x7f0600d0" />
    <public type="color" name="m3_ref_palette_dynamic_tertiary50" id="0x7f0600d1" />
    <public type="color" name="m3_ref_palette_dynamic_tertiary60" id="0x7f0600d2" />
    <public type="color" name="m3_ref_palette_dynamic_tertiary70" id="0x7f0600d3" />
    <public type="color" name="m3_ref_palette_dynamic_tertiary80" id="0x7f0600d4" />
    <public type="color" name="m3_ref_palette_dynamic_tertiary90" id="0x7f0600d5" />
    <public type="color" name="m3_ref_palette_dynamic_tertiary95" id="0x7f0600d6" />
    <public type="color" name="m3_ref_palette_dynamic_tertiary99" id="0x7f0600d7" />
    <public type="color" name="m3_ref_palette_error0" id="0x7f0600d8" />
    <public type="color" name="m3_ref_palette_error10" id="0x7f0600d9" />
    <public type="color" name="m3_ref_palette_error100" id="0x7f0600da" />
    <public type="color" name="m3_ref_palette_error20" id="0x7f0600db" />
    <public type="color" name="m3_ref_palette_error30" id="0x7f0600dc" />
    <public type="color" name="m3_ref_palette_error40" id="0x7f0600dd" />
    <public type="color" name="m3_ref_palette_error50" id="0x7f0600de" />
    <public type="color" name="m3_ref_palette_error60" id="0x7f0600df" />
    <public type="color" name="m3_ref_palette_error70" id="0x7f0600e0" />
    <public type="color" name="m3_ref_palette_error80" id="0x7f0600e1" />
    <public type="color" name="m3_ref_palette_error90" id="0x7f0600e2" />
    <public type="color" name="m3_ref_palette_error95" id="0x7f0600e3" />
    <public type="color" name="m3_ref_palette_error99" id="0x7f0600e4" />
    <public type="color" name="m3_ref_palette_neutral0" id="0x7f0600e5" />
    <public type="color" name="m3_ref_palette_neutral10" id="0x7f0600e6" />
    <public type="color" name="m3_ref_palette_neutral100" id="0x7f0600e7" />
    <public type="color" name="m3_ref_palette_neutral20" id="0x7f0600e8" />
    <public type="color" name="m3_ref_palette_neutral30" id="0x7f0600e9" />
    <public type="color" name="m3_ref_palette_neutral40" id="0x7f0600ea" />
    <public type="color" name="m3_ref_palette_neutral50" id="0x7f0600eb" />
    <public type="color" name="m3_ref_palette_neutral60" id="0x7f0600ec" />
    <public type="color" name="m3_ref_palette_neutral70" id="0x7f0600ed" />
    <public type="color" name="m3_ref_palette_neutral80" id="0x7f0600ee" />
    <public type="color" name="m3_ref_palette_neutral90" id="0x7f0600ef" />
    <public type="color" name="m3_ref_palette_neutral95" id="0x7f0600f0" />
    <public type="color" name="m3_ref_palette_neutral99" id="0x7f0600f1" />
    <public type="color" name="m3_ref_palette_neutral_variant0" id="0x7f0600f2" />
    <public type="color" name="m3_ref_palette_neutral_variant10" id="0x7f0600f3" />
    <public type="color" name="m3_ref_palette_neutral_variant100" id="0x7f0600f4" />
    <public type="color" name="m3_ref_palette_neutral_variant20" id="0x7f0600f5" />
    <public type="color" name="m3_ref_palette_neutral_variant30" id="0x7f0600f6" />
    <public type="color" name="m3_ref_palette_neutral_variant40" id="0x7f0600f7" />
    <public type="color" name="m3_ref_palette_neutral_variant50" id="0x7f0600f8" />
    <public type="color" name="m3_ref_palette_neutral_variant60" id="0x7f0600f9" />
    <public type="color" name="m3_ref_palette_neutral_variant70" id="0x7f0600fa" />
    <public type="color" name="m3_ref_palette_neutral_variant80" id="0x7f0600fb" />
    <public type="color" name="m3_ref_palette_neutral_variant90" id="0x7f0600fc" />
    <public type="color" name="m3_ref_palette_neutral_variant95" id="0x7f0600fd" />
    <public type="color" name="m3_ref_palette_neutral_variant99" id="0x7f0600fe" />
    <public type="color" name="m3_ref_palette_primary0" id="0x7f0600ff" />
    <public type="color" name="m3_ref_palette_primary10" id="0x7f060100" />
    <public type="color" name="m3_ref_palette_primary100" id="0x7f060101" />
    <public type="color" name="m3_ref_palette_primary20" id="0x7f060102" />
    <public type="color" name="m3_ref_palette_primary30" id="0x7f060103" />
    <public type="color" name="m3_ref_palette_primary40" id="0x7f060104" />
    <public type="color" name="m3_ref_palette_primary50" id="0x7f060105" />
    <public type="color" name="m3_ref_palette_primary60" id="0x7f060106" />
    <public type="color" name="m3_ref_palette_primary70" id="0x7f060107" />
    <public type="color" name="m3_ref_palette_primary80" id="0x7f060108" />
    <public type="color" name="m3_ref_palette_primary90" id="0x7f060109" />
    <public type="color" name="m3_ref_palette_primary95" id="0x7f06010a" />
    <public type="color" name="m3_ref_palette_primary99" id="0x7f06010b" />
    <public type="color" name="m3_ref_palette_secondary0" id="0x7f06010c" />
    <public type="color" name="m3_ref_palette_secondary10" id="0x7f06010d" />
    <public type="color" name="m3_ref_palette_secondary100" id="0x7f06010e" />
    <public type="color" name="m3_ref_palette_secondary20" id="0x7f06010f" />
    <public type="color" name="m3_ref_palette_secondary30" id="0x7f060110" />
    <public type="color" name="m3_ref_palette_secondary40" id="0x7f060111" />
    <public type="color" name="m3_ref_palette_secondary50" id="0x7f060112" />
    <public type="color" name="m3_ref_palette_secondary60" id="0x7f060113" />
    <public type="color" name="m3_ref_palette_secondary70" id="0x7f060114" />
    <public type="color" name="m3_ref_palette_secondary80" id="0x7f060115" />
    <public type="color" name="m3_ref_palette_secondary90" id="0x7f060116" />
    <public type="color" name="m3_ref_palette_secondary95" id="0x7f060117" />
    <public type="color" name="m3_ref_palette_secondary99" id="0x7f060118" />
    <public type="color" name="m3_ref_palette_tertiary0" id="0x7f060119" />
    <public type="color" name="m3_ref_palette_tertiary10" id="0x7f06011a" />
    <public type="color" name="m3_ref_palette_tertiary100" id="0x7f06011b" />
    <public type="color" name="m3_ref_palette_tertiary20" id="0x7f06011c" />
    <public type="color" name="m3_ref_palette_tertiary30" id="0x7f06011d" />
    <public type="color" name="m3_ref_palette_tertiary40" id="0x7f06011e" />
    <public type="color" name="m3_ref_palette_tertiary50" id="0x7f06011f" />
    <public type="color" name="m3_ref_palette_tertiary60" id="0x7f060120" />
    <public type="color" name="m3_ref_palette_tertiary70" id="0x7f060121" />
    <public type="color" name="m3_ref_palette_tertiary80" id="0x7f060122" />
    <public type="color" name="m3_ref_palette_tertiary90" id="0x7f060123" />
    <public type="color" name="m3_ref_palette_tertiary95" id="0x7f060124" />
    <public type="color" name="m3_ref_palette_tertiary99" id="0x7f060125" />
    <public type="color" name="m3_ref_palette_white" id="0x7f060126" />
    <public type="color" name="m3_selection_control_ripple_color_selector" id="0x7f060127" />
    <public type="color" name="m3_simple_item_ripple_color" id="0x7f060128" />
    <public type="color" name="m3_slider_active_track_color" id="0x7f060129" />
    <public type="color" name="m3_slider_halo_color" id="0x7f06012a" />
    <public type="color" name="m3_slider_inactive_track_color" id="0x7f06012b" />
    <public type="color" name="m3_slider_thumb_color" id="0x7f06012c" />
    <public type="color" name="m3_switch_thumb_tint" id="0x7f06012d" />
    <public type="color" name="m3_switch_track_tint" id="0x7f06012e" />
    <public type="color" name="m3_sys_color_dark_background" id="0x7f06012f" />
    <public type="color" name="m3_sys_color_dark_error" id="0x7f060130" />
    <public type="color" name="m3_sys_color_dark_error_container" id="0x7f060131" />
    <public type="color" name="m3_sys_color_dark_inverse_on_surface" id="0x7f060132" />
    <public type="color" name="m3_sys_color_dark_inverse_primary" id="0x7f060133" />
    <public type="color" name="m3_sys_color_dark_inverse_surface" id="0x7f060134" />
    <public type="color" name="m3_sys_color_dark_on_background" id="0x7f060135" />
    <public type="color" name="m3_sys_color_dark_on_error" id="0x7f060136" />
    <public type="color" name="m3_sys_color_dark_on_error_container" id="0x7f060137" />
    <public type="color" name="m3_sys_color_dark_on_primary" id="0x7f060138" />
    <public type="color" name="m3_sys_color_dark_on_primary_container" id="0x7f060139" />
    <public type="color" name="m3_sys_color_dark_on_secondary" id="0x7f06013a" />
    <public type="color" name="m3_sys_color_dark_on_secondary_container" id="0x7f06013b" />
    <public type="color" name="m3_sys_color_dark_on_surface" id="0x7f06013c" />
    <public type="color" name="m3_sys_color_dark_on_surface_variant" id="0x7f06013d" />
    <public type="color" name="m3_sys_color_dark_on_tertiary" id="0x7f06013e" />
    <public type="color" name="m3_sys_color_dark_on_tertiary_container" id="0x7f06013f" />
    <public type="color" name="m3_sys_color_dark_outline" id="0x7f060140" />
    <public type="color" name="m3_sys_color_dark_primary" id="0x7f060141" />
    <public type="color" name="m3_sys_color_dark_primary_container" id="0x7f060142" />
    <public type="color" name="m3_sys_color_dark_secondary" id="0x7f060143" />
    <public type="color" name="m3_sys_color_dark_secondary_container" id="0x7f060144" />
    <public type="color" name="m3_sys_color_dark_surface" id="0x7f060145" />
    <public type="color" name="m3_sys_color_dark_surface_variant" id="0x7f060146" />
    <public type="color" name="m3_sys_color_dark_tertiary" id="0x7f060147" />
    <public type="color" name="m3_sys_color_dark_tertiary_container" id="0x7f060148" />
    <public type="color" name="m3_sys_color_dynamic_dark_background" id="0x7f060149" />
    <public type="color" name="m3_sys_color_dynamic_dark_inverse_on_surface" id="0x7f06014a" />
    <public type="color" name="m3_sys_color_dynamic_dark_inverse_primary" id="0x7f06014b" />
    <public type="color" name="m3_sys_color_dynamic_dark_inverse_surface" id="0x7f06014c" />
    <public type="color" name="m3_sys_color_dynamic_dark_on_background" id="0x7f06014d" />
    <public type="color" name="m3_sys_color_dynamic_dark_on_primary" id="0x7f06014e" />
    <public type="color" name="m3_sys_color_dynamic_dark_on_primary_container" id="0x7f06014f" />
    <public type="color" name="m3_sys_color_dynamic_dark_on_secondary" id="0x7f060150" />
    <public type="color" name="m3_sys_color_dynamic_dark_on_secondary_container" id="0x7f060151" />
    <public type="color" name="m3_sys_color_dynamic_dark_on_surface" id="0x7f060152" />
    <public type="color" name="m3_sys_color_dynamic_dark_on_surface_variant" id="0x7f060153" />
    <public type="color" name="m3_sys_color_dynamic_dark_on_tertiary" id="0x7f060154" />
    <public type="color" name="m3_sys_color_dynamic_dark_on_tertiary_container" id="0x7f060155" />
    <public type="color" name="m3_sys_color_dynamic_dark_outline" id="0x7f060156" />
    <public type="color" name="m3_sys_color_dynamic_dark_primary" id="0x7f060157" />
    <public type="color" name="m3_sys_color_dynamic_dark_primary_container" id="0x7f060158" />
    <public type="color" name="m3_sys_color_dynamic_dark_secondary" id="0x7f060159" />
    <public type="color" name="m3_sys_color_dynamic_dark_secondary_container" id="0x7f06015a" />
    <public type="color" name="m3_sys_color_dynamic_dark_surface" id="0x7f06015b" />
    <public type="color" name="m3_sys_color_dynamic_dark_surface_variant" id="0x7f06015c" />
    <public type="color" name="m3_sys_color_dynamic_dark_tertiary" id="0x7f06015d" />
    <public type="color" name="m3_sys_color_dynamic_dark_tertiary_container" id="0x7f06015e" />
    <public type="color" name="m3_sys_color_dynamic_light_background" id="0x7f06015f" />
    <public type="color" name="m3_sys_color_dynamic_light_inverse_on_surface" id="0x7f060160" />
    <public type="color" name="m3_sys_color_dynamic_light_inverse_primary" id="0x7f060161" />
    <public type="color" name="m3_sys_color_dynamic_light_inverse_surface" id="0x7f060162" />
    <public type="color" name="m3_sys_color_dynamic_light_on_background" id="0x7f060163" />
    <public type="color" name="m3_sys_color_dynamic_light_on_primary" id="0x7f060164" />
    <public type="color" name="m3_sys_color_dynamic_light_on_primary_container" id="0x7f060165" />
    <public type="color" name="m3_sys_color_dynamic_light_on_secondary" id="0x7f060166" />
    <public type="color" name="m3_sys_color_dynamic_light_on_secondary_container" id="0x7f060167" />
    <public type="color" name="m3_sys_color_dynamic_light_on_surface" id="0x7f060168" />
    <public type="color" name="m3_sys_color_dynamic_light_on_surface_variant" id="0x7f060169" />
    <public type="color" name="m3_sys_color_dynamic_light_on_tertiary" id="0x7f06016a" />
    <public type="color" name="m3_sys_color_dynamic_light_on_tertiary_container" id="0x7f06016b" />
    <public type="color" name="m3_sys_color_dynamic_light_outline" id="0x7f06016c" />
    <public type="color" name="m3_sys_color_dynamic_light_primary" id="0x7f06016d" />
    <public type="color" name="m3_sys_color_dynamic_light_primary_container" id="0x7f06016e" />
    <public type="color" name="m3_sys_color_dynamic_light_secondary" id="0x7f06016f" />
    <public type="color" name="m3_sys_color_dynamic_light_secondary_container" id="0x7f060170" />
    <public type="color" name="m3_sys_color_dynamic_light_surface" id="0x7f060171" />
    <public type="color" name="m3_sys_color_dynamic_light_surface_variant" id="0x7f060172" />
    <public type="color" name="m3_sys_color_dynamic_light_tertiary" id="0x7f060173" />
    <public type="color" name="m3_sys_color_dynamic_light_tertiary_container" id="0x7f060174" />
    <public type="color" name="m3_sys_color_light_background" id="0x7f060175" />
    <public type="color" name="m3_sys_color_light_error" id="0x7f060176" />
    <public type="color" name="m3_sys_color_light_error_container" id="0x7f060177" />
    <public type="color" name="m3_sys_color_light_inverse_on_surface" id="0x7f060178" />
    <public type="color" name="m3_sys_color_light_inverse_primary" id="0x7f060179" />
    <public type="color" name="m3_sys_color_light_inverse_surface" id="0x7f06017a" />
    <public type="color" name="m3_sys_color_light_on_background" id="0x7f06017b" />
    <public type="color" name="m3_sys_color_light_on_error" id="0x7f06017c" />
    <public type="color" name="m3_sys_color_light_on_error_container" id="0x7f06017d" />
    <public type="color" name="m3_sys_color_light_on_primary" id="0x7f06017e" />
    <public type="color" name="m3_sys_color_light_on_primary_container" id="0x7f06017f" />
    <public type="color" name="m3_sys_color_light_on_secondary" id="0x7f060180" />
    <public type="color" name="m3_sys_color_light_on_secondary_container" id="0x7f060181" />
    <public type="color" name="m3_sys_color_light_on_surface" id="0x7f060182" />
    <public type="color" name="m3_sys_color_light_on_surface_variant" id="0x7f060183" />
    <public type="color" name="m3_sys_color_light_on_tertiary" id="0x7f060184" />
    <public type="color" name="m3_sys_color_light_on_tertiary_container" id="0x7f060185" />
    <public type="color" name="m3_sys_color_light_outline" id="0x7f060186" />
    <public type="color" name="m3_sys_color_light_primary" id="0x7f060187" />
    <public type="color" name="m3_sys_color_light_primary_container" id="0x7f060188" />
    <public type="color" name="m3_sys_color_light_secondary" id="0x7f060189" />
    <public type="color" name="m3_sys_color_light_secondary_container" id="0x7f06018a" />
    <public type="color" name="m3_sys_color_light_surface" id="0x7f06018b" />
    <public type="color" name="m3_sys_color_light_surface_variant" id="0x7f06018c" />
    <public type="color" name="m3_sys_color_light_tertiary" id="0x7f06018d" />
    <public type="color" name="m3_sys_color_light_tertiary_container" id="0x7f06018e" />
    <public type="color" name="m3_tabs_icon_color" id="0x7f06018f" />
    <public type="color" name="m3_tabs_ripple_color" id="0x7f060190" />
    <public type="color" name="m3_text_button_background_color_selector" id="0x7f060191" />
    <public type="color" name="m3_text_button_foreground_color_selector" id="0x7f060192" />
    <public type="color" name="m3_text_button_ripple_color_selector" id="0x7f060193" />
    <public type="color" name="m3_textfield_filled_background_color" id="0x7f060194" />
    <public type="color" name="m3_textfield_indicator_text_color" id="0x7f060195" />
    <public type="color" name="m3_textfield_input_text_color" id="0x7f060196" />
    <public type="color" name="m3_textfield_label_color" id="0x7f060197" />
    <public type="color" name="m3_textfield_stroke_color" id="0x7f060198" />
    <public type="color" name="m3_timepicker_button_background_color" id="0x7f060199" />
    <public type="color" name="m3_timepicker_button_ripple_color" id="0x7f06019a" />
    <public type="color" name="m3_timepicker_button_text_color" id="0x7f06019b" />
    <public type="color" name="m3_timepicker_clock_text_color" id="0x7f06019c" />
    <public type="color" name="m3_timepicker_display_background_color" id="0x7f06019d" />
    <public type="color" name="m3_timepicker_display_ripple_color" id="0x7f06019e" />
    <public type="color" name="m3_timepicker_display_stroke_color" id="0x7f06019f" />
    <public type="color" name="m3_timepicker_display_text_color" id="0x7f0601a0" />
    <public type="color" name="m3_timepicker_secondary_text_button_ripple_color" id="0x7f0601a1" />
    <public type="color" name="m3_timepicker_secondary_text_button_text_color" id="0x7f0601a2" />
    <public type="color" name="m3_tonal_button_ripple_color_selector" id="0x7f0601a3" />
    <public type="color" name="material_blue_grey_800" id="0x7f0601a4" />
    <public type="color" name="material_blue_grey_900" id="0x7f0601a5" />
    <public type="color" name="material_blue_grey_950" id="0x7f0601a6" />
    <public type="color" name="material_cursor_color" id="0x7f0601a7" />
    <public type="color" name="material_deep_teal_200" id="0x7f0601a8" />
    <public type="color" name="material_deep_teal_500" id="0x7f0601a9" />
    <public type="color" name="material_divider_color" id="0x7f0601aa" />
    <public type="color" name="material_dynamic_neutral0" id="0x7f0601ab" />
    <public type="color" name="material_dynamic_neutral10" id="0x7f0601ac" />
    <public type="color" name="material_dynamic_neutral100" id="0x7f0601ad" />
    <public type="color" name="material_dynamic_neutral20" id="0x7f0601ae" />
    <public type="color" name="material_dynamic_neutral30" id="0x7f0601af" />
    <public type="color" name="material_dynamic_neutral40" id="0x7f0601b0" />
    <public type="color" name="material_dynamic_neutral50" id="0x7f0601b1" />
    <public type="color" name="material_dynamic_neutral60" id="0x7f0601b2" />
    <public type="color" name="material_dynamic_neutral70" id="0x7f0601b3" />
    <public type="color" name="material_dynamic_neutral80" id="0x7f0601b4" />
    <public type="color" name="material_dynamic_neutral90" id="0x7f0601b5" />
    <public type="color" name="material_dynamic_neutral95" id="0x7f0601b6" />
    <public type="color" name="material_dynamic_neutral99" id="0x7f0601b7" />
    <public type="color" name="material_dynamic_neutral_variant0" id="0x7f0601b8" />
    <public type="color" name="material_dynamic_neutral_variant10" id="0x7f0601b9" />
    <public type="color" name="material_dynamic_neutral_variant100" id="0x7f0601ba" />
    <public type="color" name="material_dynamic_neutral_variant20" id="0x7f0601bb" />
    <public type="color" name="material_dynamic_neutral_variant30" id="0x7f0601bc" />
    <public type="color" name="material_dynamic_neutral_variant40" id="0x7f0601bd" />
    <public type="color" name="material_dynamic_neutral_variant50" id="0x7f0601be" />
    <public type="color" name="material_dynamic_neutral_variant60" id="0x7f0601bf" />
    <public type="color" name="material_dynamic_neutral_variant70" id="0x7f0601c0" />
    <public type="color" name="material_dynamic_neutral_variant80" id="0x7f0601c1" />
    <public type="color" name="material_dynamic_neutral_variant90" id="0x7f0601c2" />
    <public type="color" name="material_dynamic_neutral_variant95" id="0x7f0601c3" />
    <public type="color" name="material_dynamic_neutral_variant99" id="0x7f0601c4" />
    <public type="color" name="material_dynamic_primary0" id="0x7f0601c5" />
    <public type="color" name="material_dynamic_primary10" id="0x7f0601c6" />
    <public type="color" name="material_dynamic_primary100" id="0x7f0601c7" />
    <public type="color" name="material_dynamic_primary20" id="0x7f0601c8" />
    <public type="color" name="material_dynamic_primary30" id="0x7f0601c9" />
    <public type="color" name="material_dynamic_primary40" id="0x7f0601ca" />
    <public type="color" name="material_dynamic_primary50" id="0x7f0601cb" />
    <public type="color" name="material_dynamic_primary60" id="0x7f0601cc" />
    <public type="color" name="material_dynamic_primary70" id="0x7f0601cd" />
    <public type="color" name="material_dynamic_primary80" id="0x7f0601ce" />
    <public type="color" name="material_dynamic_primary90" id="0x7f0601cf" />
    <public type="color" name="material_dynamic_primary95" id="0x7f0601d0" />
    <public type="color" name="material_dynamic_primary99" id="0x7f0601d1" />
    <public type="color" name="material_dynamic_secondary0" id="0x7f0601d2" />
    <public type="color" name="material_dynamic_secondary10" id="0x7f0601d3" />
    <public type="color" name="material_dynamic_secondary100" id="0x7f0601d4" />
    <public type="color" name="material_dynamic_secondary20" id="0x7f0601d5" />
    <public type="color" name="material_dynamic_secondary30" id="0x7f0601d6" />
    <public type="color" name="material_dynamic_secondary40" id="0x7f0601d7" />
    <public type="color" name="material_dynamic_secondary50" id="0x7f0601d8" />
    <public type="color" name="material_dynamic_secondary60" id="0x7f0601d9" />
    <public type="color" name="material_dynamic_secondary70" id="0x7f0601da" />
    <public type="color" name="material_dynamic_secondary80" id="0x7f0601db" />
    <public type="color" name="material_dynamic_secondary90" id="0x7f0601dc" />
    <public type="color" name="material_dynamic_secondary95" id="0x7f0601dd" />
    <public type="color" name="material_dynamic_secondary99" id="0x7f0601de" />
    <public type="color" name="material_dynamic_tertiary0" id="0x7f0601df" />
    <public type="color" name="material_dynamic_tertiary10" id="0x7f0601e0" />
    <public type="color" name="material_dynamic_tertiary100" id="0x7f0601e1" />
    <public type="color" name="material_dynamic_tertiary20" id="0x7f0601e2" />
    <public type="color" name="material_dynamic_tertiary30" id="0x7f0601e3" />
    <public type="color" name="material_dynamic_tertiary40" id="0x7f0601e4" />
    <public type="color" name="material_dynamic_tertiary50" id="0x7f0601e5" />
    <public type="color" name="material_dynamic_tertiary60" id="0x7f0601e6" />
    <public type="color" name="material_dynamic_tertiary70" id="0x7f0601e7" />
    <public type="color" name="material_dynamic_tertiary80" id="0x7f0601e8" />
    <public type="color" name="material_dynamic_tertiary90" id="0x7f0601e9" />
    <public type="color" name="material_dynamic_tertiary95" id="0x7f0601ea" />
    <public type="color" name="material_dynamic_tertiary99" id="0x7f0601eb" />
    <public type="color" name="material_grey_100" id="0x7f0601ec" />
    <public type="color" name="material_grey_300" id="0x7f0601ed" />
    <public type="color" name="material_grey_50" id="0x7f0601ee" />
    <public type="color" name="material_grey_600" id="0x7f0601ef" />
    <public type="color" name="material_grey_800" id="0x7f0601f0" />
    <public type="color" name="material_grey_850" id="0x7f0601f1" />
    <public type="color" name="material_grey_900" id="0x7f0601f2" />
    <public type="color" name="material_harmonized_color_error" id="0x7f0601f3" />
    <public type="color" name="material_harmonized_color_error_container" id="0x7f0601f4" />
    <public type="color" name="material_harmonized_color_on_error" id="0x7f0601f5" />
    <public type="color" name="material_harmonized_color_on_error_container" id="0x7f0601f6" />
    <public type="color" name="material_on_background_disabled" id="0x7f0601f7" />
    <public type="color" name="material_on_background_emphasis_high_type" id="0x7f0601f8" />
    <public type="color" name="material_on_background_emphasis_medium" id="0x7f0601f9" />
    <public type="color" name="material_on_primary_disabled" id="0x7f0601fa" />
    <public type="color" name="material_on_primary_emphasis_high_type" id="0x7f0601fb" />
    <public type="color" name="material_on_primary_emphasis_medium" id="0x7f0601fc" />
    <public type="color" name="material_on_surface_disabled" id="0x7f0601fd" />
    <public type="color" name="material_on_surface_emphasis_high_type" id="0x7f0601fe" />
    <public type="color" name="material_on_surface_emphasis_medium" id="0x7f0601ff" />
    <public type="color" name="material_on_surface_stroke" id="0x7f060200" />
    <public type="color" name="material_slider_active_tick_marks_color" id="0x7f060201" />
    <public type="color" name="material_slider_active_track_color" id="0x7f060202" />
    <public type="color" name="material_slider_halo_color" id="0x7f060203" />
    <public type="color" name="material_slider_inactive_tick_marks_color" id="0x7f060204" />
    <public type="color" name="material_slider_inactive_track_color" id="0x7f060205" />
    <public type="color" name="material_slider_thumb_color" id="0x7f060206" />
    <public type="color" name="material_timepicker_button_background" id="0x7f060207" />
    <public type="color" name="material_timepicker_button_stroke" id="0x7f060208" />
    <public type="color" name="material_timepicker_clock_text_color" id="0x7f060209" />
    <public type="color" name="material_timepicker_clockface" id="0x7f06020a" />
    <public type="color" name="material_timepicker_modebutton_tint" id="0x7f06020b" />
    <public type="color" name="md_btn_selected" id="0x7f06020c" />
    <public type="color" name="md_btn_selected_dark" id="0x7f06020d" />
    <public type="color" name="md_divider_black" id="0x7f06020e" />
    <public type="color" name="md_divider_white" id="0x7f06020f" />
    <public type="color" name="md_edittext_error" id="0x7f060210" />
    <public type="color" name="md_material_blue_600" id="0x7f060211" />
    <public type="color" name="md_material_blue_800" id="0x7f060212" />
    <public type="color" name="mtrl_btn_bg_color_selector" id="0x7f060213" />
    <public type="color" name="mtrl_btn_ripple_color" id="0x7f060214" />
    <public type="color" name="mtrl_btn_stroke_color_selector" id="0x7f060215" />
    <public type="color" name="mtrl_btn_text_btn_bg_color_selector" id="0x7f060216" />
    <public type="color" name="mtrl_btn_text_btn_ripple_color" id="0x7f060217" />
    <public type="color" name="mtrl_btn_text_color_disabled" id="0x7f060218" />
    <public type="color" name="mtrl_btn_text_color_selector" id="0x7f060219" />
    <public type="color" name="mtrl_btn_transparent_bg_color" id="0x7f06021a" />
    <public type="color" name="mtrl_calendar_item_stroke_color" id="0x7f06021b" />
    <public type="color" name="mtrl_calendar_selected_range" id="0x7f06021c" />
    <public type="color" name="mtrl_card_view_foreground" id="0x7f06021d" />
    <public type="color" name="mtrl_card_view_ripple" id="0x7f06021e" />
    <public type="color" name="mtrl_chip_background_color" id="0x7f06021f" />
    <public type="color" name="mtrl_chip_close_icon_tint" id="0x7f060220" />
    <public type="color" name="mtrl_chip_surface_color" id="0x7f060221" />
    <public type="color" name="mtrl_chip_text_color" id="0x7f060222" />
    <public type="color" name="mtrl_choice_chip_background_color" id="0x7f060223" />
    <public type="color" name="mtrl_choice_chip_ripple_color" id="0x7f060224" />
    <public type="color" name="mtrl_choice_chip_text_color" id="0x7f060225" />
    <public type="color" name="mtrl_error" id="0x7f060226" />
    <public type="color" name="mtrl_fab_bg_color_selector" id="0x7f060227" />
    <public type="color" name="mtrl_fab_icon_text_color_selector" id="0x7f060228" />
    <public type="color" name="mtrl_fab_ripple_color" id="0x7f060229" />
    <public type="color" name="mtrl_filled_background_color" id="0x7f06022a" />
    <public type="color" name="mtrl_filled_icon_tint" id="0x7f06022b" />
    <public type="color" name="mtrl_filled_stroke_color" id="0x7f06022c" />
    <public type="color" name="mtrl_indicator_text_color" id="0x7f06022d" />
    <public type="color" name="mtrl_navigation_bar_colored_item_tint" id="0x7f06022e" />
    <public type="color" name="mtrl_navigation_bar_colored_ripple_color" id="0x7f06022f" />
    <public type="color" name="mtrl_navigation_bar_item_tint" id="0x7f060230" />
    <public type="color" name="mtrl_navigation_bar_ripple_color" id="0x7f060231" />
    <public type="color" name="mtrl_navigation_item_background_color" id="0x7f060232" />
    <public type="color" name="mtrl_navigation_item_icon_tint" id="0x7f060233" />
    <public type="color" name="mtrl_navigation_item_text_color" id="0x7f060234" />
    <public type="color" name="mtrl_on_primary_text_btn_text_color_selector" id="0x7f060235" />
    <public type="color" name="mtrl_on_surface_ripple_color" id="0x7f060236" />
    <public type="color" name="mtrl_outlined_icon_tint" id="0x7f060237" />
    <public type="color" name="mtrl_outlined_stroke_color" id="0x7f060238" />
    <public type="color" name="mtrl_popupmenu_overlay_color" id="0x7f060239" />
    <public type="color" name="mtrl_scrim_color" id="0x7f06023a" />
    <public type="color" name="mtrl_switch_thumb_icon_tint" id="0x7f06023b" />
    <public type="color" name="mtrl_switch_thumb_tint" id="0x7f06023c" />
    <public type="color" name="mtrl_switch_track_decoration_tint" id="0x7f06023d" />
    <public type="color" name="mtrl_switch_track_tint" id="0x7f06023e" />
    <public type="color" name="mtrl_tabs_colored_ripple_color" id="0x7f06023f" />
    <public type="color" name="mtrl_tabs_icon_color_selector" id="0x7f060240" />
    <public type="color" name="mtrl_tabs_icon_color_selector_colored" id="0x7f060241" />
    <public type="color" name="mtrl_tabs_legacy_text_color_selector" id="0x7f060242" />
    <public type="color" name="mtrl_tabs_ripple_color" id="0x7f060243" />
    <public type="color" name="mtrl_text_btn_text_color_selector" id="0x7f060244" />
    <public type="color" name="mtrl_textinput_default_box_stroke_color" id="0x7f060245" />
    <public type="color" name="mtrl_textinput_disabled_color" id="0x7f060246" />
    <public type="color" name="mtrl_textinput_filled_box_default_background_color" id="0x7f060247" />
    <public type="color" name="mtrl_textinput_focused_box_stroke_color" id="0x7f060248" />
    <public type="color" name="mtrl_textinput_hovered_box_stroke_color" id="0x7f060249" />
    <public type="color" name="notification_action_color_filter" id="0x7f06024a" />
    <public type="color" name="notification_icon_bg_color" id="0x7f06024b" />
    <public type="color" name="primary_dark_material_dark" id="0x7f06024c" />
    <public type="color" name="primary_dark_material_light" id="0x7f06024d" />
    <public type="color" name="primary_material_dark" id="0x7f06024e" />
    <public type="color" name="primary_material_light" id="0x7f06024f" />
    <public type="color" name="primary_text_default_material_dark" id="0x7f060250" />
    <public type="color" name="primary_text_default_material_light" id="0x7f060251" />
    <public type="color" name="primary_text_disabled_material_dark" id="0x7f060252" />
    <public type="color" name="primary_text_disabled_material_light" id="0x7f060253" />
    <public type="color" name="ripple_material_dark" id="0x7f060254" />
    <public type="color" name="ripple_material_light" id="0x7f060255" />
    <public type="color" name="scriptColorAccent" id="0x7f060256" />
    <public type="color" name="scriptColorPrimary" id="0x7f060257" />
    <public type="color" name="scriptColorPrimaryDark" id="0x7f060258" />
    <public type="color" name="secondary_text_default_material_dark" id="0x7f060259" />
    <public type="color" name="secondary_text_default_material_light" id="0x7f06025a" />
    <public type="color" name="secondary_text_disabled_material_dark" id="0x7f06025b" />
    <public type="color" name="secondary_text_disabled_material_light" id="0x7f06025c" />
    <public type="color" name="spinner_item_text_color" id="0x7f06025d" />
    <public type="color" name="switch_thumb_disabled_material_dark" id="0x7f06025e" />
    <public type="color" name="switch_thumb_disabled_material_light" id="0x7f06025f" />
    <public type="color" name="switch_thumb_material_dark" id="0x7f060260" />
    <public type="color" name="switch_thumb_material_light" id="0x7f060261" />
    <public type="color" name="switch_thumb_normal_material_dark" id="0x7f060262" />
    <public type="color" name="switch_thumb_normal_material_light" id="0x7f060263" />
    <public type="color" name="tooltip_background_dark" id="0x7f060264" />
    <public type="color" name="tooltip_background_light" id="0x7f060265" />
    <public type="dimen" name="abc_action_bar_content_inset_material" id="0x7f070000" />
    <public type="dimen" name="abc_action_bar_content_inset_with_nav" id="0x7f070001" />
    <public type="dimen" name="abc_action_bar_default_height_material" id="0x7f070002" />
    <public type="dimen" name="abc_action_bar_default_padding_end_material" id="0x7f070003" />
    <public type="dimen" name="abc_action_bar_default_padding_start_material" id="0x7f070004" />
    <public type="dimen" name="abc_action_bar_elevation_material" id="0x7f070005" />
    <public type="dimen" name="abc_action_bar_icon_vertical_padding_material" id="0x7f070006" />
    <public type="dimen" name="abc_action_bar_overflow_padding_end_material" id="0x7f070007" />
    <public type="dimen" name="abc_action_bar_overflow_padding_start_material" id="0x7f070008" />
    <public type="dimen" name="abc_action_bar_stacked_max_height" id="0x7f070009" />
    <public type="dimen" name="abc_action_bar_stacked_tab_max_width" id="0x7f07000a" />
    <public type="dimen" name="abc_action_bar_subtitle_bottom_margin_material" id="0x7f07000b" />
    <public type="dimen" name="abc_action_bar_subtitle_top_margin_material" id="0x7f07000c" />
    <public type="dimen" name="abc_action_button_min_height_material" id="0x7f07000d" />
    <public type="dimen" name="abc_action_button_min_width_material" id="0x7f07000e" />
    <public type="dimen" name="abc_action_button_min_width_overflow_material" id="0x7f07000f" />
    <public type="dimen" name="abc_alert_dialog_button_bar_height" id="0x7f070010" />
    <public type="dimen" name="abc_alert_dialog_button_dimen" id="0x7f070011" />
    <public type="dimen" name="abc_button_inset_horizontal_material" id="0x7f070012" />
    <public type="dimen" name="abc_button_inset_vertical_material" id="0x7f070013" />
    <public type="dimen" name="abc_button_padding_horizontal_material" id="0x7f070014" />
    <public type="dimen" name="abc_button_padding_vertical_material" id="0x7f070015" />
    <public type="dimen" name="abc_cascading_menus_min_smallest_width" id="0x7f070016" />
    <public type="dimen" name="abc_config_prefDialogWidth" id="0x7f070017" />
    <public type="dimen" name="abc_control_corner_material" id="0x7f070018" />
    <public type="dimen" name="abc_control_inset_material" id="0x7f070019" />
    <public type="dimen" name="abc_control_padding_material" id="0x7f07001a" />
    <public type="dimen" name="abc_dialog_corner_radius_material" id="0x7f07001b" />
    <public type="dimen" name="abc_dialog_fixed_height_major" id="0x7f07001c" />
    <public type="dimen" name="abc_dialog_fixed_height_minor" id="0x7f07001d" />
    <public type="dimen" name="abc_dialog_fixed_width_major" id="0x7f07001e" />
    <public type="dimen" name="abc_dialog_fixed_width_minor" id="0x7f07001f" />
    <public type="dimen" name="abc_dialog_list_padding_bottom_no_buttons" id="0x7f070020" />
    <public type="dimen" name="abc_dialog_list_padding_top_no_title" id="0x7f070021" />
    <public type="dimen" name="abc_dialog_min_width_major" id="0x7f070022" />
    <public type="dimen" name="abc_dialog_min_width_minor" id="0x7f070023" />
    <public type="dimen" name="abc_dialog_padding_material" id="0x7f070024" />
    <public type="dimen" name="abc_dialog_padding_top_material" id="0x7f070025" />
    <public type="dimen" name="abc_dialog_title_divider_material" id="0x7f070026" />
    <public type="dimen" name="abc_disabled_alpha_material_dark" id="0x7f070027" />
    <public type="dimen" name="abc_disabled_alpha_material_light" id="0x7f070028" />
    <public type="dimen" name="abc_dropdownitem_icon_width" id="0x7f070029" />
    <public type="dimen" name="abc_dropdownitem_text_padding_left" id="0x7f07002a" />
    <public type="dimen" name="abc_dropdownitem_text_padding_right" id="0x7f07002b" />
    <public type="dimen" name="abc_edit_text_inset_bottom_material" id="0x7f07002c" />
    <public type="dimen" name="abc_edit_text_inset_horizontal_material" id="0x7f07002d" />
    <public type="dimen" name="abc_edit_text_inset_top_material" id="0x7f07002e" />
    <public type="dimen" name="abc_floating_window_z" id="0x7f07002f" />
    <public type="dimen" name="abc_list_item_height_large_material" id="0x7f070030" />
    <public type="dimen" name="abc_list_item_height_material" id="0x7f070031" />
    <public type="dimen" name="abc_list_item_height_small_material" id="0x7f070032" />
    <public type="dimen" name="abc_list_item_padding_horizontal_material" id="0x7f070033" />
    <public type="dimen" name="abc_panel_menu_list_width" id="0x7f070034" />
    <public type="dimen" name="abc_progress_bar_height_material" id="0x7f070035" />
    <public type="dimen" name="abc_search_view_preferred_height" id="0x7f070036" />
    <public type="dimen" name="abc_search_view_preferred_width" id="0x7f070037" />
    <public type="dimen" name="abc_seekbar_track_background_height_material" id="0x7f070038" />
    <public type="dimen" name="abc_seekbar_track_progress_height_material" id="0x7f070039" />
    <public type="dimen" name="abc_select_dialog_padding_start_material" id="0x7f07003a" />
    <public type="dimen" name="abc_star_big" id="0x7f07003b" />
    <public type="dimen" name="abc_star_medium" id="0x7f07003c" />
    <public type="dimen" name="abc_star_small" id="0x7f07003d" />
    <public type="dimen" name="abc_switch_padding" id="0x7f07003e" />
    <public type="dimen" name="abc_text_size_body_1_material" id="0x7f07003f" />
    <public type="dimen" name="abc_text_size_body_2_material" id="0x7f070040" />
    <public type="dimen" name="abc_text_size_button_material" id="0x7f070041" />
    <public type="dimen" name="abc_text_size_caption_material" id="0x7f070042" />
    <public type="dimen" name="abc_text_size_display_1_material" id="0x7f070043" />
    <public type="dimen" name="abc_text_size_display_2_material" id="0x7f070044" />
    <public type="dimen" name="abc_text_size_display_3_material" id="0x7f070045" />
    <public type="dimen" name="abc_text_size_display_4_material" id="0x7f070046" />
    <public type="dimen" name="abc_text_size_headline_material" id="0x7f070047" />
    <public type="dimen" name="abc_text_size_large_material" id="0x7f070048" />
    <public type="dimen" name="abc_text_size_medium_material" id="0x7f070049" />
    <public type="dimen" name="abc_text_size_menu_header_material" id="0x7f07004a" />
    <public type="dimen" name="abc_text_size_menu_material" id="0x7f07004b" />
    <public type="dimen" name="abc_text_size_small_material" id="0x7f07004c" />
    <public type="dimen" name="abc_text_size_subhead_material" id="0x7f07004d" />
    <public type="dimen" name="abc_text_size_subtitle_material_toolbar" id="0x7f07004e" />
    <public type="dimen" name="abc_text_size_title_material" id="0x7f07004f" />
    <public type="dimen" name="abc_text_size_title_material_toolbar" id="0x7f070050" />
    <public type="dimen" name="appcompat_dialog_background_inset" id="0x7f070051" />
    <public type="dimen" name="cardview_compat_inset_shadow" id="0x7f070052" />
    <public type="dimen" name="cardview_default_elevation" id="0x7f070053" />
    <public type="dimen" name="cardview_default_radius" id="0x7f070054" />
    <public type="dimen" name="circular_progress_border" id="0x7f070055" />
    <public type="dimen" name="clock_face_margin_start" id="0x7f070056" />
    <public type="dimen" name="compat_button_inset_horizontal_material" id="0x7f070057" />
    <public type="dimen" name="compat_button_inset_vertical_material" id="0x7f070058" />
    <public type="dimen" name="compat_button_padding_horizontal_material" id="0x7f070059" />
    <public type="dimen" name="compat_button_padding_vertical_material" id="0x7f07005a" />
    <public type="dimen" name="compat_control_corner_material" id="0x7f07005b" />
    <public type="dimen" name="compat_notification_large_icon_max_height" id="0x7f07005c" />
    <public type="dimen" name="compat_notification_large_icon_max_width" id="0x7f07005d" />
    <public type="dimen" name="def_drawer_elevation" id="0x7f07005e" />
    <public type="dimen" name="design_appbar_elevation" id="0x7f07005f" />
    <public type="dimen" name="design_bottom_navigation_active_item_max_width" id="0x7f070060" />
    <public type="dimen" name="design_bottom_navigation_active_item_min_width" id="0x7f070061" />
    <public type="dimen" name="design_bottom_navigation_active_text_size" id="0x7f070062" />
    <public type="dimen" name="design_bottom_navigation_elevation" id="0x7f070063" />
    <public type="dimen" name="design_bottom_navigation_height" id="0x7f070064" />
    <public type="dimen" name="design_bottom_navigation_icon_size" id="0x7f070065" />
    <public type="dimen" name="design_bottom_navigation_item_max_width" id="0x7f070066" />
    <public type="dimen" name="design_bottom_navigation_item_min_width" id="0x7f070067" />
    <public type="dimen" name="design_bottom_navigation_label_padding" id="0x7f070068" />
    <public type="dimen" name="design_bottom_navigation_margin" id="0x7f070069" />
    <public type="dimen" name="design_bottom_navigation_shadow_height" id="0x7f07006a" />
    <public type="dimen" name="design_bottom_navigation_text_size" id="0x7f07006b" />
    <public type="dimen" name="design_bottom_sheet_elevation" id="0x7f07006c" />
    <public type="dimen" name="design_bottom_sheet_modal_elevation" id="0x7f07006d" />
    <public type="dimen" name="design_bottom_sheet_peek_height_min" id="0x7f07006e" />
    <public type="dimen" name="design_fab_border_width" id="0x7f07006f" />
    <public type="dimen" name="design_fab_elevation" id="0x7f070070" />
    <public type="dimen" name="design_fab_image_size" id="0x7f070071" />
    <public type="dimen" name="design_fab_size_mini" id="0x7f070072" />
    <public type="dimen" name="design_fab_size_normal" id="0x7f070073" />
    <public type="dimen" name="design_fab_translation_z_hovered_focused" id="0x7f070074" />
    <public type="dimen" name="design_fab_translation_z_pressed" id="0x7f070075" />
    <public type="dimen" name="design_navigation_elevation" id="0x7f070076" />
    <public type="dimen" name="design_navigation_icon_padding" id="0x7f070077" />
    <public type="dimen" name="design_navigation_icon_size" id="0x7f070078" />
    <public type="dimen" name="design_navigation_item_horizontal_padding" id="0x7f070079" />
    <public type="dimen" name="design_navigation_item_icon_padding" id="0x7f07007a" />
    <public type="dimen" name="design_navigation_item_vertical_padding" id="0x7f07007b" />
    <public type="dimen" name="design_navigation_max_width" id="0x7f07007c" />
    <public type="dimen" name="design_navigation_padding_bottom" id="0x7f07007d" />
    <public type="dimen" name="design_navigation_separator_vertical_padding" id="0x7f07007e" />
    <public type="dimen" name="design_snackbar_action_inline_max_width" id="0x7f07007f" />
    <public type="dimen" name="design_snackbar_action_text_color_alpha" id="0x7f070080" />
    <public type="dimen" name="design_snackbar_background_corner_radius" id="0x7f070081" />
    <public type="dimen" name="design_snackbar_elevation" id="0x7f070082" />
    <public type="dimen" name="design_snackbar_extra_spacing_horizontal" id="0x7f070083" />
    <public type="dimen" name="design_snackbar_max_width" id="0x7f070084" />
    <public type="dimen" name="design_snackbar_min_width" id="0x7f070085" />
    <public type="dimen" name="design_snackbar_padding_horizontal" id="0x7f070086" />
    <public type="dimen" name="design_snackbar_padding_vertical" id="0x7f070087" />
    <public type="dimen" name="design_snackbar_padding_vertical_2lines" id="0x7f070088" />
    <public type="dimen" name="design_snackbar_text_size" id="0x7f070089" />
    <public type="dimen" name="design_tab_max_width" id="0x7f07008a" />
    <public type="dimen" name="design_tab_scrollable_min_width" id="0x7f07008b" />
    <public type="dimen" name="design_tab_text_size" id="0x7f07008c" />
    <public type="dimen" name="design_tab_text_size_2line" id="0x7f07008d" />
    <public type="dimen" name="design_textinput_caption_translate_y" id="0x7f07008e" />
    <public type="dimen" name="disabled_alpha_material_dark" id="0x7f07008f" />
    <public type="dimen" name="disabled_alpha_material_light" id="0x7f070090" />
    <public type="dimen" name="fastscroll_default_thickness" id="0x7f070091" />
    <public type="dimen" name="fastscroll_margin" id="0x7f070092" />
    <public type="dimen" name="fastscroll_minimum_range" id="0x7f070093" />
    <public type="dimen" name="floaty_window_offset" id="0x7f070094" />
    <public type="dimen" name="highlight_alpha_material_colored" id="0x7f070095" />
    <public type="dimen" name="highlight_alpha_material_dark" id="0x7f070096" />
    <public type="dimen" name="highlight_alpha_material_light" id="0x7f070097" />
    <public type="dimen" name="hint_alpha_material_dark" id="0x7f070098" />
    <public type="dimen" name="hint_alpha_material_light" id="0x7f070099" />
    <public type="dimen" name="hint_pressed_alpha_material_dark" id="0x7f07009a" />
    <public type="dimen" name="hint_pressed_alpha_material_light" id="0x7f07009b" />
    <public type="dimen" name="item_touch_helper_max_drag_scroll_per_frame" id="0x7f07009c" />
    <public type="dimen" name="item_touch_helper_swipe_escape_max_velocity" id="0x7f07009d" />
    <public type="dimen" name="item_touch_helper_swipe_escape_velocity" id="0x7f07009e" />
    <public type="dimen" name="m3_alert_dialog_action_bottom_padding" id="0x7f07009f" />
    <public type="dimen" name="m3_alert_dialog_action_top_padding" id="0x7f0700a0" />
    <public type="dimen" name="m3_alert_dialog_corner_size" id="0x7f0700a1" />
    <public type="dimen" name="m3_alert_dialog_elevation" id="0x7f0700a2" />
    <public type="dimen" name="m3_alert_dialog_icon_margin" id="0x7f0700a3" />
    <public type="dimen" name="m3_alert_dialog_icon_size" id="0x7f0700a4" />
    <public type="dimen" name="m3_alert_dialog_title_bottom_margin" id="0x7f0700a5" />
    <public type="dimen" name="m3_appbar_expanded_title_margin_bottom" id="0x7f0700a6" />
    <public type="dimen" name="m3_appbar_expanded_title_margin_horizontal" id="0x7f0700a7" />
    <public type="dimen" name="m3_appbar_scrim_height_trigger" id="0x7f0700a8" />
    <public type="dimen" name="m3_appbar_scrim_height_trigger_large" id="0x7f0700a9" />
    <public type="dimen" name="m3_appbar_scrim_height_trigger_medium" id="0x7f0700aa" />
    <public type="dimen" name="m3_appbar_size_compact" id="0x7f0700ab" />
    <public type="dimen" name="m3_appbar_size_large" id="0x7f0700ac" />
    <public type="dimen" name="m3_appbar_size_medium" id="0x7f0700ad" />
    <public type="dimen" name="m3_badge_horizontal_offset" id="0x7f0700ae" />
    <public type="dimen" name="m3_badge_radius" id="0x7f0700af" />
    <public type="dimen" name="m3_badge_vertical_offset" id="0x7f0700b0" />
    <public type="dimen" name="m3_badge_with_text_horizontal_offset" id="0x7f0700b1" />
    <public type="dimen" name="m3_badge_with_text_radius" id="0x7f0700b2" />
    <public type="dimen" name="m3_badge_with_text_vertical_offset" id="0x7f0700b3" />
    <public type="dimen" name="m3_bottom_nav_item_active_indicator_height" id="0x7f0700b4" />
    <public type="dimen" name="m3_bottom_nav_item_active_indicator_margin_horizontal" id="0x7f0700b5" />
    <public type="dimen" name="m3_bottom_nav_item_active_indicator_width" id="0x7f0700b6" />
    <public type="dimen" name="m3_bottom_nav_item_padding_bottom" id="0x7f0700b7" />
    <public type="dimen" name="m3_bottom_nav_item_padding_top" id="0x7f0700b8" />
    <public type="dimen" name="m3_bottom_nav_min_height" id="0x7f0700b9" />
    <public type="dimen" name="m3_bottom_sheet_drag_handle_bottom_padding" id="0x7f0700ba" />
    <public type="dimen" name="m3_bottom_sheet_elevation" id="0x7f0700bb" />
    <public type="dimen" name="m3_bottom_sheet_modal_elevation" id="0x7f0700bc" />
    <public type="dimen" name="m3_bottomappbar_fab_cradle_margin" id="0x7f0700bd" />
    <public type="dimen" name="m3_bottomappbar_fab_cradle_rounded_corner_radius" id="0x7f0700be" />
    <public type="dimen" name="m3_bottomappbar_fab_cradle_vertical_offset" id="0x7f0700bf" />
    <public type="dimen" name="m3_bottomappbar_fab_end_margin" id="0x7f0700c0" />
    <public type="dimen" name="m3_bottomappbar_height" id="0x7f0700c1" />
    <public type="dimen" name="m3_bottomappbar_horizontal_padding" id="0x7f0700c2" />
    <public type="dimen" name="m3_btn_dialog_btn_min_width" id="0x7f0700c3" />
    <public type="dimen" name="m3_btn_dialog_btn_spacing" id="0x7f0700c4" />
    <public type="dimen" name="m3_btn_disabled_elevation" id="0x7f0700c5" />
    <public type="dimen" name="m3_btn_disabled_translation_z" id="0x7f0700c6" />
    <public type="dimen" name="m3_btn_elevated_btn_elevation" id="0x7f0700c7" />
    <public type="dimen" name="m3_btn_elevation" id="0x7f0700c8" />
    <public type="dimen" name="m3_btn_icon_btn_padding_left" id="0x7f0700c9" />
    <public type="dimen" name="m3_btn_icon_btn_padding_right" id="0x7f0700ca" />
    <public type="dimen" name="m3_btn_icon_only_default_padding" id="0x7f0700cb" />
    <public type="dimen" name="m3_btn_icon_only_default_size" id="0x7f0700cc" />
    <public type="dimen" name="m3_btn_icon_only_icon_padding" id="0x7f0700cd" />
    <public type="dimen" name="m3_btn_icon_only_min_width" id="0x7f0700ce" />
    <public type="dimen" name="m3_btn_inset" id="0x7f0700cf" />
    <public type="dimen" name="m3_btn_max_width" id="0x7f0700d0" />
    <public type="dimen" name="m3_btn_padding_bottom" id="0x7f0700d1" />
    <public type="dimen" name="m3_btn_padding_left" id="0x7f0700d2" />
    <public type="dimen" name="m3_btn_padding_right" id="0x7f0700d3" />
    <public type="dimen" name="m3_btn_padding_top" id="0x7f0700d4" />
    <public type="dimen" name="m3_btn_stroke_size" id="0x7f0700d5" />
    <public type="dimen" name="m3_btn_text_btn_icon_padding_left" id="0x7f0700d6" />
    <public type="dimen" name="m3_btn_text_btn_icon_padding_right" id="0x7f0700d7" />
    <public type="dimen" name="m3_btn_text_btn_padding_left" id="0x7f0700d8" />
    <public type="dimen" name="m3_btn_text_btn_padding_right" id="0x7f0700d9" />
    <public type="dimen" name="m3_btn_translation_z_base" id="0x7f0700da" />
    <public type="dimen" name="m3_btn_translation_z_hovered" id="0x7f0700db" />
    <public type="dimen" name="m3_card_dragged_z" id="0x7f0700dc" />
    <public type="dimen" name="m3_card_elevated_dragged_z" id="0x7f0700dd" />
    <public type="dimen" name="m3_card_elevated_elevation" id="0x7f0700de" />
    <public type="dimen" name="m3_card_elevated_hovered_z" id="0x7f0700df" />
    <public type="dimen" name="m3_card_elevation" id="0x7f0700e0" />
    <public type="dimen" name="m3_card_hovered_z" id="0x7f0700e1" />
    <public type="dimen" name="m3_card_stroke_width" id="0x7f0700e2" />
    <public type="dimen" name="m3_chip_checked_hovered_translation_z" id="0x7f0700e3" />
    <public type="dimen" name="m3_chip_corner_size" id="0x7f0700e4" />
    <public type="dimen" name="m3_chip_disabled_translation_z" id="0x7f0700e5" />
    <public type="dimen" name="m3_chip_dragged_translation_z" id="0x7f0700e6" />
    <public type="dimen" name="m3_chip_elevated_elevation" id="0x7f0700e7" />
    <public type="dimen" name="m3_chip_hovered_translation_z" id="0x7f0700e8" />
    <public type="dimen" name="m3_chip_icon_size" id="0x7f0700e9" />
    <public type="dimen" name="m3_comp_bottom_app_bar_container_elevation" id="0x7f0700ea" />
    <public type="dimen" name="m3_comp_bottom_app_bar_container_height" id="0x7f0700eb" />
    <public type="dimen" name="m3_comp_extended_fab_primary_container_elevation" id="0x7f0700ec" />
    <public type="dimen" name="m3_comp_extended_fab_primary_container_height" id="0x7f0700ed" />
    <public type="dimen" name="m3_comp_extended_fab_primary_focus_container_elevation" id="0x7f0700ee" />
    <public type="dimen" name="m3_comp_extended_fab_primary_focus_state_layer_opacity" id="0x7f0700ef" />
    <public type="dimen" name="m3_comp_extended_fab_primary_hover_container_elevation" id="0x7f0700f0" />
    <public type="dimen" name="m3_comp_extended_fab_primary_hover_state_layer_opacity" id="0x7f0700f1" />
    <public type="dimen" name="m3_comp_extended_fab_primary_icon_size" id="0x7f0700f2" />
    <public type="dimen" name="m3_comp_extended_fab_primary_pressed_container_elevation" id="0x7f0700f3" />
    <public type="dimen" name="m3_comp_extended_fab_primary_pressed_state_layer_opacity" id="0x7f0700f4" />
    <public type="dimen" name="m3_comp_fab_primary_container_elevation" id="0x7f0700f5" />
    <public type="dimen" name="m3_comp_fab_primary_container_height" id="0x7f0700f6" />
    <public type="dimen" name="m3_comp_fab_primary_focus_state_layer_opacity" id="0x7f0700f7" />
    <public type="dimen" name="m3_comp_fab_primary_hover_container_elevation" id="0x7f0700f8" />
    <public type="dimen" name="m3_comp_fab_primary_hover_state_layer_opacity" id="0x7f0700f9" />
    <public type="dimen" name="m3_comp_fab_primary_icon_size" id="0x7f0700fa" />
    <public type="dimen" name="m3_comp_fab_primary_large_container_height" id="0x7f0700fb" />
    <public type="dimen" name="m3_comp_fab_primary_large_icon_size" id="0x7f0700fc" />
    <public type="dimen" name="m3_comp_fab_primary_pressed_container_elevation" id="0x7f0700fd" />
    <public type="dimen" name="m3_comp_fab_primary_pressed_state_layer_opacity" id="0x7f0700fe" />
    <public type="dimen" name="m3_comp_fab_primary_small_container_height" id="0x7f0700ff" />
    <public type="dimen" name="m3_comp_fab_primary_small_icon_size" id="0x7f070100" />
    <public type="dimen" name="m3_comp_switch_disabled_handle_elevation" id="0x7f070101" />
    <public type="dimen" name="m3_comp_switch_disabled_handle_opacity" id="0x7f070102" />
    <public type="dimen" name="m3_comp_switch_disabled_selected_handle_opacity" id="0x7f070103" />
    <public type="dimen" name="m3_comp_switch_disabled_selected_icon_opacity" id="0x7f070104" />
    <public type="dimen" name="m3_comp_switch_disabled_track_opacity" id="0x7f070105" />
    <public type="dimen" name="m3_comp_switch_disabled_unselected_handle_opacity" id="0x7f070106" />
    <public type="dimen" name="m3_comp_switch_disabled_unselected_icon_opacity" id="0x7f070107" />
    <public type="dimen" name="m3_comp_switch_handle_elevation" id="0x7f070108" />
    <public type="dimen" name="m3_comp_switch_selected_focus_state_layer_opacity" id="0x7f070109" />
    <public type="dimen" name="m3_comp_switch_selected_hover_state_layer_opacity" id="0x7f07010a" />
    <public type="dimen" name="m3_comp_switch_selected_pressed_state_layer_opacity" id="0x7f07010b" />
    <public type="dimen" name="m3_comp_switch_track_height" id="0x7f07010c" />
    <public type="dimen" name="m3_comp_switch_track_width" id="0x7f07010d" />
    <public type="dimen" name="m3_comp_switch_unselected_focus_state_layer_opacity" id="0x7f07010e" />
    <public type="dimen" name="m3_comp_switch_unselected_hover_state_layer_opacity" id="0x7f07010f" />
    <public type="dimen" name="m3_comp_switch_unselected_pressed_state_layer_opacity" id="0x7f070110" />
    <public type="dimen" name="m3_datepicker_elevation" id="0x7f070111" />
    <public type="dimen" name="m3_divider_heavy_thickness" id="0x7f070112" />
    <public type="dimen" name="m3_exposed_dropdown_menu_popup_elevation" id="0x7f070113" />
    <public type="dimen" name="m3_extended_fab_bottom_padding" id="0x7f070114" />
    <public type="dimen" name="m3_extended_fab_end_padding" id="0x7f070115" />
    <public type="dimen" name="m3_extended_fab_icon_padding" id="0x7f070116" />
    <public type="dimen" name="m3_extended_fab_min_height" id="0x7f070117" />
    <public type="dimen" name="m3_extended_fab_start_padding" id="0x7f070118" />
    <public type="dimen" name="m3_extended_fab_top_padding" id="0x7f070119" />
    <public type="dimen" name="m3_fab_border_width" id="0x7f07011a" />
    <public type="dimen" name="m3_fab_corner_size" id="0x7f07011b" />
    <public type="dimen" name="m3_fab_translation_z_hovered_focused" id="0x7f07011c" />
    <public type="dimen" name="m3_fab_translation_z_pressed" id="0x7f07011d" />
    <public type="dimen" name="m3_large_fab_max_image_size" id="0x7f07011e" />
    <public type="dimen" name="m3_large_fab_size" id="0x7f07011f" />
    <public type="dimen" name="m3_menu_elevation" id="0x7f070120" />
    <public type="dimen" name="m3_navigation_drawer_layout_corner_size" id="0x7f070121" />
    <public type="dimen" name="m3_navigation_item_horizontal_padding" id="0x7f070122" />
    <public type="dimen" name="m3_navigation_item_icon_padding" id="0x7f070123" />
    <public type="dimen" name="m3_navigation_item_shape_inset_bottom" id="0x7f070124" />
    <public type="dimen" name="m3_navigation_item_shape_inset_end" id="0x7f070125" />
    <public type="dimen" name="m3_navigation_item_shape_inset_start" id="0x7f070126" />
    <public type="dimen" name="m3_navigation_item_shape_inset_top" id="0x7f070127" />
    <public type="dimen" name="m3_navigation_item_vertical_padding" id="0x7f070128" />
    <public type="dimen" name="m3_navigation_menu_divider_horizontal_padding" id="0x7f070129" />
    <public type="dimen" name="m3_navigation_menu_headline_horizontal_padding" id="0x7f07012a" />
    <public type="dimen" name="m3_navigation_rail_default_width" id="0x7f07012b" />
    <public type="dimen" name="m3_navigation_rail_item_active_indicator_height" id="0x7f07012c" />
    <public type="dimen" name="m3_navigation_rail_item_active_indicator_margin_horizontal" id="0x7f07012d" />
    <public type="dimen" name="m3_navigation_rail_item_active_indicator_width" id="0x7f07012e" />
    <public type="dimen" name="m3_navigation_rail_item_min_height" id="0x7f07012f" />
    <public type="dimen" name="m3_navigation_rail_item_padding_bottom" id="0x7f070130" />
    <public type="dimen" name="m3_navigation_rail_item_padding_top" id="0x7f070131" />
    <public type="dimen" name="m3_ripple_default_alpha" id="0x7f070132" />
    <public type="dimen" name="m3_ripple_focused_alpha" id="0x7f070133" />
    <public type="dimen" name="m3_ripple_hovered_alpha" id="0x7f070134" />
    <public type="dimen" name="m3_ripple_pressed_alpha" id="0x7f070135" />
    <public type="dimen" name="m3_ripple_selectable_pressed_alpha" id="0x7f070136" />
    <public type="dimen" name="m3_simple_item_color_hovered_alpha" id="0x7f070137" />
    <public type="dimen" name="m3_simple_item_color_selected_alpha" id="0x7f070138" />
    <public type="dimen" name="m3_slider_thumb_elevation" id="0x7f070139" />
    <public type="dimen" name="m3_small_fab_max_image_size" id="0x7f07013a" />
    <public type="dimen" name="m3_small_fab_size" id="0x7f07013b" />
    <public type="dimen" name="m3_snackbar_action_text_color_alpha" id="0x7f07013c" />
    <public type="dimen" name="m3_snackbar_margin" id="0x7f07013d" />
    <public type="dimen" name="m3_sys_elevation_level0" id="0x7f07013e" />
    <public type="dimen" name="m3_sys_elevation_level1" id="0x7f07013f" />
    <public type="dimen" name="m3_sys_elevation_level2" id="0x7f070140" />
    <public type="dimen" name="m3_sys_elevation_level3" id="0x7f070141" />
    <public type="dimen" name="m3_sys_elevation_level4" id="0x7f070142" />
    <public type="dimen" name="m3_sys_elevation_level5" id="0x7f070143" />
    <public type="dimen" name="m3_sys_motion_easing_emphasized_accelerate_control_x1" id="0x7f070144" />
    <public type="dimen" name="m3_sys_motion_easing_emphasized_accelerate_control_x2" id="0x7f070145" />
    <public type="dimen" name="m3_sys_motion_easing_emphasized_accelerate_control_y1" id="0x7f070146" />
    <public type="dimen" name="m3_sys_motion_easing_emphasized_accelerate_control_y2" id="0x7f070147" />
    <public type="dimen" name="m3_sys_motion_easing_emphasized_decelerate_control_x1" id="0x7f070148" />
    <public type="dimen" name="m3_sys_motion_easing_emphasized_decelerate_control_x2" id="0x7f070149" />
    <public type="dimen" name="m3_sys_motion_easing_emphasized_decelerate_control_y1" id="0x7f07014a" />
    <public type="dimen" name="m3_sys_motion_easing_emphasized_decelerate_control_y2" id="0x7f07014b" />
    <public type="dimen" name="m3_sys_motion_easing_legacy_accelerate_control_x1" id="0x7f07014c" />
    <public type="dimen" name="m3_sys_motion_easing_legacy_accelerate_control_x2" id="0x7f07014d" />
    <public type="dimen" name="m3_sys_motion_easing_legacy_accelerate_control_y1" id="0x7f07014e" />
    <public type="dimen" name="m3_sys_motion_easing_legacy_accelerate_control_y2" id="0x7f07014f" />
    <public type="dimen" name="m3_sys_motion_easing_legacy_control_x1" id="0x7f070150" />
    <public type="dimen" name="m3_sys_motion_easing_legacy_control_x2" id="0x7f070151" />
    <public type="dimen" name="m3_sys_motion_easing_legacy_control_y1" id="0x7f070152" />
    <public type="dimen" name="m3_sys_motion_easing_legacy_control_y2" id="0x7f070153" />
    <public type="dimen" name="m3_sys_motion_easing_legacy_decelerate_control_x1" id="0x7f070154" />
    <public type="dimen" name="m3_sys_motion_easing_legacy_decelerate_control_x2" id="0x7f070155" />
    <public type="dimen" name="m3_sys_motion_easing_legacy_decelerate_control_y1" id="0x7f070156" />
    <public type="dimen" name="m3_sys_motion_easing_legacy_decelerate_control_y2" id="0x7f070157" />
    <public type="dimen" name="m3_sys_motion_easing_linear_control_x1" id="0x7f070158" />
    <public type="dimen" name="m3_sys_motion_easing_linear_control_x2" id="0x7f070159" />
    <public type="dimen" name="m3_sys_motion_easing_linear_control_y1" id="0x7f07015a" />
    <public type="dimen" name="m3_sys_motion_easing_linear_control_y2" id="0x7f07015b" />
    <public type="dimen" name="m3_sys_motion_easing_standard_accelerate_control_x1" id="0x7f07015c" />
    <public type="dimen" name="m3_sys_motion_easing_standard_accelerate_control_x2" id="0x7f07015d" />
    <public type="dimen" name="m3_sys_motion_easing_standard_accelerate_control_y1" id="0x7f07015e" />
    <public type="dimen" name="m3_sys_motion_easing_standard_accelerate_control_y2" id="0x7f07015f" />
    <public type="dimen" name="m3_sys_motion_easing_standard_control_x1" id="0x7f070160" />
    <public type="dimen" name="m3_sys_motion_easing_standard_control_x2" id="0x7f070161" />
    <public type="dimen" name="m3_sys_motion_easing_standard_control_y1" id="0x7f070162" />
    <public type="dimen" name="m3_sys_motion_easing_standard_control_y2" id="0x7f070163" />
    <public type="dimen" name="m3_sys_motion_easing_standard_decelerate_control_x1" id="0x7f070164" />
    <public type="dimen" name="m3_sys_motion_easing_standard_decelerate_control_x2" id="0x7f070165" />
    <public type="dimen" name="m3_sys_motion_easing_standard_decelerate_control_y1" id="0x7f070166" />
    <public type="dimen" name="m3_sys_motion_easing_standard_decelerate_control_y2" id="0x7f070167" />
    <public type="dimen" name="m3_sys_state_dragged_state_layer_opacity" id="0x7f070168" />
    <public type="dimen" name="m3_sys_state_focus_state_layer_opacity" id="0x7f070169" />
    <public type="dimen" name="m3_sys_state_hover_state_layer_opacity" id="0x7f07016a" />
    <public type="dimen" name="m3_sys_state_pressed_state_layer_opacity" id="0x7f07016b" />
    <public type="dimen" name="m3_timepicker_display_stroke_width" id="0x7f07016c" />
    <public type="dimen" name="m3_timepicker_window_elevation" id="0x7f07016d" />
    <public type="dimen" name="material_bottom_sheet_max_width" id="0x7f07016e" />
    <public type="dimen" name="material_clock_display_padding" id="0x7f07016f" />
    <public type="dimen" name="material_clock_face_margin_top" id="0x7f070170" />
    <public type="dimen" name="material_clock_hand_center_dot_radius" id="0x7f070171" />
    <public type="dimen" name="material_clock_hand_padding" id="0x7f070172" />
    <public type="dimen" name="material_clock_hand_stroke_width" id="0x7f070173" />
    <public type="dimen" name="material_clock_number_text_padding" id="0x7f070174" />
    <public type="dimen" name="material_clock_number_text_size" id="0x7f070175" />
    <public type="dimen" name="material_clock_period_toggle_height" id="0x7f070176" />
    <public type="dimen" name="material_clock_period_toggle_margin_left" id="0x7f070177" />
    <public type="dimen" name="material_clock_period_toggle_width" id="0x7f070178" />
    <public type="dimen" name="material_clock_size" id="0x7f070179" />
    <public type="dimen" name="material_cursor_inset_bottom" id="0x7f07017a" />
    <public type="dimen" name="material_cursor_inset_top" id="0x7f07017b" />
    <public type="dimen" name="material_cursor_width" id="0x7f07017c" />
    <public type="dimen" name="material_divider_thickness" id="0x7f07017d" />
    <public type="dimen" name="material_emphasis_disabled" id="0x7f07017e" />
    <public type="dimen" name="material_emphasis_disabled_background" id="0x7f07017f" />
    <public type="dimen" name="material_emphasis_high_type" id="0x7f070180" />
    <public type="dimen" name="material_emphasis_medium" id="0x7f070181" />
    <public type="dimen" name="material_filled_edittext_font_1_3_padding_bottom" id="0x7f070182" />
    <public type="dimen" name="material_filled_edittext_font_1_3_padding_top" id="0x7f070183" />
    <public type="dimen" name="material_filled_edittext_font_2_0_padding_bottom" id="0x7f070184" />
    <public type="dimen" name="material_filled_edittext_font_2_0_padding_top" id="0x7f070185" />
    <public type="dimen" name="material_font_1_3_box_collapsed_padding_top" id="0x7f070186" />
    <public type="dimen" name="material_font_2_0_box_collapsed_padding_top" id="0x7f070187" />
    <public type="dimen" name="material_helper_text_default_padding_top" id="0x7f070188" />
    <public type="dimen" name="material_helper_text_font_1_3_padding_horizontal" id="0x7f070189" />
    <public type="dimen" name="material_helper_text_font_1_3_padding_top" id="0x7f07018a" />
    <public type="dimen" name="material_input_text_to_prefix_suffix_padding" id="0x7f07018b" />
    <public type="dimen" name="material_textinput_default_width" id="0x7f07018c" />
    <public type="dimen" name="material_textinput_max_width" id="0x7f07018d" />
    <public type="dimen" name="material_textinput_min_width" id="0x7f07018e" />
    <public type="dimen" name="material_time_input_padding_bottom" id="0x7f07018f" />
    <public type="dimen" name="material_time_picker_minimum_screen_height" id="0x7f070190" />
    <public type="dimen" name="material_time_picker_minimum_screen_width" id="0x7f070191" />
    <public type="dimen" name="material_timepicker_dialog_buttons_margin_top" id="0x7f070192" />
    <public type="dimen" name="md_action_corner_radius" id="0x7f070193" />
    <public type="dimen" name="md_bg_corner_radius" id="0x7f070194" />
    <public type="dimen" name="md_button_frame_vertical_padding" id="0x7f070195" />
    <public type="dimen" name="md_button_height" id="0x7f070196" />
    <public type="dimen" name="md_button_inset_horizontal" id="0x7f070197" />
    <public type="dimen" name="md_button_inset_vertical" id="0x7f070198" />
    <public type="dimen" name="md_button_min_width" id="0x7f070199" />
    <public type="dimen" name="md_button_padding_frame_side" id="0x7f07019a" />
    <public type="dimen" name="md_button_padding_horizontal" id="0x7f07019b" />
    <public type="dimen" name="md_button_padding_horizontal_internalexternal" id="0x7f07019c" />
    <public type="dimen" name="md_button_padding_vertical" id="0x7f07019d" />
    <public type="dimen" name="md_button_textpadding_horizontal" id="0x7f07019e" />
    <public type="dimen" name="md_button_textsize" id="0x7f07019f" />
    <public type="dimen" name="md_content_padding_bottom" id="0x7f0701a0" />
    <public type="dimen" name="md_content_padding_top" id="0x7f0701a1" />
    <public type="dimen" name="md_content_textsize" id="0x7f0701a2" />
    <public type="dimen" name="md_dialog_frame_margin" id="0x7f0701a3" />
    <public type="dimen" name="md_dialog_horizontal_margin" id="0x7f0701a4" />
    <public type="dimen" name="md_dialog_max_width" id="0x7f0701a5" />
    <public type="dimen" name="md_dialog_vertical_margin" id="0x7f0701a6" />
    <public type="dimen" name="md_divider_height" id="0x7f0701a7" />
    <public type="dimen" name="md_icon_margin" id="0x7f0701a8" />
    <public type="dimen" name="md_icon_max_size" id="0x7f0701a9" />
    <public type="dimen" name="md_listitem_control_margin" id="0x7f0701aa" />
    <public type="dimen" name="md_listitem_height" id="0x7f0701ab" />
    <public type="dimen" name="md_listitem_margin_left" id="0x7f0701ac" />
    <public type="dimen" name="md_listitem_textsize" id="0x7f0701ad" />
    <public type="dimen" name="md_listitem_vertical_margin" id="0x7f0701ae" />
    <public type="dimen" name="md_listitem_vertical_margin_choice" id="0x7f0701af" />
    <public type="dimen" name="md_neutral_button_margin" id="0x7f0701b0" />
    <public type="dimen" name="md_notitle_vertical_padding" id="0x7f0701b1" />
    <public type="dimen" name="md_notitle_vertical_padding_more" id="0x7f0701b2" />
    <public type="dimen" name="md_simplelistitem_padding_top" id="0x7f0701b3" />
    <public type="dimen" name="md_title_frame_margin_bottom" id="0x7f0701b4" />
    <public type="dimen" name="md_title_frame_margin_bottom_less" id="0x7f0701b5" />
    <public type="dimen" name="md_title_textsize" id="0x7f0701b6" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_bottom" id="0x7f0701b7" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_end" id="0x7f0701b8" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_start" id="0x7f0701b9" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_top" id="0x7f0701ba" />
    <public type="dimen" name="mtrl_alert_dialog_picker_background_inset" id="0x7f0701bb" />
    <public type="dimen" name="mtrl_badge_horizontal_edge_offset" id="0x7f0701bc" />
    <public type="dimen" name="mtrl_badge_long_text_horizontal_padding" id="0x7f0701bd" />
    <public type="dimen" name="mtrl_badge_radius" id="0x7f0701be" />
    <public type="dimen" name="mtrl_badge_text_horizontal_edge_offset" id="0x7f0701bf" />
    <public type="dimen" name="mtrl_badge_text_size" id="0x7f0701c0" />
    <public type="dimen" name="mtrl_badge_toolbar_action_menu_item_horizontal_offset" id="0x7f0701c1" />
    <public type="dimen" name="mtrl_badge_toolbar_action_menu_item_vertical_offset" id="0x7f0701c2" />
    <public type="dimen" name="mtrl_badge_with_text_radius" id="0x7f0701c3" />
    <public type="dimen" name="mtrl_bottomappbar_fabOffsetEndMode" id="0x7f0701c4" />
    <public type="dimen" name="mtrl_bottomappbar_fab_bottom_margin" id="0x7f0701c5" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_margin" id="0x7f0701c6" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius" id="0x7f0701c7" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_vertical_offset" id="0x7f0701c8" />
    <public type="dimen" name="mtrl_bottomappbar_height" id="0x7f0701c9" />
    <public type="dimen" name="mtrl_btn_corner_radius" id="0x7f0701ca" />
    <public type="dimen" name="mtrl_btn_dialog_btn_min_width" id="0x7f0701cb" />
    <public type="dimen" name="mtrl_btn_disabled_elevation" id="0x7f0701cc" />
    <public type="dimen" name="mtrl_btn_disabled_z" id="0x7f0701cd" />
    <public type="dimen" name="mtrl_btn_elevation" id="0x7f0701ce" />
    <public type="dimen" name="mtrl_btn_focused_z" id="0x7f0701cf" />
    <public type="dimen" name="mtrl_btn_hovered_z" id="0x7f0701d0" />
    <public type="dimen" name="mtrl_btn_icon_btn_padding_left" id="0x7f0701d1" />
    <public type="dimen" name="mtrl_btn_icon_padding" id="0x7f0701d2" />
    <public type="dimen" name="mtrl_btn_inset" id="0x7f0701d3" />
    <public type="dimen" name="mtrl_btn_letter_spacing" id="0x7f0701d4" />
    <public type="dimen" name="mtrl_btn_max_width" id="0x7f0701d5" />
    <public type="dimen" name="mtrl_btn_padding_bottom" id="0x7f0701d6" />
    <public type="dimen" name="mtrl_btn_padding_left" id="0x7f0701d7" />
    <public type="dimen" name="mtrl_btn_padding_right" id="0x7f0701d8" />
    <public type="dimen" name="mtrl_btn_padding_top" id="0x7f0701d9" />
    <public type="dimen" name="mtrl_btn_pressed_z" id="0x7f0701da" />
    <public type="dimen" name="mtrl_btn_snackbar_margin_horizontal" id="0x7f0701db" />
    <public type="dimen" name="mtrl_btn_stroke_size" id="0x7f0701dc" />
    <public type="dimen" name="mtrl_btn_text_btn_icon_padding" id="0x7f0701dd" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_left" id="0x7f0701de" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_right" id="0x7f0701df" />
    <public type="dimen" name="mtrl_btn_text_size" id="0x7f0701e0" />
    <public type="dimen" name="mtrl_btn_z" id="0x7f0701e1" />
    <public type="dimen" name="mtrl_calendar_action_confirm_button_min_width" id="0x7f0701e2" />
    <public type="dimen" name="mtrl_calendar_action_height" id="0x7f0701e3" />
    <public type="dimen" name="mtrl_calendar_action_padding" id="0x7f0701e4" />
    <public type="dimen" name="mtrl_calendar_bottom_padding" id="0x7f0701e5" />
    <public type="dimen" name="mtrl_calendar_content_padding" id="0x7f0701e6" />
    <public type="dimen" name="mtrl_calendar_day_corner" id="0x7f0701e7" />
    <public type="dimen" name="mtrl_calendar_day_height" id="0x7f0701e8" />
    <public type="dimen" name="mtrl_calendar_day_horizontal_padding" id="0x7f0701e9" />
    <public type="dimen" name="mtrl_calendar_day_today_stroke" id="0x7f0701ea" />
    <public type="dimen" name="mtrl_calendar_day_vertical_padding" id="0x7f0701eb" />
    <public type="dimen" name="mtrl_calendar_day_width" id="0x7f0701ec" />
    <public type="dimen" name="mtrl_calendar_days_of_week_height" id="0x7f0701ed" />
    <public type="dimen" name="mtrl_calendar_dialog_background_inset" id="0x7f0701ee" />
    <public type="dimen" name="mtrl_calendar_header_content_padding" id="0x7f0701ef" />
    <public type="dimen" name="mtrl_calendar_header_content_padding_fullscreen" id="0x7f0701f0" />
    <public type="dimen" name="mtrl_calendar_header_divider_thickness" id="0x7f0701f1" />
    <public type="dimen" name="mtrl_calendar_header_height" id="0x7f0701f2" />
    <public type="dimen" name="mtrl_calendar_header_height_fullscreen" id="0x7f0701f3" />
    <public type="dimen" name="mtrl_calendar_header_selection_line_height" id="0x7f0701f4" />
    <public type="dimen" name="mtrl_calendar_header_text_padding" id="0x7f0701f5" />
    <public type="dimen" name="mtrl_calendar_header_toggle_margin_bottom" id="0x7f0701f6" />
    <public type="dimen" name="mtrl_calendar_header_toggle_margin_top" id="0x7f0701f7" />
    <public type="dimen" name="mtrl_calendar_landscape_header_width" id="0x7f0701f8" />
    <public type="dimen" name="mtrl_calendar_maximum_default_fullscreen_minor_axis" id="0x7f0701f9" />
    <public type="dimen" name="mtrl_calendar_month_horizontal_padding" id="0x7f0701fa" />
    <public type="dimen" name="mtrl_calendar_month_vertical_padding" id="0x7f0701fb" />
    <public type="dimen" name="mtrl_calendar_navigation_bottom_padding" id="0x7f0701fc" />
    <public type="dimen" name="mtrl_calendar_navigation_height" id="0x7f0701fd" />
    <public type="dimen" name="mtrl_calendar_navigation_top_padding" id="0x7f0701fe" />
    <public type="dimen" name="mtrl_calendar_pre_l_text_clip_padding" id="0x7f0701ff" />
    <public type="dimen" name="mtrl_calendar_selection_baseline_to_top_fullscreen" id="0x7f070200" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_bottom" id="0x7f070201" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_bottom_fullscreen" id="0x7f070202" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_top" id="0x7f070203" />
    <public type="dimen" name="mtrl_calendar_text_input_padding_top" id="0x7f070204" />
    <public type="dimen" name="mtrl_calendar_title_baseline_to_top" id="0x7f070205" />
    <public type="dimen" name="mtrl_calendar_title_baseline_to_top_fullscreen" id="0x7f070206" />
    <public type="dimen" name="mtrl_calendar_year_corner" id="0x7f070207" />
    <public type="dimen" name="mtrl_calendar_year_height" id="0x7f070208" />
    <public type="dimen" name="mtrl_calendar_year_horizontal_padding" id="0x7f070209" />
    <public type="dimen" name="mtrl_calendar_year_vertical_padding" id="0x7f07020a" />
    <public type="dimen" name="mtrl_calendar_year_width" id="0x7f07020b" />
    <public type="dimen" name="mtrl_card_checked_icon_margin" id="0x7f07020c" />
    <public type="dimen" name="mtrl_card_checked_icon_size" id="0x7f07020d" />
    <public type="dimen" name="mtrl_card_corner_radius" id="0x7f07020e" />
    <public type="dimen" name="mtrl_card_dragged_z" id="0x7f07020f" />
    <public type="dimen" name="mtrl_card_elevation" id="0x7f070210" />
    <public type="dimen" name="mtrl_card_spacing" id="0x7f070211" />
    <public type="dimen" name="mtrl_chip_pressed_translation_z" id="0x7f070212" />
    <public type="dimen" name="mtrl_chip_text_size" id="0x7f070213" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_elevation" id="0x7f070214" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_vertical_offset" id="0x7f070215" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_vertical_padding" id="0x7f070216" />
    <public type="dimen" name="mtrl_extended_fab_bottom_padding" id="0x7f070217" />
    <public type="dimen" name="mtrl_extended_fab_disabled_elevation" id="0x7f070218" />
    <public type="dimen" name="mtrl_extended_fab_disabled_translation_z" id="0x7f070219" />
    <public type="dimen" name="mtrl_extended_fab_elevation" id="0x7f07021a" />
    <public type="dimen" name="mtrl_extended_fab_end_padding" id="0x7f07021b" />
    <public type="dimen" name="mtrl_extended_fab_end_padding_icon" id="0x7f07021c" />
    <public type="dimen" name="mtrl_extended_fab_icon_size" id="0x7f07021d" />
    <public type="dimen" name="mtrl_extended_fab_icon_text_spacing" id="0x7f07021e" />
    <public type="dimen" name="mtrl_extended_fab_min_height" id="0x7f07021f" />
    <public type="dimen" name="mtrl_extended_fab_min_width" id="0x7f070220" />
    <public type="dimen" name="mtrl_extended_fab_start_padding" id="0x7f070221" />
    <public type="dimen" name="mtrl_extended_fab_start_padding_icon" id="0x7f070222" />
    <public type="dimen" name="mtrl_extended_fab_top_padding" id="0x7f070223" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_base" id="0x7f070224" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_hovered_focused" id="0x7f070225" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_pressed" id="0x7f070226" />
    <public type="dimen" name="mtrl_fab_elevation" id="0x7f070227" />
    <public type="dimen" name="mtrl_fab_min_touch_target" id="0x7f070228" />
    <public type="dimen" name="mtrl_fab_translation_z_hovered_focused" id="0x7f070229" />
    <public type="dimen" name="mtrl_fab_translation_z_pressed" id="0x7f07022a" />
    <public type="dimen" name="mtrl_high_ripple_default_alpha" id="0x7f07022b" />
    <public type="dimen" name="mtrl_high_ripple_focused_alpha" id="0x7f07022c" />
    <public type="dimen" name="mtrl_high_ripple_hovered_alpha" id="0x7f07022d" />
    <public type="dimen" name="mtrl_high_ripple_pressed_alpha" id="0x7f07022e" />
    <public type="dimen" name="mtrl_low_ripple_default_alpha" id="0x7f07022f" />
    <public type="dimen" name="mtrl_low_ripple_focused_alpha" id="0x7f070230" />
    <public type="dimen" name="mtrl_low_ripple_hovered_alpha" id="0x7f070231" />
    <public type="dimen" name="mtrl_low_ripple_pressed_alpha" id="0x7f070232" />
    <public type="dimen" name="mtrl_min_touch_target_size" id="0x7f070233" />
    <public type="dimen" name="mtrl_navigation_bar_item_default_icon_size" id="0x7f070234" />
    <public type="dimen" name="mtrl_navigation_bar_item_default_margin" id="0x7f070235" />
    <public type="dimen" name="mtrl_navigation_elevation" id="0x7f070236" />
    <public type="dimen" name="mtrl_navigation_item_horizontal_padding" id="0x7f070237" />
    <public type="dimen" name="mtrl_navigation_item_icon_padding" id="0x7f070238" />
    <public type="dimen" name="mtrl_navigation_item_icon_size" id="0x7f070239" />
    <public type="dimen" name="mtrl_navigation_item_shape_horizontal_margin" id="0x7f07023a" />
    <public type="dimen" name="mtrl_navigation_item_shape_vertical_margin" id="0x7f07023b" />
    <public type="dimen" name="mtrl_navigation_rail_active_text_size" id="0x7f07023c" />
    <public type="dimen" name="mtrl_navigation_rail_compact_width" id="0x7f07023d" />
    <public type="dimen" name="mtrl_navigation_rail_default_width" id="0x7f07023e" />
    <public type="dimen" name="mtrl_navigation_rail_elevation" id="0x7f07023f" />
    <public type="dimen" name="mtrl_navigation_rail_icon_margin" id="0x7f070240" />
    <public type="dimen" name="mtrl_navigation_rail_icon_size" id="0x7f070241" />
    <public type="dimen" name="mtrl_navigation_rail_margin" id="0x7f070242" />
    <public type="dimen" name="mtrl_navigation_rail_text_bottom_margin" id="0x7f070243" />
    <public type="dimen" name="mtrl_navigation_rail_text_size" id="0x7f070244" />
    <public type="dimen" name="mtrl_progress_circular_inset" id="0x7f070245" />
    <public type="dimen" name="mtrl_progress_circular_inset_extra_small" id="0x7f070246" />
    <public type="dimen" name="mtrl_progress_circular_inset_medium" id="0x7f070247" />
    <public type="dimen" name="mtrl_progress_circular_inset_small" id="0x7f070248" />
    <public type="dimen" name="mtrl_progress_circular_radius" id="0x7f070249" />
    <public type="dimen" name="mtrl_progress_circular_size" id="0x7f07024a" />
    <public type="dimen" name="mtrl_progress_circular_size_extra_small" id="0x7f07024b" />
    <public type="dimen" name="mtrl_progress_circular_size_medium" id="0x7f07024c" />
    <public type="dimen" name="mtrl_progress_circular_size_small" id="0x7f07024d" />
    <public type="dimen" name="mtrl_progress_circular_track_thickness_extra_small" id="0x7f07024e" />
    <public type="dimen" name="mtrl_progress_circular_track_thickness_medium" id="0x7f07024f" />
    <public type="dimen" name="mtrl_progress_circular_track_thickness_small" id="0x7f070250" />
    <public type="dimen" name="mtrl_progress_indicator_full_rounded_corner_radius" id="0x7f070251" />
    <public type="dimen" name="mtrl_progress_track_thickness" id="0x7f070252" />
    <public type="dimen" name="mtrl_shape_corner_size_large_component" id="0x7f070253" />
    <public type="dimen" name="mtrl_shape_corner_size_medium_component" id="0x7f070254" />
    <public type="dimen" name="mtrl_shape_corner_size_small_component" id="0x7f070255" />
    <public type="dimen" name="mtrl_slider_halo_radius" id="0x7f070256" />
    <public type="dimen" name="mtrl_slider_label_padding" id="0x7f070257" />
    <public type="dimen" name="mtrl_slider_label_radius" id="0x7f070258" />
    <public type="dimen" name="mtrl_slider_label_square_side" id="0x7f070259" />
    <public type="dimen" name="mtrl_slider_thumb_elevation" id="0x7f07025a" />
    <public type="dimen" name="mtrl_slider_thumb_radius" id="0x7f07025b" />
    <public type="dimen" name="mtrl_slider_track_height" id="0x7f07025c" />
    <public type="dimen" name="mtrl_slider_track_side_padding" id="0x7f07025d" />
    <public type="dimen" name="mtrl_slider_widget_height" id="0x7f07025e" />
    <public type="dimen" name="mtrl_snackbar_action_text_color_alpha" id="0x7f07025f" />
    <public type="dimen" name="mtrl_snackbar_background_corner_radius" id="0x7f070260" />
    <public type="dimen" name="mtrl_snackbar_background_overlay_color_alpha" id="0x7f070261" />
    <public type="dimen" name="mtrl_snackbar_margin" id="0x7f070262" />
    <public type="dimen" name="mtrl_snackbar_message_margin_horizontal" id="0x7f070263" />
    <public type="dimen" name="mtrl_snackbar_padding_horizontal" id="0x7f070264" />
    <public type="dimen" name="mtrl_switch_text_padding" id="0x7f070265" />
    <public type="dimen" name="mtrl_switch_thumb_elevation" id="0x7f070266" />
    <public type="dimen" name="mtrl_switch_thumb_size" id="0x7f070267" />
    <public type="dimen" name="mtrl_switch_track_height" id="0x7f070268" />
    <public type="dimen" name="mtrl_switch_track_width" id="0x7f070269" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_medium" id="0x7f07026a" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_small" id="0x7f07026b" />
    <public type="dimen" name="mtrl_textinput_box_label_cutout_padding" id="0x7f07026c" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_default" id="0x7f07026d" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_focused" id="0x7f07026e" />
    <public type="dimen" name="mtrl_textinput_counter_margin_start" id="0x7f07026f" />
    <public type="dimen" name="mtrl_textinput_end_icon_margin_start" id="0x7f070270" />
    <public type="dimen" name="mtrl_textinput_outline_box_expanded_padding" id="0x7f070271" />
    <public type="dimen" name="mtrl_textinput_start_icon_margin_end" id="0x7f070272" />
    <public type="dimen" name="mtrl_toolbar_default_height" id="0x7f070273" />
    <public type="dimen" name="mtrl_tooltip_arrowSize" id="0x7f070274" />
    <public type="dimen" name="mtrl_tooltip_cornerSize" id="0x7f070275" />
    <public type="dimen" name="mtrl_tooltip_minHeight" id="0x7f070276" />
    <public type="dimen" name="mtrl_tooltip_minWidth" id="0x7f070277" />
    <public type="dimen" name="mtrl_tooltip_padding" id="0x7f070278" />
    <public type="dimen" name="mtrl_transition_shared_axis_slide_distance" id="0x7f070279" />
    <public type="dimen" name="notification_action_icon_size" id="0x7f07027a" />
    <public type="dimen" name="notification_action_text_size" id="0x7f07027b" />
    <public type="dimen" name="notification_big_circle_margin" id="0x7f07027c" />
    <public type="dimen" name="notification_content_margin_start" id="0x7f07027d" />
    <public type="dimen" name="notification_large_icon_height" id="0x7f07027e" />
    <public type="dimen" name="notification_large_icon_width" id="0x7f07027f" />
    <public type="dimen" name="notification_main_column_padding_top" id="0x7f070280" />
    <public type="dimen" name="notification_media_narrow_margin" id="0x7f070281" />
    <public type="dimen" name="notification_right_icon_size" id="0x7f070282" />
    <public type="dimen" name="notification_right_side_padding_top" id="0x7f070283" />
    <public type="dimen" name="notification_small_icon_background_padding" id="0x7f070284" />
    <public type="dimen" name="notification_small_icon_size_as_large" id="0x7f070285" />
    <public type="dimen" name="notification_subtext_size" id="0x7f070286" />
    <public type="dimen" name="notification_top_pad" id="0x7f070287" />
    <public type="dimen" name="notification_top_pad_large_text" id="0x7f070288" />
    <public type="dimen" name="tooltip_corner_radius" id="0x7f070289" />
    <public type="dimen" name="tooltip_horizontal_padding" id="0x7f07028a" />
    <public type="dimen" name="tooltip_margin" id="0x7f07028b" />
    <public type="dimen" name="tooltip_precise_anchor_extra_offset" id="0x7f07028c" />
    <public type="dimen" name="tooltip_precise_anchor_threshold" id="0x7f07028d" />
    <public type="dimen" name="tooltip_vertical_padding" id="0x7f07028e" />
    <public type="dimen" name="tooltip_y_offset_non_touch" id="0x7f07028f" />
    <public type="dimen" name="tooltip_y_offset_touch" id="0x7f070290" />
    <public type="drawable" name="_avd_hide_password__0_res_0x7f080000" id="0x7f080000" />
    <public type="drawable" name="_avd_hide_password__1_res_0x7f080001" id="0x7f080001" />
    <public type="drawable" name="_avd_hide_password__2_res_0x7f080002" id="0x7f080002" />
    <public type="drawable" name="_avd_show_password__0_res_0x7f080003" id="0x7f080003" />
    <public type="drawable" name="_avd_show_password__1_res_0x7f080004" id="0x7f080004" />
    <public type="drawable" name="_avd_show_password__2_res_0x7f080005" id="0x7f080005" />
    <public type="drawable" name="_mtrl_checkbox_button_checked_unchecked__0_res_0x7f080006" id="0x7f080006" />
    <public type="drawable" name="_mtrl_checkbox_button_checked_unchecked__1_res_0x7f080007" id="0x7f080007" />
    <public type="drawable" name="_mtrl_checkbox_button_checked_unchecked__2_res_0x7f080008" id="0x7f080008" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_checked_indeterminate__0_res_0x7f080009" id="0x7f080009" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_checked_unchecked__0_res_0x7f08000a" id="0x7f08000a" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_checked_unchecked__1_res_0x7f08000b" id="0x7f08000b" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_checked_unchecked__2_res_0x7f08000c" id="0x7f08000c" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_indeterminate_checked__0_res_0x7f08000d" id="0x7f08000d" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_indeterminate_unchecked__0_res_0x7f08000e" id="0x7f08000e" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_indeterminate_unchecked__1_res_0x7f08000f" id="0x7f08000f" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_indeterminate_unchecked__2_res_0x7f080010" id="0x7f080010" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_unchecked_checked__0_res_0x7f080011" id="0x7f080011" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_unchecked_checked__1_res_0x7f080012" id="0x7f080012" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_unchecked_checked__2_res_0x7f080013" id="0x7f080013" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_unchecked_indeterminate__0_res_0x7f080014" id="0x7f080014" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_unchecked_indeterminate__1_res_0x7f080015" id="0x7f080015" />
    <public type="drawable" name="_mtrl_checkbox_button_icon_unchecked_indeterminate__2_res_0x7f080016" id="0x7f080016" />
    <public type="drawable" name="_mtrl_checkbox_button_unchecked_checked__0_res_0x7f080017" id="0x7f080017" />
    <public type="drawable" name="_mtrl_checkbox_button_unchecked_checked__1_res_0x7f080018" id="0x7f080018" />
    <public type="drawable" name="_mtrl_checkbox_button_unchecked_checked__2_res_0x7f080019" id="0x7f080019" />
    <public type="drawable" name="_mtrl_switch_thumb_checked_pressed__0_res_0x7f08001a" id="0x7f08001a" />
    <public type="drawable" name="_mtrl_switch_thumb_checked_unchecked__0_res_0x7f08001b" id="0x7f08001b" />
    <public type="drawable" name="_mtrl_switch_thumb_checked_unchecked__1_res_0x7f08001c" id="0x7f08001c" />
    <public type="drawable" name="_mtrl_switch_thumb_pressed_checked__0_res_0x7f08001d" id="0x7f08001d" />
    <public type="drawable" name="_mtrl_switch_thumb_pressed_unchecked__0_res_0x7f08001e" id="0x7f08001e" />
    <public type="drawable" name="_mtrl_switch_thumb_unchecked_checked__0_res_0x7f08001f" id="0x7f08001f" />
    <public type="drawable" name="_mtrl_switch_thumb_unchecked_checked__1_res_0x7f080020" id="0x7f080020" />
    <public type="drawable" name="_mtrl_switch_thumb_unchecked_pressed__0_res_0x7f080021" id="0x7f080021" />
    <public type="drawable" name="abc_ab_share_pack_mtrl_alpha" id="0x7f080022" />
    <public type="drawable" name="abc_action_bar_item_background_material" id="0x7f080023" />
    <public type="drawable" name="abc_btn_borderless_material" id="0x7f080024" />
    <public type="drawable" name="abc_btn_check_material" id="0x7f080025" />
    <public type="drawable" name="abc_btn_check_material_anim" id="0x7f080026" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_000" id="0x7f080027" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_015" id="0x7f080028" />
    <public type="drawable" name="abc_btn_colored_material" id="0x7f080029" />
    <public type="drawable" name="abc_btn_default_mtrl_shape" id="0x7f08002a" />
    <public type="drawable" name="abc_btn_radio_material" id="0x7f08002b" />
    <public type="drawable" name="abc_btn_radio_material_anim" id="0x7f08002c" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_000" id="0x7f08002d" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_015" id="0x7f08002e" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00001" id="0x7f08002f" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00012" id="0x7f080030" />
    <public type="drawable" name="abc_cab_background_internal_bg" id="0x7f080031" />
    <public type="drawable" name="abc_cab_background_top_material" id="0x7f080032" />
    <public type="drawable" name="abc_cab_background_top_mtrl_alpha" id="0x7f080033" />
    <public type="drawable" name="abc_control_background_material" id="0x7f080034" />
    <public type="drawable" name="abc_dialog_material_background" id="0x7f080035" />
    <public type="drawable" name="abc_edit_text_material" id="0x7f080036" />
    <public type="drawable" name="abc_ic_ab_back_material" id="0x7f080037" />
    <public type="drawable" name="abc_ic_arrow_drop_right_black_24dp" id="0x7f080038" />
    <public type="drawable" name="abc_ic_clear_material" id="0x7f080039" />
    <public type="drawable" name="abc_ic_commit_search_api_mtrl_alpha" id="0x7f08003a" />
    <public type="drawable" name="abc_ic_go_search_api_material" id="0x7f08003b" />
    <public type="drawable" name="abc_ic_menu_copy_mtrl_am_alpha" id="0x7f08003c" />
    <public type="drawable" name="abc_ic_menu_cut_mtrl_alpha" id="0x7f08003d" />
    <public type="drawable" name="abc_ic_menu_overflow_material" id="0x7f08003e" />
    <public type="drawable" name="abc_ic_menu_paste_mtrl_am_alpha" id="0x7f08003f" />
    <public type="drawable" name="abc_ic_menu_selectall_mtrl_alpha" id="0x7f080040" />
    <public type="drawable" name="abc_ic_menu_share_mtrl_alpha" id="0x7f080041" />
    <public type="drawable" name="abc_ic_search_api_material" id="0x7f080042" />
    <public type="drawable" name="abc_ic_voice_search_api_material" id="0x7f080043" />
    <public type="drawable" name="abc_item_background_holo_dark" id="0x7f080044" />
    <public type="drawable" name="abc_item_background_holo_light" id="0x7f080045" />
    <public type="drawable" name="abc_list_divider_material" id="0x7f080046" />
    <public type="drawable" name="abc_list_divider_mtrl_alpha" id="0x7f080047" />
    <public type="drawable" name="abc_list_focused_holo" id="0x7f080048" />
    <public type="drawable" name="abc_list_longpressed_holo" id="0x7f080049" />
    <public type="drawable" name="abc_list_pressed_holo_dark" id="0x7f08004a" />
    <public type="drawable" name="abc_list_pressed_holo_light" id="0x7f08004b" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_dark" id="0x7f08004c" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_light" id="0x7f08004d" />
    <public type="drawable" name="abc_list_selector_disabled_holo_dark" id="0x7f08004e" />
    <public type="drawable" name="abc_list_selector_disabled_holo_light" id="0x7f08004f" />
    <public type="drawable" name="abc_list_selector_holo_dark" id="0x7f080050" />
    <public type="drawable" name="abc_list_selector_holo_light" id="0x7f080051" />
    <public type="drawable" name="abc_menu_hardkey_panel_mtrl_mult" id="0x7f080052" />
    <public type="drawable" name="abc_popup_background_mtrl_mult" id="0x7f080053" />
    <public type="drawable" name="abc_ratingbar_indicator_material" id="0x7f080054" />
    <public type="drawable" name="abc_ratingbar_material" id="0x7f080055" />
    <public type="drawable" name="abc_ratingbar_small_material" id="0x7f080056" />
    <public type="drawable" name="abc_scrubber_control_off_mtrl_alpha" id="0x7f080057" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_000" id="0x7f080058" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_005" id="0x7f080059" />
    <public type="drawable" name="abc_scrubber_primary_mtrl_alpha" id="0x7f08005a" />
    <public type="drawable" name="abc_scrubber_track_mtrl_alpha" id="0x7f08005b" />
    <public type="drawable" name="abc_seekbar_thumb_material" id="0x7f08005c" />
    <public type="drawable" name="abc_seekbar_tick_mark_material" id="0x7f08005d" />
    <public type="drawable" name="abc_seekbar_track_material" id="0x7f08005e" />
    <public type="drawable" name="abc_spinner_mtrl_am_alpha" id="0x7f08005f" />
    <public type="drawable" name="abc_spinner_textfield_background_material" id="0x7f080060" />
    <public type="drawable" name="abc_star_black_48dp" id="0x7f080061" />
    <public type="drawable" name="abc_star_half_black_48dp" id="0x7f080062" />
    <public type="drawable" name="abc_switch_thumb_material" id="0x7f080063" />
    <public type="drawable" name="abc_switch_track_mtrl_alpha" id="0x7f080064" />
    <public type="drawable" name="abc_tab_indicator_material" id="0x7f080065" />
    <public type="drawable" name="abc_tab_indicator_mtrl_alpha" id="0x7f080066" />
    <public type="drawable" name="abc_text_cursor_material" id="0x7f080067" />
    <public type="drawable" name="abc_text_select_handle_left_mtrl" id="0x7f080068" />
    <public type="drawable" name="abc_text_select_handle_middle_mtrl" id="0x7f080069" />
    <public type="drawable" name="abc_text_select_handle_right_mtrl" id="0x7f08006a" />
    <public type="drawable" name="abc_textfield_activated_mtrl_alpha" id="0x7f08006b" />
    <public type="drawable" name="abc_textfield_default_mtrl_alpha" id="0x7f08006c" />
    <public type="drawable" name="abc_textfield_search_activated_mtrl_alpha" id="0x7f08006d" />
    <public type="drawable" name="abc_textfield_search_default_mtrl_alpha" id="0x7f08006e" />
    <public type="drawable" name="abc_textfield_search_material" id="0x7f08006f" />
    <public type="drawable" name="abc_vector_test" id="0x7f080070" />
    <public type="drawable" name="avd_hide_password" id="0x7f080071" />
    <public type="drawable" name="avd_show_password" id="0x7f080072" />
    <public type="drawable" name="baseline_filter_list_white_24dp" id="0x7f080073" />
    <public type="drawable" name="bg_floating_controller" id="0x7f080074" />
    <public type="drawable" name="btn_checkbox_checked_mtrl" id="0x7f080075" />
    <public type="drawable" name="btn_checkbox_checked_to_unchecked_mtrl_animation" id="0x7f080076" />
    <public type="drawable" name="btn_checkbox_unchecked_mtrl" id="0x7f080077" />
    <public type="drawable" name="btn_checkbox_unchecked_to_checked_mtrl_animation" id="0x7f080078" />
    <public type="drawable" name="btn_radio_off_mtrl" id="0x7f080079" />
    <public type="drawable" name="btn_radio_off_to_on_mtrl_animation" id="0x7f08007a" />
    <public type="drawable" name="btn_radio_on_mtrl" id="0x7f08007b" />
    <public type="drawable" name="btn_radio_on_to_off_mtrl_animation" id="0x7f08007c" />
    <public type="drawable" name="circle_cool_black" id="0x7f08007d" />
    <public type="drawable" name="design_fab_background" id="0x7f08007e" />
    <public type="drawable" name="design_ic_visibility" id="0x7f08007f" />
    <public type="drawable" name="design_ic_visibility_off" id="0x7f080080" />
    <public type="drawable" name="design_password_eye" id="0x7f080081" />
    <public type="drawable" name="design_snackbar_background" id="0x7f080082" />
    <public type="drawable" name="floating_console_bg_bottom" id="0x7f080083" />
    <public type="drawable" name="floating_console_bg_top" id="0x7f080084" />
    <public type="drawable" name="ic_3d_rotation_black_48dp" id="0x7f080085" />
    <public type="drawable" name="ic_ac_unit_black_48dp" id="0x7f080086" />
    <public type="drawable" name="ic_access_alarm_black_48dp" id="0x7f080087" />
    <public type="drawable" name="ic_access_alarms_black_48dp" id="0x7f080088" />
    <public type="drawable" name="ic_access_time_black_48dp" id="0x7f080089" />
    <public type="drawable" name="ic_accessibility_black_48dp" id="0x7f08008a" />
    <public type="drawable" name="ic_accessible_black_48dp" id="0x7f08008b" />
    <public type="drawable" name="ic_account_balance_black_48dp" id="0x7f08008c" />
    <public type="drawable" name="ic_account_balance_wallet_black_48dp" id="0x7f08008d" />
    <public type="drawable" name="ic_account_box_black_48dp" id="0x7f08008e" />
    <public type="drawable" name="ic_account_circle_black_48dp" id="0x7f08008f" />
    <public type="drawable" name="ic_adb_black_48dp" id="0x7f080090" />
    <public type="drawable" name="ic_add_a_photo_black_48dp" id="0x7f080091" />
    <public type="drawable" name="ic_add_alarm_black_48dp" id="0x7f080092" />
    <public type="drawable" name="ic_add_alert_black_48dp" id="0x7f080093" />
    <public type="drawable" name="ic_add_black_48dp" id="0x7f080094" />
    <public type="drawable" name="ic_add_box_black_48dp" id="0x7f080095" />
    <public type="drawable" name="ic_add_circle_black_48dp" id="0x7f080096" />
    <public type="drawable" name="ic_add_circle_outline_black_48dp" id="0x7f080097" />
    <public type="drawable" name="ic_add_location_black_48dp" id="0x7f080098" />
    <public type="drawable" name="ic_add_shopping_cart_black_48dp" id="0x7f080099" />
    <public type="drawable" name="ic_add_to_photos_black_48dp" id="0x7f08009a" />
    <public type="drawable" name="ic_add_to_queue_black_48dp" id="0x7f08009b" />
    <public type="drawable" name="ic_add_white_48dp" id="0x7f08009c" />
    <public type="drawable" name="ic_adjust_black_48dp" id="0x7f08009d" />
    <public type="drawable" name="ic_airline_seat_flat_angled_black_48dp" id="0x7f08009e" />
    <public type="drawable" name="ic_airline_seat_flat_black_48dp" id="0x7f08009f" />
    <public type="drawable" name="ic_airline_seat_individual_suite_black_48dp" id="0x7f0800a0" />
    <public type="drawable" name="ic_airline_seat_legroom_extra_black_48dp" id="0x7f0800a1" />
    <public type="drawable" name="ic_airline_seat_legroom_normal_black_48dp" id="0x7f0800a2" />
    <public type="drawable" name="ic_airline_seat_legroom_reduced_black_48dp" id="0x7f0800a3" />
    <public type="drawable" name="ic_airline_seat_recline_extra_black_48dp" id="0x7f0800a4" />
    <public type="drawable" name="ic_airline_seat_recline_normal_black_48dp" id="0x7f0800a5" />
    <public type="drawable" name="ic_airplanemode_active_black_48dp" id="0x7f0800a6" />
    <public type="drawable" name="ic_airplanemode_inactive_black_48dp" id="0x7f0800a7" />
    <public type="drawable" name="ic_airplay_black_48dp" id="0x7f0800a8" />
    <public type="drawable" name="ic_airport_shuttle_black_48dp" id="0x7f0800a9" />
    <public type="drawable" name="ic_alarm_add_black_48dp" id="0x7f0800aa" />
    <public type="drawable" name="ic_alarm_black_48dp" id="0x7f0800ab" />
    <public type="drawable" name="ic_alarm_off_black_48dp" id="0x7f0800ac" />
    <public type="drawable" name="ic_alarm_on_black_48dp" id="0x7f0800ad" />
    <public type="drawable" name="ic_album_black_48dp" id="0x7f0800ae" />
    <public type="drawable" name="ic_all_inclusive_black_48dp" id="0x7f0800af" />
    <public type="drawable" name="ic_all_out_black_48dp" id="0x7f0800b0" />
    <public type="drawable" name="ic_android_black_48dp" id="0x7f0800b1" />
    <public type="drawable" name="ic_announcement_black_48dp" id="0x7f0800b2" />
    <public type="drawable" name="ic_apps_black_48dp" id="0x7f0800b3" />
    <public type="drawable" name="ic_archive_black_48dp" id="0x7f0800b4" />
    <public type="drawable" name="ic_arrow_back_black_48dp" id="0x7f0800b5" />
    <public type="drawable" name="ic_arrow_downward_black_48dp" id="0x7f0800b6" />
    <public type="drawable" name="ic_arrow_drop_down_black_48dp" id="0x7f0800b7" />
    <public type="drawable" name="ic_arrow_drop_down_circle_black_48dp" id="0x7f0800b8" />
    <public type="drawable" name="ic_arrow_drop_up_black_48dp" id="0x7f0800b9" />
    <public type="drawable" name="ic_arrow_forward_black_48dp" id="0x7f0800ba" />
    <public type="drawable" name="ic_arrow_upward_black_48dp" id="0x7f0800bb" />
    <public type="drawable" name="ic_art_track_black_48dp" id="0x7f0800bc" />
    <public type="drawable" name="ic_aspect_ratio_black_48dp" id="0x7f0800bd" />
    <public type="drawable" name="ic_assessment_black_48dp" id="0x7f0800be" />
    <public type="drawable" name="ic_assignment_black_48dp" id="0x7f0800bf" />
    <public type="drawable" name="ic_assignment_ind_black_48dp" id="0x7f0800c0" />
    <public type="drawable" name="ic_assignment_late_black_48dp" id="0x7f0800c1" />
    <public type="drawable" name="ic_assignment_return_black_48dp" id="0x7f0800c2" />
    <public type="drawable" name="ic_assignment_returned_black_48dp" id="0x7f0800c3" />
    <public type="drawable" name="ic_assignment_turned_in_black_48dp" id="0x7f0800c4" />
    <public type="drawable" name="ic_assistant_black_48dp" id="0x7f0800c5" />
    <public type="drawable" name="ic_assistant_photo_black_48dp" id="0x7f0800c6" />
    <public type="drawable" name="ic_attach_file_black_48dp" id="0x7f0800c7" />
    <public type="drawable" name="ic_attach_money_black_48dp" id="0x7f0800c8" />
    <public type="drawable" name="ic_attachment_black_48dp" id="0x7f0800c9" />
    <public type="drawable" name="ic_audiotrack_black_48dp" id="0x7f0800ca" />
    <public type="drawable" name="ic_autorenew_black_48dp" id="0x7f0800cb" />
    <public type="drawable" name="ic_av_timer_black_48dp" id="0x7f0800cc" />
    <public type="drawable" name="ic_backspace_black_48dp" id="0x7f0800cd" />
    <public type="drawable" name="ic_backup_black_48dp" id="0x7f0800ce" />
    <public type="drawable" name="ic_battery_20_black_48dp" id="0x7f0800cf" />
    <public type="drawable" name="ic_battery_30_black_48dp" id="0x7f0800d0" />
    <public type="drawable" name="ic_battery_50_black_48dp" id="0x7f0800d1" />
    <public type="drawable" name="ic_battery_60_black_48dp" id="0x7f0800d2" />
    <public type="drawable" name="ic_battery_80_black_48dp" id="0x7f0800d3" />
    <public type="drawable" name="ic_battery_90_black_48dp" id="0x7f0800d4" />
    <public type="drawable" name="ic_battery_alert_black_48dp" id="0x7f0800d5" />
    <public type="drawable" name="ic_battery_charging_20_black_48dp" id="0x7f0800d6" />
    <public type="drawable" name="ic_battery_charging_30_black_48dp" id="0x7f0800d7" />
    <public type="drawable" name="ic_battery_charging_50_black_48dp" id="0x7f0800d8" />
    <public type="drawable" name="ic_battery_charging_60_black_48dp" id="0x7f0800d9" />
    <public type="drawable" name="ic_battery_charging_80_black_48dp" id="0x7f0800da" />
    <public type="drawable" name="ic_battery_charging_90_black_48dp" id="0x7f0800db" />
    <public type="drawable" name="ic_battery_charging_full_black_48dp" id="0x7f0800dc" />
    <public type="drawable" name="ic_battery_full_black_48dp" id="0x7f0800dd" />
    <public type="drawable" name="ic_battery_std_black_48dp" id="0x7f0800de" />
    <public type="drawable" name="ic_battery_unknown_black_48dp" id="0x7f0800df" />
    <public type="drawable" name="ic_beach_access_black_48dp" id="0x7f0800e0" />
    <public type="drawable" name="ic_beenhere_black_48dp" id="0x7f0800e1" />
    <public type="drawable" name="ic_block_black_48dp" id="0x7f0800e2" />
    <public type="drawable" name="ic_bluetooth_audio_black_48dp" id="0x7f0800e3" />
    <public type="drawable" name="ic_bluetooth_black_48dp" id="0x7f0800e4" />
    <public type="drawable" name="ic_bluetooth_connected_black_48dp" id="0x7f0800e5" />
    <public type="drawable" name="ic_bluetooth_disabled_black_48dp" id="0x7f0800e6" />
    <public type="drawable" name="ic_bluetooth_searching_black_48dp" id="0x7f0800e7" />
    <public type="drawable" name="ic_blur_circular_black_48dp" id="0x7f0800e8" />
    <public type="drawable" name="ic_blur_linear_black_48dp" id="0x7f0800e9" />
    <public type="drawable" name="ic_blur_off_black_48dp" id="0x7f0800ea" />
    <public type="drawable" name="ic_blur_on_black_48dp" id="0x7f0800eb" />
    <public type="drawable" name="ic_book_black_48dp" id="0x7f0800ec" />
    <public type="drawable" name="ic_bookmark_black_48dp" id="0x7f0800ed" />
    <public type="drawable" name="ic_bookmark_border_black_48dp" id="0x7f0800ee" />
    <public type="drawable" name="ic_border_all_black_48dp" id="0x7f0800ef" />
    <public type="drawable" name="ic_border_bottom_black_48dp" id="0x7f0800f0" />
    <public type="drawable" name="ic_border_clear_black_48dp" id="0x7f0800f1" />
    <public type="drawable" name="ic_border_color_black_48dp" id="0x7f0800f2" />
    <public type="drawable" name="ic_border_horizontal_black_48dp" id="0x7f0800f3" />
    <public type="drawable" name="ic_border_inner_black_48dp" id="0x7f0800f4" />
    <public type="drawable" name="ic_border_left_black_48dp" id="0x7f0800f5" />
    <public type="drawable" name="ic_border_outer_black_48dp" id="0x7f0800f6" />
    <public type="drawable" name="ic_border_right_black_48dp" id="0x7f0800f7" />
    <public type="drawable" name="ic_border_style_black_48dp" id="0x7f0800f8" />
    <public type="drawable" name="ic_border_top_black_48dp" id="0x7f0800f9" />
    <public type="drawable" name="ic_border_vertical_black_48dp" id="0x7f0800fa" />
    <public type="drawable" name="ic_branding_watermark_black_48dp" id="0x7f0800fb" />
    <public type="drawable" name="ic_brightness_1_black_48dp" id="0x7f0800fc" />
    <public type="drawable" name="ic_brightness_2_black_48dp" id="0x7f0800fd" />
    <public type="drawable" name="ic_brightness_3_black_48dp" id="0x7f0800fe" />
    <public type="drawable" name="ic_brightness_4_black_48dp" id="0x7f0800ff" />
    <public type="drawable" name="ic_brightness_5_black_48dp" id="0x7f080100" />
    <public type="drawable" name="ic_brightness_6_black_48dp" id="0x7f080101" />
    <public type="drawable" name="ic_brightness_7_black_48dp" id="0x7f080102" />
    <public type="drawable" name="ic_brightness_auto_black_48dp" id="0x7f080103" />
    <public type="drawable" name="ic_brightness_high_black_48dp" id="0x7f080104" />
    <public type="drawable" name="ic_brightness_low_black_48dp" id="0x7f080105" />
    <public type="drawable" name="ic_brightness_medium_black_48dp" id="0x7f080106" />
    <public type="drawable" name="ic_broken_image_black_48dp" id="0x7f080107" />
    <public type="drawable" name="ic_brush_black_48dp" id="0x7f080108" />
    <public type="drawable" name="ic_bubble_chart_black_48dp" id="0x7f080109" />
    <public type="drawable" name="ic_bug_report_black_48dp" id="0x7f08010a" />
    <public type="drawable" name="ic_build_black_48dp" id="0x7f08010b" />
    <public type="drawable" name="ic_burst_mode_black_48dp" id="0x7f08010c" />
    <public type="drawable" name="ic_business_black_48dp" id="0x7f08010d" />
    <public type="drawable" name="ic_business_center_black_48dp" id="0x7f08010e" />
    <public type="drawable" name="ic_cached_black_48dp" id="0x7f08010f" />
    <public type="drawable" name="ic_cake_black_48dp" id="0x7f080110" />
    <public type="drawable" name="ic_call_black_48dp" id="0x7f080111" />
    <public type="drawable" name="ic_call_end_black_48dp" id="0x7f080112" />
    <public type="drawable" name="ic_call_made_black_48dp" id="0x7f080113" />
    <public type="drawable" name="ic_call_merge_black_48dp" id="0x7f080114" />
    <public type="drawable" name="ic_call_missed_black_48dp" id="0x7f080115" />
    <public type="drawable" name="ic_call_missed_outgoing_black_48dp" id="0x7f080116" />
    <public type="drawable" name="ic_call_received_black_48dp" id="0x7f080117" />
    <public type="drawable" name="ic_call_split_black_48dp" id="0x7f080118" />
    <public type="drawable" name="ic_call_to_action_black_48dp" id="0x7f080119" />
    <public type="drawable" name="ic_camera_alt_black_48dp" id="0x7f08011a" />
    <public type="drawable" name="ic_camera_black_48dp" id="0x7f08011b" />
    <public type="drawable" name="ic_camera_enhance_black_48dp" id="0x7f08011c" />
    <public type="drawable" name="ic_camera_front_black_48dp" id="0x7f08011d" />
    <public type="drawable" name="ic_camera_rear_black_48dp" id="0x7f08011e" />
    <public type="drawable" name="ic_camera_roll_black_48dp" id="0x7f08011f" />
    <public type="drawable" name="ic_cancel_black_48dp" id="0x7f080120" />
    <public type="drawable" name="ic_card_giftcard_black_48dp" id="0x7f080121" />
    <public type="drawable" name="ic_card_membership_black_48dp" id="0x7f080122" />
    <public type="drawable" name="ic_card_travel_black_48dp" id="0x7f080123" />
    <public type="drawable" name="ic_casino_black_48dp" id="0x7f080124" />
    <public type="drawable" name="ic_cast_black_48dp" id="0x7f080125" />
    <public type="drawable" name="ic_cast_connected_black_48dp" id="0x7f080126" />
    <public type="drawable" name="ic_center_focus_strong_black_48dp" id="0x7f080127" />
    <public type="drawable" name="ic_center_focus_weak_black_48dp" id="0x7f080128" />
    <public type="drawable" name="ic_change_history_black_48dp" id="0x7f080129" />
    <public type="drawable" name="ic_chat_black_48dp" id="0x7f08012a" />
    <public type="drawable" name="ic_chat_bubble_black_48dp" id="0x7f08012b" />
    <public type="drawable" name="ic_chat_bubble_outline_black_48dp" id="0x7f08012c" />
    <public type="drawable" name="ic_check_black_48dp" id="0x7f08012d" />
    <public type="drawable" name="ic_check_circle_black_48dp" id="0x7f08012e" />
    <public type="drawable" name="ic_chevron_left_black_48dp" id="0x7f08012f" />
    <public type="drawable" name="ic_chevron_right_black_48dp" id="0x7f080130" />
    <public type="drawable" name="ic_child_care_black_48dp" id="0x7f080131" />
    <public type="drawable" name="ic_child_friendly_black_48dp" id="0x7f080132" />
    <public type="drawable" name="ic_chrome_reader_mode_black_48dp" id="0x7f080133" />
    <public type="drawable" name="ic_class_black_48dp" id="0x7f080134" />
    <public type="drawable" name="ic_clear_all_black_48dp" id="0x7f080135" />
    <public type="drawable" name="ic_clear_black_48dp" id="0x7f080136" />
    <public type="drawable" name="ic_clear_white_48dp" id="0x7f080137" />
    <public type="drawable" name="ic_clock_black_24dp" id="0x7f080138" />
    <public type="drawable" name="ic_close_black_48dp" id="0x7f080139" />
    <public type="drawable" name="ic_close_white_24dp" id="0x7f08013a" />
    <public type="drawable" name="ic_closed_caption_black_48dp" id="0x7f08013b" />
    <public type="drawable" name="ic_cloud_black_48dp" id="0x7f08013c" />
    <public type="drawable" name="ic_cloud_circle_black_48dp" id="0x7f08013d" />
    <public type="drawable" name="ic_cloud_done_black_48dp" id="0x7f08013e" />
    <public type="drawable" name="ic_cloud_download_black_48dp" id="0x7f08013f" />
    <public type="drawable" name="ic_cloud_off_black_48dp" id="0x7f080140" />
    <public type="drawable" name="ic_cloud_queue_black_48dp" id="0x7f080141" />
    <public type="drawable" name="ic_cloud_upload_black_48dp" id="0x7f080142" />
    <public type="drawable" name="ic_code_black_48dp" id="0x7f080143" />
    <public type="drawable" name="ic_collections_black_48dp" id="0x7f080144" />
    <public type="drawable" name="ic_collections_bookmark_black_48dp" id="0x7f080145" />
    <public type="drawable" name="ic_color_lens_black_48dp" id="0x7f080146" />
    <public type="drawable" name="ic_colorize_black_48dp" id="0x7f080147" />
    <public type="drawable" name="ic_comment_black_48dp" id="0x7f080148" />
    <public type="drawable" name="ic_compare_arrows_black_48dp" id="0x7f080149" />
    <public type="drawable" name="ic_compare_black_48dp" id="0x7f08014a" />
    <public type="drawable" name="ic_computer_black_48dp" id="0x7f08014b" />
    <public type="drawable" name="ic_confirmation_number_black_48dp" id="0x7f08014c" />
    <public type="drawable" name="ic_contact_mail_black_48dp" id="0x7f08014d" />
    <public type="drawable" name="ic_contact_phone_black_48dp" id="0x7f08014e" />
    <public type="drawable" name="ic_contacts_black_48dp" id="0x7f08014f" />
    <public type="drawable" name="ic_content_copy_black_48dp" id="0x7f080150" />
    <public type="drawable" name="ic_content_cut_black_48dp" id="0x7f080151" />
    <public type="drawable" name="ic_content_paste_black_48dp" id="0x7f080152" />
    <public type="drawable" name="ic_control_point_black_48dp" id="0x7f080153" />
    <public type="drawable" name="ic_control_point_duplicate_black_48dp" id="0x7f080154" />
    <public type="drawable" name="ic_copyright_black_48dp" id="0x7f080155" />
    <public type="drawable" name="ic_create_black_48dp" id="0x7f080156" />
    <public type="drawable" name="ic_create_new_folder_black_48dp" id="0x7f080157" />
    <public type="drawable" name="ic_credit_card_black_48dp" id="0x7f080158" />
    <public type="drawable" name="ic_crop_16_9_black_48dp" id="0x7f080159" />
    <public type="drawable" name="ic_crop_3_2_black_48dp" id="0x7f08015a" />
    <public type="drawable" name="ic_crop_5_4_black_48dp" id="0x7f08015b" />
    <public type="drawable" name="ic_crop_7_5_black_48dp" id="0x7f08015c" />
    <public type="drawable" name="ic_crop_black_48dp" id="0x7f08015d" />
    <public type="drawable" name="ic_crop_din_black_48dp" id="0x7f08015e" />
    <public type="drawable" name="ic_crop_free_black_48dp" id="0x7f08015f" />
    <public type="drawable" name="ic_crop_landscape_black_48dp" id="0x7f080160" />
    <public type="drawable" name="ic_crop_original_black_48dp" id="0x7f080161" />
    <public type="drawable" name="ic_crop_portrait_black_48dp" id="0x7f080162" />
    <public type="drawable" name="ic_crop_rotate_black_48dp" id="0x7f080163" />
    <public type="drawable" name="ic_crop_square_black_48dp" id="0x7f080164" />
    <public type="drawable" name="ic_dashboard_black_48dp" id="0x7f080165" />
    <public type="drawable" name="ic_data_usage_black_48dp" id="0x7f080166" />
    <public type="drawable" name="ic_date_range_black_48dp" id="0x7f080167" />
    <public type="drawable" name="ic_date_range_white_48dp" id="0x7f080168" />
    <public type="drawable" name="ic_debug_console" id="0x7f080169" />
    <public type="drawable" name="ic_dehaze_black_48dp" id="0x7f08016a" />
    <public type="drawable" name="ic_delete_black_48dp" id="0x7f08016b" />
    <public type="drawable" name="ic_delete_forever_black_48dp" id="0x7f08016c" />
    <public type="drawable" name="ic_delete_sweep_black_48dp" id="0x7f08016d" />
    <public type="drawable" name="ic_description_black_48dp" id="0x7f08016e" />
    <public type="drawable" name="ic_desktop_mac_black_48dp" id="0x7f08016f" />
    <public type="drawable" name="ic_desktop_windows_black_48dp" id="0x7f080170" />
    <public type="drawable" name="ic_details_black_48dp" id="0x7f080171" />
    <public type="drawable" name="ic_developer_board_black_48dp" id="0x7f080172" />
    <public type="drawable" name="ic_developer_mode_black_48dp" id="0x7f080173" />
    <public type="drawable" name="ic_device_hub_black_48dp" id="0x7f080174" />
    <public type="drawable" name="ic_devices_black_48dp" id="0x7f080175" />
    <public type="drawable" name="ic_devices_other_black_48dp" id="0x7f080176" />
    <public type="drawable" name="ic_dialer_sip_black_48dp" id="0x7f080177" />
    <public type="drawable" name="ic_dialpad_black_48dp" id="0x7f080178" />
    <public type="drawable" name="ic_directions_bike_black_48dp" id="0x7f080179" />
    <public type="drawable" name="ic_directions_black_48dp" id="0x7f08017a" />
    <public type="drawable" name="ic_directions_boat_black_48dp" id="0x7f08017b" />
    <public type="drawable" name="ic_directions_bus_black_48dp" id="0x7f08017c" />
    <public type="drawable" name="ic_directions_car_black_48dp" id="0x7f08017d" />
    <public type="drawable" name="ic_directions_railway_black_48dp" id="0x7f08017e" />
    <public type="drawable" name="ic_directions_run_black_48dp" id="0x7f08017f" />
    <public type="drawable" name="ic_directions_subway_black_48dp" id="0x7f080180" />
    <public type="drawable" name="ic_directions_transit_black_48dp" id="0x7f080181" />
    <public type="drawable" name="ic_directions_walk_black_48dp" id="0x7f080182" />
    <public type="drawable" name="ic_disc_full_black_48dp" id="0x7f080183" />
    <public type="drawable" name="ic_dns_black_48dp" id="0x7f080184" />
    <public type="drawable" name="ic_do_not_disturb_alt_black_48dp" id="0x7f080185" />
    <public type="drawable" name="ic_do_not_disturb_black_48dp" id="0x7f080186" />
    <public type="drawable" name="ic_do_not_disturb_off_black_48dp" id="0x7f080187" />
    <public type="drawable" name="ic_do_not_disturb_on_black_48dp" id="0x7f080188" />
    <public type="drawable" name="ic_dock_black_48dp" id="0x7f080189" />
    <public type="drawable" name="ic_domain_black_48dp" id="0x7f08018a" />
    <public type="drawable" name="ic_done_all_black_48dp" id="0x7f08018b" />
    <public type="drawable" name="ic_done_black_48dp" id="0x7f08018c" />
    <public type="drawable" name="ic_donut_large_black_48dp" id="0x7f08018d" />
    <public type="drawable" name="ic_donut_small_black_48dp" id="0x7f08018e" />
    <public type="drawable" name="ic_drafts_black_48dp" id="0x7f08018f" />
    <public type="drawable" name="ic_drag_handle_black_48dp" id="0x7f080190" />
    <public type="drawable" name="ic_drive_eta_black_48dp" id="0x7f080191" />
    <public type="drawable" name="ic_dvr_black_48dp" id="0x7f080192" />
    <public type="drawable" name="ic_edit_black_48dp" id="0x7f080193" />
    <public type="drawable" name="ic_edit_location_black_48dp" id="0x7f080194" />
    <public type="drawable" name="ic_eject_black_48dp" id="0x7f080195" />
    <public type="drawable" name="ic_email_black_48dp" id="0x7f080196" />
    <public type="drawable" name="ic_enhanced_encryption_black_48dp" id="0x7f080197" />
    <public type="drawable" name="ic_equalizer_black_48dp" id="0x7f080198" />
    <public type="drawable" name="ic_error_black_48dp" id="0x7f080199" />
    <public type="drawable" name="ic_error_outline_black_48dp" id="0x7f08019a" />
    <public type="drawable" name="ic_euro_symbol_black_48dp" id="0x7f08019b" />
    <public type="drawable" name="ic_ev_station_black_48dp" id="0x7f08019c" />
    <public type="drawable" name="ic_event_available_black_48dp" id="0x7f08019d" />
    <public type="drawable" name="ic_event_black_48dp" id="0x7f08019e" />
    <public type="drawable" name="ic_event_busy_black_48dp" id="0x7f08019f" />
    <public type="drawable" name="ic_event_note_black_48dp" id="0x7f0801a0" />
    <public type="drawable" name="ic_event_seat_black_48dp" id="0x7f0801a1" />
    <public type="drawable" name="ic_exit_to_app_black_48dp" id="0x7f0801a2" />
    <public type="drawable" name="ic_expand_less_black_48dp" id="0x7f0801a3" />
    <public type="drawable" name="ic_expand_more_black_48dp" id="0x7f0801a4" />
    <public type="drawable" name="ic_explicit_black_48dp" id="0x7f0801a5" />
    <public type="drawable" name="ic_explore_black_48dp" id="0x7f0801a6" />
    <public type="drawable" name="ic_exposure_black_48dp" id="0x7f0801a7" />
    <public type="drawable" name="ic_exposure_neg_1_black_48dp" id="0x7f0801a8" />
    <public type="drawable" name="ic_exposure_neg_2_black_48dp" id="0x7f0801a9" />
    <public type="drawable" name="ic_exposure_plus_1_black_48dp" id="0x7f0801aa" />
    <public type="drawable" name="ic_exposure_plus_2_black_48dp" id="0x7f0801ab" />
    <public type="drawable" name="ic_exposure_zero_black_48dp" id="0x7f0801ac" />
    <public type="drawable" name="ic_extension_black_48dp" id="0x7f0801ad" />
    <public type="drawable" name="ic_face_black_48dp" id="0x7f0801ae" />
    <public type="drawable" name="ic_fast_forward_black_48dp" id="0x7f0801af" />
    <public type="drawable" name="ic_fast_rewind_black_48dp" id="0x7f0801b0" />
    <public type="drawable" name="ic_favorite_black_48dp" id="0x7f0801b1" />
    <public type="drawable" name="ic_favorite_border_black_48dp" id="0x7f0801b2" />
    <public type="drawable" name="ic_featured_play_list_black_48dp" id="0x7f0801b3" />
    <public type="drawable" name="ic_featured_video_black_48dp" id="0x7f0801b4" />
    <public type="drawable" name="ic_feedback_black_48dp" id="0x7f0801b5" />
    <public type="drawable" name="ic_fiber_dvr_black_48dp" id="0x7f0801b6" />
    <public type="drawable" name="ic_fiber_manual_record_black_48dp" id="0x7f0801b7" />
    <public type="drawable" name="ic_fiber_new_black_48dp" id="0x7f0801b8" />
    <public type="drawable" name="ic_fiber_pin_black_48dp" id="0x7f0801b9" />
    <public type="drawable" name="ic_fiber_smart_record_black_48dp" id="0x7f0801ba" />
    <public type="drawable" name="ic_file_download_black_48dp" id="0x7f0801bb" />
    <public type="drawable" name="ic_file_type_js" id="0x7f0801bc" />
    <public type="drawable" name="ic_file_upload_black_48dp" id="0x7f0801bd" />
    <public type="drawable" name="ic_filter_1_black_48dp" id="0x7f0801be" />
    <public type="drawable" name="ic_filter_2_black_48dp" id="0x7f0801bf" />
    <public type="drawable" name="ic_filter_3_black_48dp" id="0x7f0801c0" />
    <public type="drawable" name="ic_filter_4_black_48dp" id="0x7f0801c1" />
    <public type="drawable" name="ic_filter_5_black_48dp" id="0x7f0801c2" />
    <public type="drawable" name="ic_filter_6_black_48dp" id="0x7f0801c3" />
    <public type="drawable" name="ic_filter_7_black_48dp" id="0x7f0801c4" />
    <public type="drawable" name="ic_filter_8_black_48dp" id="0x7f0801c5" />
    <public type="drawable" name="ic_filter_9_black_48dp" id="0x7f0801c6" />
    <public type="drawable" name="ic_filter_9_plus_black_48dp" id="0x7f0801c7" />
    <public type="drawable" name="ic_filter_b_and_w_black_48dp" id="0x7f0801c8" />
    <public type="drawable" name="ic_filter_black_48dp" id="0x7f0801c9" />
    <public type="drawable" name="ic_filter_center_focus_black_48dp" id="0x7f0801ca" />
    <public type="drawable" name="ic_filter_drama_black_48dp" id="0x7f0801cb" />
    <public type="drawable" name="ic_filter_frames_black_48dp" id="0x7f0801cc" />
    <public type="drawable" name="ic_filter_hdr_black_48dp" id="0x7f0801cd" />
    <public type="drawable" name="ic_filter_list_black_48dp" id="0x7f0801ce" />
    <public type="drawable" name="ic_filter_none_black_48dp" id="0x7f0801cf" />
    <public type="drawable" name="ic_filter_tilt_shift_black_48dp" id="0x7f0801d0" />
    <public type="drawable" name="ic_filter_vintage_black_48dp" id="0x7f0801d1" />
    <public type="drawable" name="ic_find_in_page_black_48dp" id="0x7f0801d2" />
    <public type="drawable" name="ic_find_replace_black_48dp" id="0x7f0801d3" />
    <public type="drawable" name="ic_fingerprint_black_48dp" id="0x7f0801d4" />
    <public type="drawable" name="ic_first_page_black_48dp" id="0x7f0801d5" />
    <public type="drawable" name="ic_fitness_center_black_48dp" id="0x7f0801d6" />
    <public type="drawable" name="ic_flag_black_48dp" id="0x7f0801d7" />
    <public type="drawable" name="ic_flare_black_48dp" id="0x7f0801d8" />
    <public type="drawable" name="ic_flash_auto_black_48dp" id="0x7f0801d9" />
    <public type="drawable" name="ic_flash_off_black_48dp" id="0x7f0801da" />
    <public type="drawable" name="ic_flash_on_black_48dp" id="0x7f0801db" />
    <public type="drawable" name="ic_flight_black_48dp" id="0x7f0801dc" />
    <public type="drawable" name="ic_flight_land_black_48dp" id="0x7f0801dd" />
    <public type="drawable" name="ic_flight_takeoff_black_48dp" id="0x7f0801de" />
    <public type="drawable" name="ic_flip_black_48dp" id="0x7f0801df" />
    <public type="drawable" name="ic_flip_to_back_black_48dp" id="0x7f0801e0" />
    <public type="drawable" name="ic_flip_to_front_black_48dp" id="0x7f0801e1" />
    <public type="drawable" name="ic_folder_black_48dp" id="0x7f0801e2" />
    <public type="drawable" name="ic_folder_open_black_48dp" id="0x7f0801e3" />
    <public type="drawable" name="ic_folder_shared_black_48dp" id="0x7f0801e4" />
    <public type="drawable" name="ic_folder_special_black_48dp" id="0x7f0801e5" />
    <public type="drawable" name="ic_font_download_black_48dp" id="0x7f0801e6" />
    <public type="drawable" name="ic_format_align_center_black_48dp" id="0x7f0801e7" />
    <public type="drawable" name="ic_format_align_justify_black_48dp" id="0x7f0801e8" />
    <public type="drawable" name="ic_format_align_left_black_48dp" id="0x7f0801e9" />
    <public type="drawable" name="ic_format_align_right_black_48dp" id="0x7f0801ea" />
    <public type="drawable" name="ic_format_bold_black_48dp" id="0x7f0801eb" />
    <public type="drawable" name="ic_format_clear_black_48dp" id="0x7f0801ec" />
    <public type="drawable" name="ic_format_color_fill_black_48dp" id="0x7f0801ed" />
    <public type="drawable" name="ic_format_color_reset_black_48dp" id="0x7f0801ee" />
    <public type="drawable" name="ic_format_color_text_black_48dp" id="0x7f0801ef" />
    <public type="drawable" name="ic_format_indent_decrease_black_48dp" id="0x7f0801f0" />
    <public type="drawable" name="ic_format_indent_increase_black_48dp" id="0x7f0801f1" />
    <public type="drawable" name="ic_format_italic_black_48dp" id="0x7f0801f2" />
    <public type="drawable" name="ic_format_line_spacing_black_48dp" id="0x7f0801f3" />
    <public type="drawable" name="ic_format_list_bulleted_black_48dp" id="0x7f0801f4" />
    <public type="drawable" name="ic_format_list_numbered_black_48dp" id="0x7f0801f5" />
    <public type="drawable" name="ic_format_paint_black_48dp" id="0x7f0801f6" />
    <public type="drawable" name="ic_format_quote_black_48dp" id="0x7f0801f7" />
    <public type="drawable" name="ic_format_shapes_black_48dp" id="0x7f0801f8" />
    <public type="drawable" name="ic_format_size_black_48dp" id="0x7f0801f9" />
    <public type="drawable" name="ic_format_strikethrough_black_48dp" id="0x7f0801fa" />
    <public type="drawable" name="ic_format_textdirection_l_to_r_black_48dp" id="0x7f0801fb" />
    <public type="drawable" name="ic_format_textdirection_r_to_l_black_48dp" id="0x7f0801fc" />
    <public type="drawable" name="ic_format_underlined_black_48dp" id="0x7f0801fd" />
    <public type="drawable" name="ic_forum_black_48dp" id="0x7f0801fe" />
    <public type="drawable" name="ic_forward_10_black_48dp" id="0x7f0801ff" />
    <public type="drawable" name="ic_forward_30_black_48dp" id="0x7f080200" />
    <public type="drawable" name="ic_forward_5_black_48dp" id="0x7f080201" />
    <public type="drawable" name="ic_forward_black_48dp" id="0x7f080202" />
    <public type="drawable" name="ic_free_breakfast_black_48dp" id="0x7f080203" />
    <public type="drawable" name="ic_fullscreen_black_48dp" id="0x7f080204" />
    <public type="drawable" name="ic_fullscreen_exit_black_48dp" id="0x7f080205" />
    <public type="drawable" name="ic_functions_black_48dp" id="0x7f080206" />
    <public type="drawable" name="ic_g_translate_black_48dp" id="0x7f080207" />
    <public type="drawable" name="ic_gamepad_black_48dp" id="0x7f080208" />
    <public type="drawable" name="ic_games_black_48dp" id="0x7f080209" />
    <public type="drawable" name="ic_gavel_black_48dp" id="0x7f08020a" />
    <public type="drawable" name="ic_gesture_black_48dp" id="0x7f08020b" />
    <public type="drawable" name="ic_get_app_black_48dp" id="0x7f08020c" />
    <public type="drawable" name="ic_gif_black_48dp" id="0x7f08020d" />
    <public type="drawable" name="ic_golf_course_black_48dp" id="0x7f08020e" />
    <public type="drawable" name="ic_gps_fixed_black_48dp" id="0x7f08020f" />
    <public type="drawable" name="ic_gps_not_fixed_black_48dp" id="0x7f080210" />
    <public type="drawable" name="ic_gps_off_black_48dp" id="0x7f080211" />
    <public type="drawable" name="ic_grade_black_48dp" id="0x7f080212" />
    <public type="drawable" name="ic_gradient_black_48dp" id="0x7f080213" />
    <public type="drawable" name="ic_grain_black_48dp" id="0x7f080214" />
    <public type="drawable" name="ic_graphic_eq_black_48dp" id="0x7f080215" />
    <public type="drawable" name="ic_grid_off_black_48dp" id="0x7f080216" />
    <public type="drawable" name="ic_grid_on_black_48dp" id="0x7f080217" />
    <public type="drawable" name="ic_group_add_black_48dp" id="0x7f080218" />
    <public type="drawable" name="ic_group_black_48dp" id="0x7f080219" />
    <public type="drawable" name="ic_group_work_black_48dp" id="0x7f08021a" />
    <public type="drawable" name="ic_hd_black_48dp" id="0x7f08021b" />
    <public type="drawable" name="ic_hdr_off_black_48dp" id="0x7f08021c" />
    <public type="drawable" name="ic_hdr_on_black_48dp" id="0x7f08021d" />
    <public type="drawable" name="ic_hdr_strong_black_48dp" id="0x7f08021e" />
    <public type="drawable" name="ic_hdr_weak_black_48dp" id="0x7f08021f" />
    <public type="drawable" name="ic_headset_black_48dp" id="0x7f080220" />
    <public type="drawable" name="ic_headset_mic_black_48dp" id="0x7f080221" />
    <public type="drawable" name="ic_healing_black_48dp" id="0x7f080222" />
    <public type="drawable" name="ic_hearing_black_48dp" id="0x7f080223" />
    <public type="drawable" name="ic_help_black_48dp" id="0x7f080224" />
    <public type="drawable" name="ic_help_outline_black_48dp" id="0x7f080225" />
    <public type="drawable" name="ic_high_quality_black_48dp" id="0x7f080226" />
    <public type="drawable" name="ic_highlight_black_48dp" id="0x7f080227" />
    <public type="drawable" name="ic_highlight_off_black_48dp" id="0x7f080228" />
    <public type="drawable" name="ic_history_black_48dp" id="0x7f080229" />
    <public type="drawable" name="ic_home_black_48dp" id="0x7f08022a" />
    <public type="drawable" name="ic_hot_tub_black_48dp" id="0x7f08022b" />
    <public type="drawable" name="ic_hotel_black_48dp" id="0x7f08022c" />
    <public type="drawable" name="ic_hourglass_empty_black_48dp" id="0x7f08022d" />
    <public type="drawable" name="ic_hourglass_full_black_48dp" id="0x7f08022e" />
    <public type="drawable" name="ic_http_black_48dp" id="0x7f08022f" />
    <public type="drawable" name="ic_https_black_48dp" id="0x7f080230" />
    <public type="drawable" name="ic_image_aspect_ratio_black_48dp" id="0x7f080231" />
    <public type="drawable" name="ic_image_black_48dp" id="0x7f080232" />
    <public type="drawable" name="ic_import_contacts_black_48dp" id="0x7f080233" />
    <public type="drawable" name="ic_import_export_black_48dp" id="0x7f080234" />
    <public type="drawable" name="ic_important_devices_black_48dp" id="0x7f080235" />
    <public type="drawable" name="ic_inbox_black_48dp" id="0x7f080236" />
    <public type="drawable" name="ic_info_black_48dp" id="0x7f080237" />
    <public type="drawable" name="ic_info_outline_black_48dp" id="0x7f080238" />
    <public type="drawable" name="ic_input_black_48dp" id="0x7f080239" />
    <public type="drawable" name="ic_insert_chart_black_48dp" id="0x7f08023a" />
    <public type="drawable" name="ic_insert_comment_black_48dp" id="0x7f08023b" />
    <public type="drawable" name="ic_insert_drive_file_black_48dp" id="0x7f08023c" />
    <public type="drawable" name="ic_insert_emoticon_black_48dp" id="0x7f08023d" />
    <public type="drawable" name="ic_insert_invitation_black_48dp" id="0x7f08023e" />
    <public type="drawable" name="ic_insert_link_black_48dp" id="0x7f08023f" />
    <public type="drawable" name="ic_insert_photo_black_48dp" id="0x7f080240" />
    <public type="drawable" name="ic_invert_colors_black_48dp" id="0x7f080241" />
    <public type="drawable" name="ic_invert_colors_off_black_48dp" id="0x7f080242" />
    <public type="drawable" name="ic_iso_black_48dp" id="0x7f080243" />
    <public type="drawable" name="ic_keyboard_arrow_down_black_48dp" id="0x7f080244" />
    <public type="drawable" name="ic_keyboard_arrow_left_black_48dp" id="0x7f080245" />
    <public type="drawable" name="ic_keyboard_arrow_right_black_48dp" id="0x7f080246" />
    <public type="drawable" name="ic_keyboard_arrow_up_black_48dp" id="0x7f080247" />
    <public type="drawable" name="ic_keyboard_backspace_black_48dp" id="0x7f080248" />
    <public type="drawable" name="ic_keyboard_black_24dp" id="0x7f080249" />
    <public type="drawable" name="ic_keyboard_black_48dp" id="0x7f08024a" />
    <public type="drawable" name="ic_keyboard_capslock_black_48dp" id="0x7f08024b" />
    <public type="drawable" name="ic_keyboard_hide_black_48dp" id="0x7f08024c" />
    <public type="drawable" name="ic_keyboard_return_black_48dp" id="0x7f08024d" />
    <public type="drawable" name="ic_keyboard_tab_black_48dp" id="0x7f08024e" />
    <public type="drawable" name="ic_keyboard_voice_black_48dp" id="0x7f08024f" />
    <public type="drawable" name="ic_kitchen_black_48dp" id="0x7f080250" />
    <public type="drawable" name="ic_label_black_48dp" id="0x7f080251" />
    <public type="drawable" name="ic_label_outline_black_48dp" id="0x7f080252" />
    <public type="drawable" name="ic_landscape_black_48dp" id="0x7f080253" />
    <public type="drawable" name="ic_language_black_48dp" id="0x7f080254" />
    <public type="drawable" name="ic_laptop_black_48dp" id="0x7f080255" />
    <public type="drawable" name="ic_laptop_chromebook_black_48dp" id="0x7f080256" />
    <public type="drawable" name="ic_laptop_mac_black_48dp" id="0x7f080257" />
    <public type="drawable" name="ic_laptop_windows_black_48dp" id="0x7f080258" />
    <public type="drawable" name="ic_last_page_black_48dp" id="0x7f080259" />
    <public type="drawable" name="ic_launch_black_48dp" id="0x7f08025a" />
    <public type="drawable" name="ic_layers_black_48dp" id="0x7f08025b" />
    <public type="drawable" name="ic_layers_clear_black_48dp" id="0x7f08025c" />
    <public type="drawable" name="ic_leak_add_black_48dp" id="0x7f08025d" />
    <public type="drawable" name="ic_leak_remove_black_48dp" id="0x7f08025e" />
    <public type="drawable" name="ic_lens_black_48dp" id="0x7f08025f" />
    <public type="drawable" name="ic_library_add_black_48dp" id="0x7f080260" />
    <public type="drawable" name="ic_library_books_black_48dp" id="0x7f080261" />
    <public type="drawable" name="ic_library_music_black_48dp" id="0x7f080262" />
    <public type="drawable" name="ic_lightbulb_outline_black_48dp" id="0x7f080263" />
    <public type="drawable" name="ic_line_style_black_48dp" id="0x7f080264" />
    <public type="drawable" name="ic_line_weight_black_48dp" id="0x7f080265" />
    <public type="drawable" name="ic_linear_scale_black_48dp" id="0x7f080266" />
    <public type="drawable" name="ic_link_black_48dp" id="0x7f080267" />
    <public type="drawable" name="ic_linked_camera_black_48dp" id="0x7f080268" />
    <public type="drawable" name="ic_list_black_48dp" id="0x7f080269" />
    <public type="drawable" name="ic_live_help_black_48dp" id="0x7f08026a" />
    <public type="drawable" name="ic_live_tv_black_48dp" id="0x7f08026b" />
    <public type="drawable" name="ic_local_activity_black_48dp" id="0x7f08026c" />
    <public type="drawable" name="ic_local_airport_black_48dp" id="0x7f08026d" />
    <public type="drawable" name="ic_local_atm_black_48dp" id="0x7f08026e" />
    <public type="drawable" name="ic_local_bar_black_48dp" id="0x7f08026f" />
    <public type="drawable" name="ic_local_cafe_black_48dp" id="0x7f080270" />
    <public type="drawable" name="ic_local_car_wash_black_48dp" id="0x7f080271" />
    <public type="drawable" name="ic_local_convenience_store_black_48dp" id="0x7f080272" />
    <public type="drawable" name="ic_local_dining_black_48dp" id="0x7f080273" />
    <public type="drawable" name="ic_local_drink_black_48dp" id="0x7f080274" />
    <public type="drawable" name="ic_local_florist_black_48dp" id="0x7f080275" />
    <public type="drawable" name="ic_local_gas_station_black_48dp" id="0x7f080276" />
    <public type="drawable" name="ic_local_grocery_store_black_48dp" id="0x7f080277" />
    <public type="drawable" name="ic_local_hospital_black_48dp" id="0x7f080278" />
    <public type="drawable" name="ic_local_hotel_black_48dp" id="0x7f080279" />
    <public type="drawable" name="ic_local_laundry_service_black_48dp" id="0x7f08027a" />
    <public type="drawable" name="ic_local_library_black_48dp" id="0x7f08027b" />
    <public type="drawable" name="ic_local_mall_black_48dp" id="0x7f08027c" />
    <public type="drawable" name="ic_local_movies_black_48dp" id="0x7f08027d" />
    <public type="drawable" name="ic_local_offer_black_48dp" id="0x7f08027e" />
    <public type="drawable" name="ic_local_parking_black_48dp" id="0x7f08027f" />
    <public type="drawable" name="ic_local_pharmacy_black_48dp" id="0x7f080280" />
    <public type="drawable" name="ic_local_phone_black_48dp" id="0x7f080281" />
    <public type="drawable" name="ic_local_pizza_black_48dp" id="0x7f080282" />
    <public type="drawable" name="ic_local_play_black_48dp" id="0x7f080283" />
    <public type="drawable" name="ic_local_post_office_black_48dp" id="0x7f080284" />
    <public type="drawable" name="ic_local_printshop_black_48dp" id="0x7f080285" />
    <public type="drawable" name="ic_local_see_black_48dp" id="0x7f080286" />
    <public type="drawable" name="ic_local_shipping_black_48dp" id="0x7f080287" />
    <public type="drawable" name="ic_local_taxi_black_48dp" id="0x7f080288" />
    <public type="drawable" name="ic_location_city_black_48dp" id="0x7f080289" />
    <public type="drawable" name="ic_location_disabled_black_48dp" id="0x7f08028a" />
    <public type="drawable" name="ic_location_off_black_48dp" id="0x7f08028b" />
    <public type="drawable" name="ic_location_on_black_48dp" id="0x7f08028c" />
    <public type="drawable" name="ic_location_searching_black_48dp" id="0x7f08028d" />
    <public type="drawable" name="ic_lock_black_48dp" id="0x7f08028e" />
    <public type="drawable" name="ic_lock_open_black_48dp" id="0x7f08028f" />
    <public type="drawable" name="ic_lock_outline_black_48dp" id="0x7f080290" />
    <public type="drawable" name="ic_looks_3_black_48dp" id="0x7f080291" />
    <public type="drawable" name="ic_looks_4_black_48dp" id="0x7f080292" />
    <public type="drawable" name="ic_looks_5_black_48dp" id="0x7f080293" />
    <public type="drawable" name="ic_looks_6_black_48dp" id="0x7f080294" />
    <public type="drawable" name="ic_looks_black_48dp" id="0x7f080295" />
    <public type="drawable" name="ic_looks_one_black_48dp" id="0x7f080296" />
    <public type="drawable" name="ic_looks_two_black_48dp" id="0x7f080297" />
    <public type="drawable" name="ic_loop_black_48dp" id="0x7f080298" />
    <public type="drawable" name="ic_loupe_black_48dp" id="0x7f080299" />
    <public type="drawable" name="ic_low_priority_black_48dp" id="0x7f08029a" />
    <public type="drawable" name="ic_loyalty_black_48dp" id="0x7f08029b" />
    <public type="drawable" name="ic_m3_chip_check" id="0x7f08029c" />
    <public type="drawable" name="ic_m3_chip_checked_circle" id="0x7f08029d" />
    <public type="drawable" name="ic_m3_chip_close" id="0x7f08029e" />
    <public type="drawable" name="ic_mail_black_48dp" id="0x7f08029f" />
    <public type="drawable" name="ic_mail_outline_black_48dp" id="0x7f0802a0" />
    <public type="drawable" name="ic_map_black_48dp" id="0x7f0802a1" />
    <public type="drawable" name="ic_markunread_black_48dp" id="0x7f0802a2" />
    <public type="drawable" name="ic_markunread_mailbox_black_48dp" id="0x7f0802a3" />
    <public type="drawable" name="ic_memory_black_48dp" id="0x7f0802a4" />
    <public type="drawable" name="ic_menu_black_48dp" id="0x7f0802a5" />
    <public type="drawable" name="ic_merge_type_black_48dp" id="0x7f0802a6" />
    <public type="drawable" name="ic_message_black_48dp" id="0x7f0802a7" />
    <public type="drawable" name="ic_mic_black_48dp" id="0x7f0802a8" />
    <public type="drawable" name="ic_mic_none_black_48dp" id="0x7f0802a9" />
    <public type="drawable" name="ic_mic_off_black_48dp" id="0x7f0802aa" />
    <public type="drawable" name="ic_mms_black_48dp" id="0x7f0802ab" />
    <public type="drawable" name="ic_mode_comment_black_48dp" id="0x7f0802ac" />
    <public type="drawable" name="ic_mode_edit_black_48dp" id="0x7f0802ad" />
    <public type="drawable" name="ic_monetization_on_black_48dp" id="0x7f0802ae" />
    <public type="drawable" name="ic_money_off_black_48dp" id="0x7f0802af" />
    <public type="drawable" name="ic_monochrome_photos_black_48dp" id="0x7f0802b0" />
    <public type="drawable" name="ic_mood_bad_black_48dp" id="0x7f0802b1" />
    <public type="drawable" name="ic_mood_black_48dp" id="0x7f0802b2" />
    <public type="drawable" name="ic_more_black_48dp" id="0x7f0802b3" />
    <public type="drawable" name="ic_more_horiz_black_48dp" id="0x7f0802b4" />
    <public type="drawable" name="ic_more_vert_black_48dp" id="0x7f0802b5" />
    <public type="drawable" name="ic_motorcycle_black_48dp" id="0x7f0802b6" />
    <public type="drawable" name="ic_mouse_black_48dp" id="0x7f0802b7" />
    <public type="drawable" name="ic_move_cursor" id="0x7f0802b8" />
    <public type="drawable" name="ic_move_to_inbox_black_48dp" id="0x7f0802b9" />
    <public type="drawable" name="ic_movie_black_48dp" id="0x7f0802ba" />
    <public type="drawable" name="ic_movie_creation_black_48dp" id="0x7f0802bb" />
    <public type="drawable" name="ic_movie_filter_black_48dp" id="0x7f0802bc" />
    <public type="drawable" name="ic_mtrl_checked_circle" id="0x7f0802bd" />
    <public type="drawable" name="ic_mtrl_chip_checked_black" id="0x7f0802be" />
    <public type="drawable" name="ic_mtrl_chip_checked_circle" id="0x7f0802bf" />
    <public type="drawable" name="ic_mtrl_chip_close_circle" id="0x7f0802c0" />
    <public type="drawable" name="ic_multiline_chart_black_48dp" id="0x7f0802c1" />
    <public type="drawable" name="ic_music_note_black_48dp" id="0x7f0802c2" />
    <public type="drawable" name="ic_music_video_black_48dp" id="0x7f0802c3" />
    <public type="drawable" name="ic_my_location_black_48dp" id="0x7f0802c4" />
    <public type="drawable" name="ic_nature_black_48dp" id="0x7f0802c5" />
    <public type="drawable" name="ic_nature_people_black_48dp" id="0x7f0802c6" />
    <public type="drawable" name="ic_navigate_before_black_48dp" id="0x7f0802c7" />
    <public type="drawable" name="ic_navigate_next_black_48dp" id="0x7f0802c8" />
    <public type="drawable" name="ic_navigation_black_48dp" id="0x7f0802c9" />
    <public type="drawable" name="ic_near_me_black_48dp" id="0x7f0802ca" />
    <public type="drawable" name="ic_network_cell_black_48dp" id="0x7f0802cb" />
    <public type="drawable" name="ic_network_check_black_48dp" id="0x7f0802cc" />
    <public type="drawable" name="ic_network_locked_black_48dp" id="0x7f0802cd" />
    <public type="drawable" name="ic_network_wifi_black_48dp" id="0x7f0802ce" />
    <public type="drawable" name="ic_new_releases_black_48dp" id="0x7f0802cf" />
    <public type="drawable" name="ic_next_week_black_48dp" id="0x7f0802d0" />
    <public type="drawable" name="ic_nfc_black_48dp" id="0x7f0802d1" />
    <public type="drawable" name="ic_no_encryption_black_48dp" id="0x7f0802d2" />
    <public type="drawable" name="ic_no_sim_black_48dp" id="0x7f0802d3" />
    <public type="drawable" name="ic_not_interested_black_48dp" id="0x7f0802d4" />
    <public type="drawable" name="ic_note_add_black_48dp" id="0x7f0802d5" />
    <public type="drawable" name="ic_note_black_48dp" id="0x7f0802d6" />
    <public type="drawable" name="ic_notifications_active_black_48dp" id="0x7f0802d7" />
    <public type="drawable" name="ic_notifications_black_48dp" id="0x7f0802d8" />
    <public type="drawable" name="ic_notifications_none_black_48dp" id="0x7f0802d9" />
    <public type="drawable" name="ic_notifications_off_black_48dp" id="0x7f0802da" />
    <public type="drawable" name="ic_notifications_paused_black_48dp" id="0x7f0802db" />
    <public type="drawable" name="ic_offline_pin_black_48dp" id="0x7f0802dc" />
    <public type="drawable" name="ic_ondemand_video_black_48dp" id="0x7f0802dd" />
    <public type="drawable" name="ic_opacity_black_48dp" id="0x7f0802de" />
    <public type="drawable" name="ic_open_in_browser_black_48dp" id="0x7f0802df" />
    <public type="drawable" name="ic_open_in_new_black_48dp" id="0x7f0802e0" />
    <public type="drawable" name="ic_open_with_black_48dp" id="0x7f0802e1" />
    <public type="drawable" name="ic_pages_black_48dp" id="0x7f0802e2" />
    <public type="drawable" name="ic_pageview_black_48dp" id="0x7f0802e3" />
    <public type="drawable" name="ic_palette_black_48dp" id="0x7f0802e4" />
    <public type="drawable" name="ic_pan_tool_black_48dp" id="0x7f0802e5" />
    <public type="drawable" name="ic_panorama_black_48dp" id="0x7f0802e6" />
    <public type="drawable" name="ic_panorama_fish_eye_black_48dp" id="0x7f0802e7" />
    <public type="drawable" name="ic_panorama_horizontal_black_48dp" id="0x7f0802e8" />
    <public type="drawable" name="ic_panorama_vertical_black_48dp" id="0x7f0802e9" />
    <public type="drawable" name="ic_panorama_wide_angle_black_48dp" id="0x7f0802ea" />
    <public type="drawable" name="ic_party_mode_black_48dp" id="0x7f0802eb" />
    <public type="drawable" name="ic_pause_black_48dp" id="0x7f0802ec" />
    <public type="drawable" name="ic_pause_circle_filled_black_48dp" id="0x7f0802ed" />
    <public type="drawable" name="ic_pause_circle_outline_black_48dp" id="0x7f0802ee" />
    <public type="drawable" name="ic_payment_black_48dp" id="0x7f0802ef" />
    <public type="drawable" name="ic_people_black_48dp" id="0x7f0802f0" />
    <public type="drawable" name="ic_people_outline_black_48dp" id="0x7f0802f1" />
    <public type="drawable" name="ic_perm_camera_mic_black_48dp" id="0x7f0802f2" />
    <public type="drawable" name="ic_perm_contact_calendar_black_48dp" id="0x7f0802f3" />
    <public type="drawable" name="ic_perm_data_setting_black_48dp" id="0x7f0802f4" />
    <public type="drawable" name="ic_perm_device_information_black_48dp" id="0x7f0802f5" />
    <public type="drawable" name="ic_perm_identity_black_48dp" id="0x7f0802f6" />
    <public type="drawable" name="ic_perm_media_black_48dp" id="0x7f0802f7" />
    <public type="drawable" name="ic_perm_phone_msg_black_48dp" id="0x7f0802f8" />
    <public type="drawable" name="ic_perm_scan_wifi_black_48dp" id="0x7f0802f9" />
    <public type="drawable" name="ic_person_add_black_48dp" id="0x7f0802fa" />
    <public type="drawable" name="ic_person_black_48dp" id="0x7f0802fb" />
    <public type="drawable" name="ic_person_outline_black_48dp" id="0x7f0802fc" />
    <public type="drawable" name="ic_person_pin_black_48dp" id="0x7f0802fd" />
    <public type="drawable" name="ic_person_pin_circle_black_48dp" id="0x7f0802fe" />
    <public type="drawable" name="ic_personal_video_black_48dp" id="0x7f0802ff" />
    <public type="drawable" name="ic_pets_black_48dp" id="0x7f080300" />
    <public type="drawable" name="ic_phone_android_black_48dp" id="0x7f080301" />
    <public type="drawable" name="ic_phone_black_48dp" id="0x7f080302" />
    <public type="drawable" name="ic_phone_bluetooth_speaker_black_48dp" id="0x7f080303" />
    <public type="drawable" name="ic_phone_forwarded_black_48dp" id="0x7f080304" />
    <public type="drawable" name="ic_phone_in_talk_black_48dp" id="0x7f080305" />
    <public type="drawable" name="ic_phone_iphone_black_48dp" id="0x7f080306" />
    <public type="drawable" name="ic_phone_locked_black_48dp" id="0x7f080307" />
    <public type="drawable" name="ic_phone_missed_black_48dp" id="0x7f080308" />
    <public type="drawable" name="ic_phone_paused_black_48dp" id="0x7f080309" />
    <public type="drawable" name="ic_phonelink_black_48dp" id="0x7f08030a" />
    <public type="drawable" name="ic_phonelink_erase_black_48dp" id="0x7f08030b" />
    <public type="drawable" name="ic_phonelink_lock_black_48dp" id="0x7f08030c" />
    <public type="drawable" name="ic_phonelink_off_black_48dp" id="0x7f08030d" />
    <public type="drawable" name="ic_phonelink_ring_black_48dp" id="0x7f08030e" />
    <public type="drawable" name="ic_phonelink_setup_black_48dp" id="0x7f08030f" />
    <public type="drawable" name="ic_photo_album_black_48dp" id="0x7f080310" />
    <public type="drawable" name="ic_photo_black_48dp" id="0x7f080311" />
    <public type="drawable" name="ic_photo_camera_black_48dp" id="0x7f080312" />
    <public type="drawable" name="ic_photo_filter_black_48dp" id="0x7f080313" />
    <public type="drawable" name="ic_photo_library_black_48dp" id="0x7f080314" />
    <public type="drawable" name="ic_photo_size_select_actual_black_48dp" id="0x7f080315" />
    <public type="drawable" name="ic_photo_size_select_large_black_48dp" id="0x7f080316" />
    <public type="drawable" name="ic_photo_size_select_small_black_48dp" id="0x7f080317" />
    <public type="drawable" name="ic_picture_as_pdf_black_48dp" id="0x7f080318" />
    <public type="drawable" name="ic_picture_in_picture_alt_black_48dp" id="0x7f080319" />
    <public type="drawable" name="ic_picture_in_picture_black_48dp" id="0x7f08031a" />
    <public type="drawable" name="ic_pie_chart_black_48dp" id="0x7f08031b" />
    <public type="drawable" name="ic_pie_chart_outlined_black_48dp" id="0x7f08031c" />
    <public type="drawable" name="ic_pin_drop_black_48dp" id="0x7f08031d" />
    <public type="drawable" name="ic_place_black_48dp" id="0x7f08031e" />
    <public type="drawable" name="ic_play_arrow_black_48dp" id="0x7f08031f" />
    <public type="drawable" name="ic_play_circle_filled_black_48dp" id="0x7f080320" />
    <public type="drawable" name="ic_play_circle_filled_white_black_48dp" id="0x7f080321" />
    <public type="drawable" name="ic_play_circle_outline_black_48dp" id="0x7f080322" />
    <public type="drawable" name="ic_play_for_work_black_48dp" id="0x7f080323" />
    <public type="drawable" name="ic_playlist_add_black_48dp" id="0x7f080324" />
    <public type="drawable" name="ic_playlist_add_check_black_48dp" id="0x7f080325" />
    <public type="drawable" name="ic_playlist_play_black_48dp" id="0x7f080326" />
    <public type="drawable" name="ic_plus_one_black_48dp" id="0x7f080327" />
    <public type="drawable" name="ic_poll_black_48dp" id="0x7f080328" />
    <public type="drawable" name="ic_polymer_black_48dp" id="0x7f080329" />
    <public type="drawable" name="ic_pool_black_48dp" id="0x7f08032a" />
    <public type="drawable" name="ic_portable_wifi_off_black_48dp" id="0x7f08032b" />
    <public type="drawable" name="ic_portrait_black_48dp" id="0x7f08032c" />
    <public type="drawable" name="ic_power_black_48dp" id="0x7f08032d" />
    <public type="drawable" name="ic_power_input_black_48dp" id="0x7f08032e" />
    <public type="drawable" name="ic_power_settings_new_black_48dp" id="0x7f08032f" />
    <public type="drawable" name="ic_pregnant_woman_black_48dp" id="0x7f080330" />
    <public type="drawable" name="ic_present_to_all_black_48dp" id="0x7f080331" />
    <public type="drawable" name="ic_print_black_48dp" id="0x7f080332" />
    <public type="drawable" name="ic_priority_high_black_48dp" id="0x7f080333" />
    <public type="drawable" name="ic_public_black_48dp" id="0x7f080334" />
    <public type="drawable" name="ic_publish_black_48dp" id="0x7f080335" />
    <public type="drawable" name="ic_query_builder_black_48dp" id="0x7f080336" />
    <public type="drawable" name="ic_question_answer_black_48dp" id="0x7f080337" />
    <public type="drawable" name="ic_queue_black_48dp" id="0x7f080338" />
    <public type="drawable" name="ic_queue_music_black_48dp" id="0x7f080339" />
    <public type="drawable" name="ic_queue_play_next_black_48dp" id="0x7f08033a" />
    <public type="drawable" name="ic_radio_black_48dp" id="0x7f08033b" />
    <public type="drawable" name="ic_rate_review_black_48dp" id="0x7f08033c" />
    <public type="drawable" name="ic_receipt_black_48dp" id="0x7f08033d" />
    <public type="drawable" name="ic_recent_actors_black_48dp" id="0x7f08033e" />
    <public type="drawable" name="ic_record_voice_over_black_48dp" id="0x7f08033f" />
    <public type="drawable" name="ic_redeem_black_48dp" id="0x7f080340" />
    <public type="drawable" name="ic_redo_black_48dp" id="0x7f080341" />
    <public type="drawable" name="ic_refresh_black_48dp" id="0x7f080342" />
    <public type="drawable" name="ic_remove_black_48dp" id="0x7f080343" />
    <public type="drawable" name="ic_remove_circle_black_48dp" id="0x7f080344" />
    <public type="drawable" name="ic_remove_circle_outline_black_48dp" id="0x7f080345" />
    <public type="drawable" name="ic_remove_circle_outline_white_48dp" id="0x7f080346" />
    <public type="drawable" name="ic_remove_from_queue_black_48dp" id="0x7f080347" />
    <public type="drawable" name="ic_remove_red_eye_black_48dp" id="0x7f080348" />
    <public type="drawable" name="ic_remove_shopping_cart_black_48dp" id="0x7f080349" />
    <public type="drawable" name="ic_remove_shopping_cart_white_48dp" id="0x7f08034a" />
    <public type="drawable" name="ic_remove_white_24dp" id="0x7f08034b" />
    <public type="drawable" name="ic_remove_white_48dp" id="0x7f08034c" />
    <public type="drawable" name="ic_reorder_black_48dp" id="0x7f08034d" />
    <public type="drawable" name="ic_repeat_black_48dp" id="0x7f08034e" />
    <public type="drawable" name="ic_repeat_one_black_48dp" id="0x7f08034f" />
    <public type="drawable" name="ic_replay_10_black_48dp" id="0x7f080350" />
    <public type="drawable" name="ic_replay_30_black_48dp" id="0x7f080351" />
    <public type="drawable" name="ic_replay_5_black_48dp" id="0x7f080352" />
    <public type="drawable" name="ic_replay_black_48dp" id="0x7f080353" />
    <public type="drawable" name="ic_reply_all_black_48dp" id="0x7f080354" />
    <public type="drawable" name="ic_reply_black_48dp" id="0x7f080355" />
    <public type="drawable" name="ic_report_black_48dp" id="0x7f080356" />
    <public type="drawable" name="ic_report_problem_black_48dp" id="0x7f080357" />
    <public type="drawable" name="ic_resizer" id="0x7f080358" />
    <public type="drawable" name="ic_restaurant_black_48dp" id="0x7f080359" />
    <public type="drawable" name="ic_restaurant_menu_black_48dp" id="0x7f08035a" />
    <public type="drawable" name="ic_restore_black_48dp" id="0x7f08035b" />
    <public type="drawable" name="ic_restore_page_black_48dp" id="0x7f08035c" />
    <public type="drawable" name="ic_ring_volume_black_48dp" id="0x7f08035d" />
    <public type="drawable" name="ic_room_black_48dp" id="0x7f08035e" />
    <public type="drawable" name="ic_room_service_black_48dp" id="0x7f08035f" />
    <public type="drawable" name="ic_rotate_90_degrees_ccw_black_48dp" id="0x7f080360" />
    <public type="drawable" name="ic_rotate_left_black_48dp" id="0x7f080361" />
    <public type="drawable" name="ic_rotate_right_black_48dp" id="0x7f080362" />
    <public type="drawable" name="ic_rounded_corner_black_48dp" id="0x7f080363" />
    <public type="drawable" name="ic_router_black_48dp" id="0x7f080364" />
    <public type="drawable" name="ic_rowing_black_48dp" id="0x7f080365" />
    <public type="drawable" name="ic_rss_feed_black_48dp" id="0x7f080366" />
    <public type="drawable" name="ic_rv_hookup_black_48dp" id="0x7f080367" />
    <public type="drawable" name="ic_satellite_black_48dp" id="0x7f080368" />
    <public type="drawable" name="ic_save_black_48dp" id="0x7f080369" />
    <public type="drawable" name="ic_scanner_black_48dp" id="0x7f08036a" />
    <public type="drawable" name="ic_schedule_black_48dp" id="0x7f08036b" />
    <public type="drawable" name="ic_school_black_48dp" id="0x7f08036c" />
    <public type="drawable" name="ic_screen_lock_landscape_black_48dp" id="0x7f08036d" />
    <public type="drawable" name="ic_screen_lock_portrait_black_48dp" id="0x7f08036e" />
    <public type="drawable" name="ic_screen_lock_rotation_black_48dp" id="0x7f08036f" />
    <public type="drawable" name="ic_screen_rotation_black_48dp" id="0x7f080370" />
    <public type="drawable" name="ic_screen_share_black_48dp" id="0x7f080371" />
    <public type="drawable" name="ic_sd_card_black_48dp" id="0x7f080372" />
    <public type="drawable" name="ic_sd_storage_black_48dp" id="0x7f080373" />
    <public type="drawable" name="ic_search_black_48dp" id="0x7f080374" />
    <public type="drawable" name="ic_security_black_48dp" id="0x7f080375" />
    <public type="drawable" name="ic_select_all_black_48dp" id="0x7f080376" />
    <public type="drawable" name="ic_send_black_48dp" id="0x7f080377" />
    <public type="drawable" name="ic_sentiment_dissatisfied_black_48dp" id="0x7f080378" />
    <public type="drawable" name="ic_sentiment_neutral_black_48dp" id="0x7f080379" />
    <public type="drawable" name="ic_sentiment_satisfied_black_48dp" id="0x7f08037a" />
    <public type="drawable" name="ic_sentiment_very_dissatisfied_black_48dp" id="0x7f08037b" />
    <public type="drawable" name="ic_sentiment_very_satisfied_black_48dp" id="0x7f08037c" />
    <public type="drawable" name="ic_settings_applications_black_48dp" id="0x7f08037d" />
    <public type="drawable" name="ic_settings_backup_restore_black_48dp" id="0x7f08037e" />
    <public type="drawable" name="ic_settings_black_48dp" id="0x7f08037f" />
    <public type="drawable" name="ic_settings_bluetooth_black_48dp" id="0x7f080380" />
    <public type="drawable" name="ic_settings_brightness_black_48dp" id="0x7f080381" />
    <public type="drawable" name="ic_settings_cell_black_48dp" id="0x7f080382" />
    <public type="drawable" name="ic_settings_ethernet_black_48dp" id="0x7f080383" />
    <public type="drawable" name="ic_settings_ethernet_white_24dp" id="0x7f080384" />
    <public type="drawable" name="ic_settings_ethernet_white_48dp" id="0x7f080385" />
    <public type="drawable" name="ic_settings_input_antenna_black_48dp" id="0x7f080386" />
    <public type="drawable" name="ic_settings_input_component_black_48dp" id="0x7f080387" />
    <public type="drawable" name="ic_settings_input_composite_black_48dp" id="0x7f080388" />
    <public type="drawable" name="ic_settings_input_hdmi_black_48dp" id="0x7f080389" />
    <public type="drawable" name="ic_settings_input_svideo_black_48dp" id="0x7f08038a" />
    <public type="drawable" name="ic_settings_overscan_black_48dp" id="0x7f08038b" />
    <public type="drawable" name="ic_settings_phone_black_48dp" id="0x7f08038c" />
    <public type="drawable" name="ic_settings_power_black_48dp" id="0x7f08038d" />
    <public type="drawable" name="ic_settings_remote_black_48dp" id="0x7f08038e" />
    <public type="drawable" name="ic_settings_system_daydream_black_48dp" id="0x7f08038f" />
    <public type="drawable" name="ic_settings_voice_black_48dp" id="0x7f080390" />
    <public type="drawable" name="ic_share_black_48dp" id="0x7f080391" />
    <public type="drawable" name="ic_shop_black_48dp" id="0x7f080392" />
    <public type="drawable" name="ic_shop_two_black_48dp" id="0x7f080393" />
    <public type="drawable" name="ic_shopping_basket_black_48dp" id="0x7f080394" />
    <public type="drawable" name="ic_shopping_cart_black_48dp" id="0x7f080395" />
    <public type="drawable" name="ic_short_text_black_48dp" id="0x7f080396" />
    <public type="drawable" name="ic_show_chart_black_48dp" id="0x7f080397" />
    <public type="drawable" name="ic_shuffle_black_48dp" id="0x7f080398" />
    <public type="drawable" name="ic_signal_cellular_0_bar_black_48dp" id="0x7f080399" />
    <public type="drawable" name="ic_signal_cellular_1_bar_black_48dp" id="0x7f08039a" />
    <public type="drawable" name="ic_signal_cellular_2_bar_black_48dp" id="0x7f08039b" />
    <public type="drawable" name="ic_signal_cellular_3_bar_black_48dp" id="0x7f08039c" />
    <public type="drawable" name="ic_signal_cellular_4_bar_black_48dp" id="0x7f08039d" />
    <public type="drawable" name="ic_signal_cellular_connected_no_internet_0_bar_black_48dp" id="0x7f08039e" />
    <public type="drawable" name="ic_signal_cellular_connected_no_internet_1_bar_black_48dp" id="0x7f08039f" />
    <public type="drawable" name="ic_signal_cellular_connected_no_internet_2_bar_black_48dp" id="0x7f0803a0" />
    <public type="drawable" name="ic_signal_cellular_connected_no_internet_3_bar_black_48dp" id="0x7f0803a1" />
    <public type="drawable" name="ic_signal_cellular_connected_no_internet_4_bar_black_48dp" id="0x7f0803a2" />
    <public type="drawable" name="ic_signal_cellular_no_sim_black_48dp" id="0x7f0803a3" />
    <public type="drawable" name="ic_signal_cellular_null_black_48dp" id="0x7f0803a4" />
    <public type="drawable" name="ic_signal_cellular_off_black_48dp" id="0x7f0803a5" />
    <public type="drawable" name="ic_signal_wifi_0_bar_black_48dp" id="0x7f0803a6" />
    <public type="drawable" name="ic_signal_wifi_1_bar_black_48dp" id="0x7f0803a7" />
    <public type="drawable" name="ic_signal_wifi_1_bar_lock_black_48dp" id="0x7f0803a8" />
    <public type="drawable" name="ic_signal_wifi_2_bar_black_48dp" id="0x7f0803a9" />
    <public type="drawable" name="ic_signal_wifi_2_bar_lock_black_48dp" id="0x7f0803aa" />
    <public type="drawable" name="ic_signal_wifi_3_bar_black_48dp" id="0x7f0803ab" />
    <public type="drawable" name="ic_signal_wifi_3_bar_lock_black_48dp" id="0x7f0803ac" />
    <public type="drawable" name="ic_signal_wifi_4_bar_black_48dp" id="0x7f0803ad" />
    <public type="drawable" name="ic_signal_wifi_4_bar_lock_black_48dp" id="0x7f0803ae" />
    <public type="drawable" name="ic_signal_wifi_off_black_48dp" id="0x7f0803af" />
    <public type="drawable" name="ic_sim_card_alert_black_48dp" id="0x7f0803b0" />
    <public type="drawable" name="ic_sim_card_black_48dp" id="0x7f0803b1" />
    <public type="drawable" name="ic_skip_next_black_48dp" id="0x7f0803b2" />
    <public type="drawable" name="ic_skip_previous_black_48dp" id="0x7f0803b3" />
    <public type="drawable" name="ic_slideshow_black_48dp" id="0x7f0803b4" />
    <public type="drawable" name="ic_slow_motion_video_black_48dp" id="0x7f0803b5" />
    <public type="drawable" name="ic_smartphone_black_48dp" id="0x7f0803b6" />
    <public type="drawable" name="ic_smoke_free_black_48dp" id="0x7f0803b7" />
    <public type="drawable" name="ic_smoking_rooms_black_48dp" id="0x7f0803b8" />
    <public type="drawable" name="ic_sms_black_48dp" id="0x7f0803b9" />
    <public type="drawable" name="ic_sms_failed_black_48dp" id="0x7f0803ba" />
    <public type="drawable" name="ic_snooze_black_48dp" id="0x7f0803bb" />
    <public type="drawable" name="ic_sort_black_48dp" id="0x7f0803bc" />
    <public type="drawable" name="ic_sort_by_alpha_black_48dp" id="0x7f0803bd" />
    <public type="drawable" name="ic_spa_black_48dp" id="0x7f0803be" />
    <public type="drawable" name="ic_space_bar_black_48dp" id="0x7f0803bf" />
    <public type="drawable" name="ic_speaker_black_48dp" id="0x7f0803c0" />
    <public type="drawable" name="ic_speaker_group_black_48dp" id="0x7f0803c1" />
    <public type="drawable" name="ic_speaker_notes_black_48dp" id="0x7f0803c2" />
    <public type="drawable" name="ic_speaker_notes_off_black_48dp" id="0x7f0803c3" />
    <public type="drawable" name="ic_speaker_phone_black_48dp" id="0x7f0803c4" />
    <public type="drawable" name="ic_spellcheck_black_48dp" id="0x7f0803c5" />
    <public type="drawable" name="ic_star_black_48dp" id="0x7f0803c6" />
    <public type="drawable" name="ic_star_border_black_48dp" id="0x7f0803c7" />
    <public type="drawable" name="ic_star_half_black_48dp" id="0x7f0803c8" />
    <public type="drawable" name="ic_stars_black_48dp" id="0x7f0803c9" />
    <public type="drawable" name="ic_stay_current_landscape_black_48dp" id="0x7f0803ca" />
    <public type="drawable" name="ic_stay_current_portrait_black_48dp" id="0x7f0803cb" />
    <public type="drawable" name="ic_stay_primary_landscape_black_48dp" id="0x7f0803cc" />
    <public type="drawable" name="ic_stay_primary_portrait_black_48dp" id="0x7f0803cd" />
    <public type="drawable" name="ic_stop_black_48dp" id="0x7f0803ce" />
    <public type="drawable" name="ic_stop_screen_share_black_48dp" id="0x7f0803cf" />
    <public type="drawable" name="ic_storage_black_48dp" id="0x7f0803d0" />
    <public type="drawable" name="ic_store_black_48dp" id="0x7f0803d1" />
    <public type="drawable" name="ic_store_mall_directory_black_48dp" id="0x7f0803d2" />
    <public type="drawable" name="ic_straighten_black_48dp" id="0x7f0803d3" />
    <public type="drawable" name="ic_streetview_black_48dp" id="0x7f0803d4" />
    <public type="drawable" name="ic_strikethrough_s_black_48dp" id="0x7f0803d5" />
    <public type="drawable" name="ic_style_black_48dp" id="0x7f0803d6" />
    <public type="drawable" name="ic_subdirectory_arrow_left_black_48dp" id="0x7f0803d7" />
    <public type="drawable" name="ic_subdirectory_arrow_right_black_48dp" id="0x7f0803d8" />
    <public type="drawable" name="ic_subject_black_48dp" id="0x7f0803d9" />
    <public type="drawable" name="ic_subscriptions_black_48dp" id="0x7f0803da" />
    <public type="drawable" name="ic_subtitles_black_48dp" id="0x7f0803db" />
    <public type="drawable" name="ic_subway_black_48dp" id="0x7f0803dc" />
    <public type="drawable" name="ic_supervisor_account_black_48dp" id="0x7f0803dd" />
    <public type="drawable" name="ic_surround_sound_black_48dp" id="0x7f0803de" />
    <public type="drawable" name="ic_swap_calls_black_48dp" id="0x7f0803df" />
    <public type="drawable" name="ic_swap_horiz_black_48dp" id="0x7f0803e0" />
    <public type="drawable" name="ic_swap_vert_black_48dp" id="0x7f0803e1" />
    <public type="drawable" name="ic_swap_vertical_circle_black_48dp" id="0x7f0803e2" />
    <public type="drawable" name="ic_switch_camera_black_48dp" id="0x7f0803e3" />
    <public type="drawable" name="ic_switch_video_black_48dp" id="0x7f0803e4" />
    <public type="drawable" name="ic_sync_black_48dp" id="0x7f0803e5" />
    <public type="drawable" name="ic_sync_disabled_black_48dp" id="0x7f0803e6" />
    <public type="drawable" name="ic_sync_problem_black_48dp" id="0x7f0803e7" />
    <public type="drawable" name="ic_system_update_alt_black_48dp" id="0x7f0803e8" />
    <public type="drawable" name="ic_system_update_black_48dp" id="0x7f0803e9" />
    <public type="drawable" name="ic_tab_black_48dp" id="0x7f0803ea" />
    <public type="drawable" name="ic_tab_unselected_black_48dp" id="0x7f0803eb" />
    <public type="drawable" name="ic_tablet_android_black_48dp" id="0x7f0803ec" />
    <public type="drawable" name="ic_tablet_black_48dp" id="0x7f0803ed" />
    <public type="drawable" name="ic_tablet_mac_black_48dp" id="0x7f0803ee" />
    <public type="drawable" name="ic_tag_faces_black_48dp" id="0x7f0803ef" />
    <public type="drawable" name="ic_tap_and_play_black_48dp" id="0x7f0803f0" />
    <public type="drawable" name="ic_terrain_black_48dp" id="0x7f0803f1" />
    <public type="drawable" name="ic_text_fields_black_48dp" id="0x7f0803f2" />
    <public type="drawable" name="ic_text_format_black_48dp" id="0x7f0803f3" />
    <public type="drawable" name="ic_textsms_black_48dp" id="0x7f0803f4" />
    <public type="drawable" name="ic_texture_black_48dp" id="0x7f0803f5" />
    <public type="drawable" name="ic_theaters_black_48dp" id="0x7f0803f6" />
    <public type="drawable" name="ic_thumb_down_black_48dp" id="0x7f0803f7" />
    <public type="drawable" name="ic_thumb_up_black_48dp" id="0x7f0803f8" />
    <public type="drawable" name="ic_thumbs_up_down_black_48dp" id="0x7f0803f9" />
    <public type="drawable" name="ic_time_to_leave_black_48dp" id="0x7f0803fa" />
    <public type="drawable" name="ic_timelapse_black_48dp" id="0x7f0803fb" />
    <public type="drawable" name="ic_timeline_black_48dp" id="0x7f0803fc" />
    <public type="drawable" name="ic_timer_10_black_48dp" id="0x7f0803fd" />
    <public type="drawable" name="ic_timer_3_black_48dp" id="0x7f0803fe" />
    <public type="drawable" name="ic_timer_black_48dp" id="0x7f0803ff" />
    <public type="drawable" name="ic_timer_off_black_48dp" id="0x7f080400" />
    <public type="drawable" name="ic_title_black_48dp" id="0x7f080401" />
    <public type="drawable" name="ic_toc_black_48dp" id="0x7f080402" />
    <public type="drawable" name="ic_today_black_48dp" id="0x7f080403" />
    <public type="drawable" name="ic_toll_black_48dp" id="0x7f080404" />
    <public type="drawable" name="ic_tonality_black_48dp" id="0x7f080405" />
    <public type="drawable" name="ic_touch_app_black_48dp" id="0x7f080406" />
    <public type="drawable" name="ic_toys_black_48dp" id="0x7f080407" />
    <public type="drawable" name="ic_track_changes_black_48dp" id="0x7f080408" />
    <public type="drawable" name="ic_traffic_black_48dp" id="0x7f080409" />
    <public type="drawable" name="ic_train_black_48dp" id="0x7f08040a" />
    <public type="drawable" name="ic_tram_black_48dp" id="0x7f08040b" />
    <public type="drawable" name="ic_transfer_within_a_station_black_48dp" id="0x7f08040c" />
    <public type="drawable" name="ic_transform_black_48dp" id="0x7f08040d" />
    <public type="drawable" name="ic_translate_black_48dp" id="0x7f08040e" />
    <public type="drawable" name="ic_trending_down_black_48dp" id="0x7f08040f" />
    <public type="drawable" name="ic_trending_flat_black_48dp" id="0x7f080410" />
    <public type="drawable" name="ic_trending_up_black_48dp" id="0x7f080411" />
    <public type="drawable" name="ic_tune_black_48dp" id="0x7f080412" />
    <public type="drawable" name="ic_turned_in_black_48dp" id="0x7f080413" />
    <public type="drawable" name="ic_turned_in_not_black_48dp" id="0x7f080414" />
    <public type="drawable" name="ic_tv_black_48dp" id="0x7f080415" />
    <public type="drawable" name="ic_unarchive_black_48dp" id="0x7f080416" />
    <public type="drawable" name="ic_undo_black_48dp" id="0x7f080417" />
    <public type="drawable" name="ic_unfold_less_black_48dp" id="0x7f080418" />
    <public type="drawable" name="ic_unfold_more_black_48dp" id="0x7f080419" />
    <public type="drawable" name="ic_update_black_48dp" id="0x7f08041a" />
    <public type="drawable" name="ic_usb_black_48dp" id="0x7f08041b" />
    <public type="drawable" name="ic_verified_user_black_48dp" id="0x7f08041c" />
    <public type="drawable" name="ic_vertical_align_bottom_black_48dp" id="0x7f08041d" />
    <public type="drawable" name="ic_vertical_align_center_black_48dp" id="0x7f08041e" />
    <public type="drawable" name="ic_vertical_align_top_black_48dp" id="0x7f08041f" />
    <public type="drawable" name="ic_vibration_black_48dp" id="0x7f080420" />
    <public type="drawable" name="ic_video_call_black_48dp" id="0x7f080421" />
    <public type="drawable" name="ic_video_label_black_48dp" id="0x7f080422" />
    <public type="drawable" name="ic_video_library_black_48dp" id="0x7f080423" />
    <public type="drawable" name="ic_videocam_black_48dp" id="0x7f080424" />
    <public type="drawable" name="ic_videocam_off_black_48dp" id="0x7f080425" />
    <public type="drawable" name="ic_videogame_asset_black_48dp" id="0x7f080426" />
    <public type="drawable" name="ic_view_agenda_black_48dp" id="0x7f080427" />
    <public type="drawable" name="ic_view_array_black_48dp" id="0x7f080428" />
    <public type="drawable" name="ic_view_carousel_black_48dp" id="0x7f080429" />
    <public type="drawable" name="ic_view_column_black_48dp" id="0x7f08042a" />
    <public type="drawable" name="ic_view_comfy_black_48dp" id="0x7f08042b" />
    <public type="drawable" name="ic_view_compact_black_48dp" id="0x7f08042c" />
    <public type="drawable" name="ic_view_day_black_48dp" id="0x7f08042d" />
    <public type="drawable" name="ic_view_headline_black_48dp" id="0x7f08042e" />
    <public type="drawable" name="ic_view_list_black_48dp" id="0x7f08042f" />
    <public type="drawable" name="ic_view_module_black_48dp" id="0x7f080430" />
    <public type="drawable" name="ic_view_quilt_black_48dp" id="0x7f080431" />
    <public type="drawable" name="ic_view_stream_black_48dp" id="0x7f080432" />
    <public type="drawable" name="ic_view_week_black_48dp" id="0x7f080433" />
    <public type="drawable" name="ic_vignette_black_48dp" id="0x7f080434" />
    <public type="drawable" name="ic_visibility_black_48dp" id="0x7f080435" />
    <public type="drawable" name="ic_visibility_off_black_48dp" id="0x7f080436" />
    <public type="drawable" name="ic_voice_chat_black_48dp" id="0x7f080437" />
    <public type="drawable" name="ic_voicemail_black_48dp" id="0x7f080438" />
    <public type="drawable" name="ic_volume_down_black_48dp" id="0x7f080439" />
    <public type="drawable" name="ic_volume_mute_black_48dp" id="0x7f08043a" />
    <public type="drawable" name="ic_volume_off_black_48dp" id="0x7f08043b" />
    <public type="drawable" name="ic_volume_up_black_48dp" id="0x7f08043c" />
    <public type="drawable" name="ic_vpn_key_black_48dp" id="0x7f08043d" />
    <public type="drawable" name="ic_vpn_lock_black_48dp" id="0x7f08043e" />
    <public type="drawable" name="ic_wallpaper_black_48dp" id="0x7f08043f" />
    <public type="drawable" name="ic_warning_black_48dp" id="0x7f080440" />
    <public type="drawable" name="ic_watch_black_48dp" id="0x7f080441" />
    <public type="drawable" name="ic_watch_later_black_48dp" id="0x7f080442" />
    <public type="drawable" name="ic_wb_auto_black_48dp" id="0x7f080443" />
    <public type="drawable" name="ic_wb_cloudy_black_48dp" id="0x7f080444" />
    <public type="drawable" name="ic_wb_incandescent_black_48dp" id="0x7f080445" />
    <public type="drawable" name="ic_wb_iridescent_black_48dp" id="0x7f080446" />
    <public type="drawable" name="ic_wb_sunny_black_48dp" id="0x7f080447" />
    <public type="drawable" name="ic_wc_black_48dp" id="0x7f080448" />
    <public type="drawable" name="ic_web_asset_black_48dp" id="0x7f080449" />
    <public type="drawable" name="ic_web_black_48dp" id="0x7f08044a" />
    <public type="drawable" name="ic_weekend_black_48dp" id="0x7f08044b" />
    <public type="drawable" name="ic_whatshot_black_48dp" id="0x7f08044c" />
    <public type="drawable" name="ic_widgets_black_48dp" id="0x7f08044d" />
    <public type="drawable" name="ic_wifi_black_48dp" id="0x7f08044e" />
    <public type="drawable" name="ic_wifi_lock_black_48dp" id="0x7f08044f" />
    <public type="drawable" name="ic_wifi_tethering_black_48dp" id="0x7f080450" />
    <public type="drawable" name="ic_work_black_48dp" id="0x7f080451" />
    <public type="drawable" name="ic_wrap_text_black_48dp" id="0x7f080452" />
    <public type="drawable" name="ic_youtube_searched_for_black_48dp" id="0x7f080453" />
    <public type="drawable" name="ic_zoom_in_black_48dp" id="0x7f080454" />
    <public type="drawable" name="ic_zoom_out_black_48dp" id="0x7f080455" />
    <public type="drawable" name="ic_zoom_out_map_black_48dp" id="0x7f080456" />
    <public type="drawable" name="m3_appbar_background" id="0x7f080457" />
    <public type="drawable" name="m3_popupmenu_background_overlay" id="0x7f080458" />
    <public type="drawable" name="m3_radiobutton_ripple" id="0x7f080459" />
    <public type="drawable" name="m3_selection_control_ripple" id="0x7f08045a" />
    <public type="drawable" name="m3_tabs_background" id="0x7f08045b" />
    <public type="drawable" name="m3_tabs_line_indicator" id="0x7f08045c" />
    <public type="drawable" name="m3_tabs_rounded_line_indicator" id="0x7f08045d" />
    <public type="drawable" name="m3_tabs_transparent_background" id="0x7f08045e" />
    <public type="drawable" name="material_cursor_drawable" id="0x7f08045f" />
    <public type="drawable" name="material_ic_calendar_black_24dp" id="0x7f080460" />
    <public type="drawable" name="material_ic_clear_black_24dp" id="0x7f080461" />
    <public type="drawable" name="material_ic_edit_black_24dp" id="0x7f080462" />
    <public type="drawable" name="material_ic_keyboard_arrow_left_black_24dp" id="0x7f080463" />
    <public type="drawable" name="material_ic_keyboard_arrow_next_black_24dp" id="0x7f080464" />
    <public type="drawable" name="material_ic_keyboard_arrow_previous_black_24dp" id="0x7f080465" />
    <public type="drawable" name="material_ic_keyboard_arrow_right_black_24dp" id="0x7f080466" />
    <public type="drawable" name="material_ic_menu_arrow_down_black_24dp" id="0x7f080467" />
    <public type="drawable" name="material_ic_menu_arrow_up_black_24dp" id="0x7f080468" />
    <public type="drawable" name="md_btn_selected" id="0x7f080469" />
    <public type="drawable" name="md_btn_selected_dark" id="0x7f08046a" />
    <public type="drawable" name="md_btn_selector" id="0x7f08046b" />
    <public type="drawable" name="md_btn_selector_dark" id="0x7f08046c" />
    <public type="drawable" name="md_btn_selector_ripple" id="0x7f08046d" />
    <public type="drawable" name="md_btn_selector_ripple_dark" id="0x7f08046e" />
    <public type="drawable" name="md_btn_shape" id="0x7f08046f" />
    <public type="drawable" name="md_item_selected" id="0x7f080470" />
    <public type="drawable" name="md_item_selected_dark" id="0x7f080471" />
    <public type="drawable" name="md_nav_back" id="0x7f080472" />
    <public type="drawable" name="md_selector" id="0x7f080473" />
    <public type="drawable" name="md_selector_dark" id="0x7f080474" />
    <public type="drawable" name="md_transparent" id="0x7f080475" />
    <public type="drawable" name="mtrl_bottomsheet_drag_handle" id="0x7f080476" />
    <public type="drawable" name="mtrl_checkbox_button" id="0x7f080477" />
    <public type="drawable" name="mtrl_checkbox_button_checked_unchecked" id="0x7f080478" />
    <public type="drawable" name="mtrl_checkbox_button_icon" id="0x7f080479" />
    <public type="drawable" name="mtrl_checkbox_button_icon_checked_indeterminate" id="0x7f08047a" />
    <public type="drawable" name="mtrl_checkbox_button_icon_checked_unchecked" id="0x7f08047b" />
    <public type="drawable" name="mtrl_checkbox_button_icon_indeterminate_checked" id="0x7f08047c" />
    <public type="drawable" name="mtrl_checkbox_button_icon_indeterminate_unchecked" id="0x7f08047d" />
    <public type="drawable" name="mtrl_checkbox_button_icon_unchecked_checked" id="0x7f08047e" />
    <public type="drawable" name="mtrl_checkbox_button_icon_unchecked_indeterminate" id="0x7f08047f" />
    <public type="drawable" name="mtrl_checkbox_button_unchecked_checked" id="0x7f080480" />
    <public type="drawable" name="mtrl_dialog_background" id="0x7f080481" />
    <public type="drawable" name="mtrl_dropdown_arrow" id="0x7f080482" />
    <public type="drawable" name="mtrl_ic_arrow_drop_down" id="0x7f080483" />
    <public type="drawable" name="mtrl_ic_arrow_drop_up" id="0x7f080484" />
    <public type="drawable" name="mtrl_ic_cancel" id="0x7f080485" />
    <public type="drawable" name="mtrl_ic_check_mark" id="0x7f080486" />
    <public type="drawable" name="mtrl_ic_checkbox_checked" id="0x7f080487" />
    <public type="drawable" name="mtrl_ic_checkbox_unchecked" id="0x7f080488" />
    <public type="drawable" name="mtrl_ic_error" id="0x7f080489" />
    <public type="drawable" name="mtrl_ic_indeterminate" id="0x7f08048a" />
    <public type="drawable" name="mtrl_navigation_bar_item_background" id="0x7f08048b" />
    <public type="drawable" name="mtrl_popupmenu_background" id="0x7f08048c" />
    <public type="drawable" name="mtrl_popupmenu_background_overlay" id="0x7f08048d" />
    <public type="drawable" name="mtrl_switch_thumb" id="0x7f08048e" />
    <public type="drawable" name="mtrl_switch_thumb_checked" id="0x7f08048f" />
    <public type="drawable" name="mtrl_switch_thumb_checked_pressed" id="0x7f080490" />
    <public type="drawable" name="mtrl_switch_thumb_checked_unchecked" id="0x7f080491" />
    <public type="drawable" name="mtrl_switch_thumb_pressed" id="0x7f080492" />
    <public type="drawable" name="mtrl_switch_thumb_pressed_checked" id="0x7f080493" />
    <public type="drawable" name="mtrl_switch_thumb_pressed_unchecked" id="0x7f080494" />
    <public type="drawable" name="mtrl_switch_thumb_unchecked" id="0x7f080495" />
    <public type="drawable" name="mtrl_switch_thumb_unchecked_checked" id="0x7f080496" />
    <public type="drawable" name="mtrl_switch_thumb_unchecked_pressed" id="0x7f080497" />
    <public type="drawable" name="mtrl_switch_track" id="0x7f080498" />
    <public type="drawable" name="mtrl_switch_track_decoration" id="0x7f080499" />
    <public type="drawable" name="mtrl_tabs_default_indicator" id="0x7f08049a" />
    <public type="drawable" name="navigation_empty_icon" id="0x7f08049b" />
    <public type="drawable" name="notification_action_background" id="0x7f08049c" />
    <public type="drawable" name="notification_bg" id="0x7f08049d" />
    <public type="drawable" name="notification_bg_low" id="0x7f08049e" />
    <public type="drawable" name="notification_bg_low_normal" id="0x7f08049f" />
    <public type="drawable" name="notification_bg_low_pressed" id="0x7f0804a0" />
    <public type="drawable" name="notification_bg_normal" id="0x7f0804a1" />
    <public type="drawable" name="notification_bg_normal_pressed" id="0x7f0804a2" />
    <public type="drawable" name="notification_icon_background" id="0x7f0804a3" />
    <public type="drawable" name="notification_template_icon_bg" id="0x7f0804a4" />
    <public type="drawable" name="notification_template_icon_low_bg" id="0x7f0804a5" />
    <public type="drawable" name="notification_tile_bg" id="0x7f0804a6" />
    <public type="drawable" name="notify_panel_notification_icon_bg" id="0x7f0804a7" />
    <public type="drawable" name="round_article_20" id="0x7f0804a8" />
    <public type="drawable" name="round_close_20" id="0x7f0804a9" />
    <public type="drawable" name="round_delete_outline_24" id="0x7f0804aa" />
    <public type="drawable" name="round_edit_20" id="0x7f0804ab" />
    <public type="drawable" name="round_open_in_new_24" id="0x7f0804ac" />
    <public type="drawable" name="round_play_arrow_20" id="0x7f0804ad" />
    <public type="drawable" name="round_search_24" id="0x7f0804ae" />
    <public type="drawable" name="round_stop_20" id="0x7f0804af" />
    <public type="drawable" name="splash_icon" id="0x7f0804b0" />
    <public type="drawable" name="test_level_drawable" id="0x7f0804b1" />
    <public type="drawable" name="tooltip_frame_dark" id="0x7f0804b2" />
    <public type="drawable" name="tooltip_frame_light" id="0x7f0804b3" />
    <public type="id" name="ALT" id="0x7f090000" />
    <public type="id" name="BOTTOM_END" id="0x7f090001" />
    <public type="id" name="BOTTOM_START" id="0x7f090002" />
    <public type="id" name="CTRL" id="0x7f090003" />
    <public type="id" name="FUNCTION" id="0x7f090004" />
    <public type="id" name="META" id="0x7f090005" />
    <public type="id" name="NO_DEBUG" id="0x7f090006" />
    <public type="id" name="SHIFT" id="0x7f090007" />
    <public type="id" name="SHOW_ALL" id="0x7f090008" />
    <public type="id" name="SHOW_PATH" id="0x7f090009" />
    <public type="id" name="SHOW_PROGRESS" id="0x7f09000a" />
    <public type="id" name="SYM" id="0x7f09000b" />
    <public type="id" name="TOP_END" id="0x7f09000c" />
    <public type="id" name="TOP_START" id="0x7f09000d" />
    <public type="id" name="accelerate" id="0x7f09000e" />
    <public type="id" name="accessibility_action_clickable_span" id="0x7f09000f" />
    <public type="id" name="accessibility_custom_action_0" id="0x7f090010" />
    <public type="id" name="accessibility_custom_action_1" id="0x7f090011" />
    <public type="id" name="accessibility_custom_action_10" id="0x7f090012" />
    <public type="id" name="accessibility_custom_action_11" id="0x7f090013" />
    <public type="id" name="accessibility_custom_action_12" id="0x7f090014" />
    <public type="id" name="accessibility_custom_action_13" id="0x7f090015" />
    <public type="id" name="accessibility_custom_action_14" id="0x7f090016" />
    <public type="id" name="accessibility_custom_action_15" id="0x7f090017" />
    <public type="id" name="accessibility_custom_action_16" id="0x7f090018" />
    <public type="id" name="accessibility_custom_action_17" id="0x7f090019" />
    <public type="id" name="accessibility_custom_action_18" id="0x7f09001a" />
    <public type="id" name="accessibility_custom_action_19" id="0x7f09001b" />
    <public type="id" name="accessibility_custom_action_2" id="0x7f09001c" />
    <public type="id" name="accessibility_custom_action_20" id="0x7f09001d" />
    <public type="id" name="accessibility_custom_action_21" id="0x7f09001e" />
    <public type="id" name="accessibility_custom_action_22" id="0x7f09001f" />
    <public type="id" name="accessibility_custom_action_23" id="0x7f090020" />
    <public type="id" name="accessibility_custom_action_24" id="0x7f090021" />
    <public type="id" name="accessibility_custom_action_25" id="0x7f090022" />
    <public type="id" name="accessibility_custom_action_26" id="0x7f090023" />
    <public type="id" name="accessibility_custom_action_27" id="0x7f090024" />
    <public type="id" name="accessibility_custom_action_28" id="0x7f090025" />
    <public type="id" name="accessibility_custom_action_29" id="0x7f090026" />
    <public type="id" name="accessibility_custom_action_3" id="0x7f090027" />
    <public type="id" name="accessibility_custom_action_30" id="0x7f090028" />
    <public type="id" name="accessibility_custom_action_31" id="0x7f090029" />
    <public type="id" name="accessibility_custom_action_4" id="0x7f09002a" />
    <public type="id" name="accessibility_custom_action_5" id="0x7f09002b" />
    <public type="id" name="accessibility_custom_action_6" id="0x7f09002c" />
    <public type="id" name="accessibility_custom_action_7" id="0x7f09002d" />
    <public type="id" name="accessibility_custom_action_8" id="0x7f09002e" />
    <public type="id" name="accessibility_custom_action_9" id="0x7f09002f" />
    <public type="id" name="action" id="0x7f090030" />
    <public type="id" name="actionDown" id="0x7f090031" />
    <public type="id" name="actionDownUp" id="0x7f090032" />
    <public type="id" name="actionUp" id="0x7f090033" />
    <public type="id" name="action_bar" id="0x7f090034" />
    <public type="id" name="action_bar_activity_content" id="0x7f090035" />
    <public type="id" name="action_bar_container" id="0x7f090036" />
    <public type="id" name="action_bar_root" id="0x7f090037" />
    <public type="id" name="action_bar_spinner" id="0x7f090038" />
    <public type="id" name="action_bar_subtitle" id="0x7f090039" />
    <public type="id" name="action_bar_title" id="0x7f09003a" />
    <public type="id" name="action_clear_log_file" id="0x7f09003b" />
    <public type="id" name="action_container" id="0x7f09003c" />
    <public type="id" name="action_context_bar" id="0x7f09003d" />
    <public type="id" name="action_divider" id="0x7f09003e" />
    <public type="id" name="action_image" id="0x7f09003f" />
    <public type="id" name="action_log" id="0x7f090040" />
    <public type="id" name="action_menu_divider" id="0x7f090041" />
    <public type="id" name="action_menu_presenter" id="0x7f090042" />
    <public type="id" name="action_mode_bar" id="0x7f090043" />
    <public type="id" name="action_mode_bar_stub" id="0x7f090044" />
    <public type="id" name="action_mode_close_button" id="0x7f090045" />
    <public type="id" name="action_open_by_other_apps" id="0x7f090046" />
    <public type="id" name="action_search" id="0x7f090047" />
    <public type="id" name="action_switch_log_level" id="0x7f090048" />
    <public type="id" name="action_text" id="0x7f090049" />
    <public type="id" name="actions" id="0x7f09004a" />
    <public type="id" name="activity_chooser_view_content" id="0x7f09004b" />
    <public type="id" name="add" id="0x7f09004c" />
    <public type="id" name="alertTitle" id="0x7f09004d" />
    <public type="id" name="aligned" id="0x7f09004e" />
    <public type="id" name="all" id="0x7f09004f" />
    <public type="id" name="allStates" id="0x7f090050" />
    <public type="id" name="always" id="0x7f090051" />
    <public type="id" name="animateToEnd" id="0x7f090052" />
    <public type="id" name="animateToStart" id="0x7f090053" />
    <public type="id" name="antiClockwise" id="0x7f090054" />
    <public type="id" name="anticipate" id="0x7f090055" />
    <public type="id" name="any" id="0x7f090056" />
    <public type="id" name="arc" id="0x7f090057" />
    <public type="id" name="asConfigured" id="0x7f090058" />
    <public type="id" name="async" id="0x7f090059" />
    <public type="id" name="auto" id="0x7f09005a" />
    <public type="id" name="autoComplete" id="0x7f09005b" />
    <public type="id" name="autoCompleteToEnd" id="0x7f09005c" />
    <public type="id" name="autoCompleteToStart" id="0x7f09005d" />
    <public type="id" name="back" id="0x7f09005e" />
    <public type="id" name="barrier" id="0x7f09005f" />
    <public type="id" name="baseline" id="0x7f090060" />
    <public type="id" name="beginOnFirstDraw" id="0x7f090061" />
    <public type="id" name="beginning" id="0x7f090062" />
    <public type="id" name="bestChoice" id="0x7f090063" />
    <public type="id" name="blocking" id="0x7f090064" />
    <public type="id" name="bottom" id="0x7f090065" />
    <public type="id" name="bounce" id="0x7f090066" />
    <public type="id" name="bounceBoth" id="0x7f090067" />
    <public type="id" name="bounceEnd" id="0x7f090068" />
    <public type="id" name="bounceStart" id="0x7f090069" />
    <public type="id" name="buttonContainer" id="0x7f09006a" />
    <public type="id" name="buttonPanel" id="0x7f09006b" />
    <public type="id" name="cache_measures" id="0x7f09006c" />
    <public type="id" name="callMeasure" id="0x7f09006d" />
    <public type="id" name="cancel_button" id="0x7f09006e" />
    <public type="id" name="carryVelocity" id="0x7f09006f" />
    <public type="id" name="center" id="0x7f090070" />
    <public type="id" name="centerCrop" id="0x7f090071" />
    <public type="id" name="centerInside" id="0x7f090072" />
    <public type="id" name="center_horizontal" id="0x7f090073" />
    <public type="id" name="center_vertical" id="0x7f090074" />
    <public type="id" name="chain" id="0x7f090075" />
    <public type="id" name="chain2" id="0x7f090076" />
    <public type="id" name="chains" id="0x7f090077" />
    <public type="id" name="checkbox" id="0x7f090078" />
    <public type="id" name="checked" id="0x7f090079" />
    <public type="id" name="chronometer" id="0x7f09007a" />
    <public type="id" name="circle_center" id="0x7f09007b" />
    <public type="id" name="circular" id="0x7f09007c" />
    <public type="id" name="clamp" id="0x7f09007d" />
    <public type="id" name="clear_text" id="0x7f09007e" />
    <public type="id" name="clip_horizontal" id="0x7f09007f" />
    <public type="id" name="clip_vertical" id="0x7f090080" />
    <public type="id" name="clockwise" id="0x7f090081" />
    <public type="id" name="close" id="0x7f090082" />
    <public type="id" name="closest" id="0x7f090083" />
    <public type="id" name="collapseActionView" id="0x7f090084" />
    <public type="id" name="compress" id="0x7f090085" />
    <public type="id" name="confirm_button" id="0x7f090086" />
    <public type="id" name="console" id="0x7f090087" />
    <public type="id" name="constraint" id="0x7f090088" />
    <public type="id" name="container" id="0x7f090089" />
    <public type="id" name="content" id="0x7f09008a" />
    <public type="id" name="contentPanel" id="0x7f09008b" />
    <public type="id" name="contiguous" id="0x7f09008c" />
    <public type="id" name="continuousVelocity" id="0x7f09008d" />
    <public type="id" name="coordinator" id="0x7f09008e" />
    <public type="id" name="copy" id="0x7f09008f" />
    <public type="id" name="cos" id="0x7f090090" />
    <public type="id" name="counterclockwise" id="0x7f090091" />
    <public type="id" name="cradle" id="0x7f090092" />
    <public type="id" name="currentState" id="0x7f090093" />
    <public type="id" name="custom" id="0x7f090094" />
    <public type="id" name="customPanel" id="0x7f090095" />
    <public type="id" name="cut" id="0x7f090096" />
    <public type="id" name="date_picker_actions" id="0x7f090097" />
    <public type="id" name="decelerate" id="0x7f090098" />
    <public type="id" name="decelerateAndComplete" id="0x7f090099" />
    <public type="id" name="decor_content_parent" id="0x7f09009a" />
    <public type="id" name="defaultSplash" id="0x7f09009b" />
    <public type="id" name="default_activity_button" id="0x7f09009c" />
    <public type="id" name="deltaRelative" id="0x7f09009d" />
    <public type="id" name="dependency_ordering" id="0x7f09009e" />
    <public type="id" name="design_bottom_sheet" id="0x7f09009f" />
    <public type="id" name="design_menu_item_action_area" id="0x7f0900a0" />
    <public type="id" name="design_menu_item_action_area_stub" id="0x7f0900a1" />
    <public type="id" name="design_menu_item_text" id="0x7f0900a2" />
    <public type="id" name="design_navigation_view" id="0x7f0900a3" />
    <public type="id" name="dialog_button" id="0x7f0900a4" />
    <public type="id" name="dimensions" id="0x7f0900a5" />
    <public type="id" name="direct" id="0x7f0900a6" />
    <public type="id" name="disableHome" id="0x7f0900a7" />
    <public type="id" name="disableIntraAutoTransition" id="0x7f0900a8" />
    <public type="id" name="disablePostScroll" id="0x7f0900a9" />
    <public type="id" name="disableScroll" id="0x7f0900aa" />
    <public type="id" name="disjoint" id="0x7f0900ab" />
    <public type="id" name="dragAnticlockwise" id="0x7f0900ac" />
    <public type="id" name="dragClockwise" id="0x7f0900ad" />
    <public type="id" name="dragDown" id="0x7f0900ae" />
    <public type="id" name="dragEnd" id="0x7f0900af" />
    <public type="id" name="dragLeft" id="0x7f0900b0" />
    <public type="id" name="dragRight" id="0x7f0900b1" />
    <public type="id" name="dragStart" id="0x7f0900b2" />
    <public type="id" name="dragUp" id="0x7f0900b3" />
    <public type="id" name="dropdown_menu" id="0x7f0900b4" />
    <public type="id" name="easeIn" id="0x7f0900b5" />
    <public type="id" name="easeInOut" id="0x7f0900b6" />
    <public type="id" name="easeOut" id="0x7f0900b7" />
    <public type="id" name="east" id="0x7f0900b8" />
    <public type="id" name="edit" id="0x7f0900b9" />
    <public type="id" name="edit_query" id="0x7f0900ba" />
    <public type="id" name="elastic" id="0x7f0900bb" />
    <public type="id" name="embed" id="0x7f0900bc" />
    <public type="id" name="end" id="0x7f0900bd" />
    <public type="id" name="endToStart" id="0x7f0900be" />
    <public type="id" name="enterAlways" id="0x7f0900bf" />
    <public type="id" name="enterAlwaysCollapsed" id="0x7f0900c0" />
    <public type="id" name="error" id="0x7f0900c1" />
    <public type="id" name="exit" id="0x7f0900c2" />
    <public type="id" name="exitUntilCollapsed" id="0x7f0900c3" />
    <public type="id" name="expand_activities_button" id="0x7f0900c4" />
    <public type="id" name="expanded_menu" id="0x7f0900c5" />
    <public type="id" name="fade" id="0x7f0900c6" />
    <public type="id" name="fill" id="0x7f0900c7" />
    <public type="id" name="fill_horizontal" id="0x7f0900c8" />
    <public type="id" name="fill_vertical" id="0x7f0900c9" />
    <public type="id" name="filled" id="0x7f0900ca" />
    <public type="id" name="fitCenter" id="0x7f0900cb" />
    <public type="id" name="fitEnd" id="0x7f0900cc" />
    <public type="id" name="fitStart" id="0x7f0900cd" />
    <public type="id" name="fitToContents" id="0x7f0900ce" />
    <public type="id" name="fitXY" id="0x7f0900cf" />
    <public type="id" name="fixed" id="0x7f0900d0" />
    <public type="id" name="flip" id="0x7f0900d1" />
    <public type="id" name="floating" id="0x7f0900d2" />
    <public type="id" name="forever" id="0x7f0900d3" />
    <public type="id" name="fragment_container_view_tag" id="0x7f0900d4" />
    <public type="id" name="fragment_setting" id="0x7f0900d5" />
    <public type="id" name="front" id="0x7f0900d6" />
    <public type="id" name="frost" id="0x7f0900d7" />
    <public type="id" name="fullscreen_header" id="0x7f0900d8" />
    <public type="id" name="ghost_view" id="0x7f0900d9" />
    <public type="id" name="ghost_view_holder" id="0x7f0900da" />
    <public type="id" name="glide_custom_view_target_tag" id="0x7f0900db" />
    <public type="id" name="gone" id="0x7f0900dc" />
    <public type="id" name="graph" id="0x7f0900dd" />
    <public type="id" name="graph_wrap" id="0x7f0900de" />
    <public type="id" name="group_divider" id="0x7f0900df" />
    <public type="id" name="grouping" id="0x7f0900e0" />
    <public type="id" name="groups" id="0x7f0900e1" />
    <public type="id" name="guideline" id="0x7f0900e2" />
    <public type="id" name="header_title" id="0x7f0900e3" />
    <public type="id" name="hideable" id="0x7f0900e4" />
    <public type="id" name="home" id="0x7f0900e5" />
    <public type="id" name="homeAsUp" id="0x7f0900e6" />
    <public type="id" name="honorRequest" id="0x7f0900e7" />
    <public type="id" name="horizontal" id="0x7f0900e8" />
    <public type="id" name="horizontal_only" id="0x7f0900e9" />
    <public type="id" name="icon" id="0x7f0900ea" />
    <public type="id" name="icon_group" id="0x7f0900eb" />
    <public type="id" name="ifRoom" id="0x7f0900ec" />
    <public type="id" name="ignore" id="0x7f0900ed" />
    <public type="id" name="ignoreRequest" id="0x7f0900ee" />
    <public type="id" name="image" id="0x7f0900ef" />
    <public type="id" name="immediateStop" id="0x7f0900f0" />
    <public type="id" name="included" id="0x7f0900f1" />
    <public type="id" name="indeterminate" id="0x7f0900f2" />
    <public type="id" name="info" id="0x7f0900f3" />
    <public type="id" name="input" id="0x7f0900f4" />
    <public type="id" name="inputContainer" id="0x7f0900f5" />
    <public type="id" name="invisible" id="0x7f0900f6" />
    <public type="id" name="inward" id="0x7f0900f7" />
    <public type="id" name="italic" id="0x7f0900f8" />
    <public type="id" name="item_touch_helper_previous_elevation" id="0x7f0900f9" />
    <public type="id" name="js_view_extras" id="0x7f0900fa" />
    <public type="id" name="jumpToEnd" id="0x7f0900fb" />
    <public type="id" name="jumpToStart" id="0x7f0900fc" />
    <public type="id" name="labeled" id="0x7f0900fd" />
    <public type="id" name="layout" id="0x7f0900fe" />
    <public type="id" name="left" id="0x7f0900ff" />
    <public type="id" name="leftToRight" id="0x7f090100" />
    <public type="id" name="legacy" id="0x7f090101" />
    <public type="id" name="line1" id="0x7f090102" />
    <public type="id" name="line3" id="0x7f090103" />
    <public type="id" name="linear" id="0x7f090104" />
    <public type="id" name="listMode" id="0x7f090105" />
    <public type="id" name="list_item" id="0x7f090106" />
    <public type="id" name="log" id="0x7f090107" />
    <public type="id" name="logList" id="0x7f090108" />
    <public type="id" name="marquee" id="0x7f090109" />
    <public type="id" name="masked" id="0x7f09010a" />
    <public type="id" name="match_constraint" id="0x7f09010b" />
    <public type="id" name="match_parent" id="0x7f09010c" />
    <public type="id" name="material_clock_display" id="0x7f09010d" />
    <public type="id" name="material_clock_face" id="0x7f09010e" />
    <public type="id" name="material_clock_hand" id="0x7f09010f" />
    <public type="id" name="material_clock_period_am_button" id="0x7f090110" />
    <public type="id" name="material_clock_period_pm_button" id="0x7f090111" />
    <public type="id" name="material_clock_period_toggle" id="0x7f090112" />
    <public type="id" name="material_hour_text_input" id="0x7f090113" />
    <public type="id" name="material_hour_tv" id="0x7f090114" />
    <public type="id" name="material_label" id="0x7f090115" />
    <public type="id" name="material_minute_text_input" id="0x7f090116" />
    <public type="id" name="material_minute_tv" id="0x7f090117" />
    <public type="id" name="material_textinput_timepicker" id="0x7f090118" />
    <public type="id" name="material_timepicker_cancel_button" id="0x7f090119" />
    <public type="id" name="material_timepicker_container" id="0x7f09011a" />
    <public type="id" name="material_timepicker_mode_button" id="0x7f09011b" />
    <public type="id" name="material_timepicker_ok_button" id="0x7f09011c" />
    <public type="id" name="material_timepicker_view" id="0x7f09011d" />
    <public type="id" name="material_value_index" id="0x7f09011e" />
    <public type="id" name="matrix" id="0x7f09011f" />
    <public type="id" name="md_buttonDefaultNegative" id="0x7f090120" />
    <public type="id" name="md_buttonDefaultNeutral" id="0x7f090121" />
    <public type="id" name="md_buttonDefaultPositive" id="0x7f090122" />
    <public type="id" name="md_content" id="0x7f090123" />
    <public type="id" name="md_contentListViewFrame" id="0x7f090124" />
    <public type="id" name="md_contentRecyclerView" id="0x7f090125" />
    <public type="id" name="md_contentScrollView" id="0x7f090126" />
    <public type="id" name="md_control" id="0x7f090127" />
    <public type="id" name="md_customViewFrame" id="0x7f090128" />
    <public type="id" name="md_icon" id="0x7f090129" />
    <public type="id" name="md_label" id="0x7f09012a" />
    <public type="id" name="md_minMax" id="0x7f09012b" />
    <public type="id" name="md_promptCheckbox" id="0x7f09012c" />
    <public type="id" name="md_root" id="0x7f09012d" />
    <public type="id" name="md_title" id="0x7f09012e" />
    <public type="id" name="md_titleFrame" id="0x7f09012f" />
    <public type="id" name="message" id="0x7f090130" />
    <public type="id" name="middle" id="0x7f090131" />
    <public type="id" name="mini" id="0x7f090132" />
    <public type="id" name="minimize" id="0x7f090133" />
    <public type="id" name="mirror" id="0x7f090134" />
    <public type="id" name="month_grid" id="0x7f090135" />
    <public type="id" name="month_navigation_bar" id="0x7f090136" />
    <public type="id" name="month_navigation_fragment_toggle" id="0x7f090137" />
    <public type="id" name="month_navigation_next" id="0x7f090138" />
    <public type="id" name="month_navigation_previous" id="0x7f090139" />
    <public type="id" name="month_title" id="0x7f09013a" />
    <public type="id" name="motion_base" id="0x7f09013b" />
    <public type="id" name="move_cursor" id="0x7f09013c" />
    <public type="id" name="move_or_resize" id="0x7f09013d" />
    <public type="id" name="mtrl_anchor_parent" id="0x7f09013e" />
    <public type="id" name="mtrl_calendar_day_selector_frame" id="0x7f09013f" />
    <public type="id" name="mtrl_calendar_days_of_week" id="0x7f090140" />
    <public type="id" name="mtrl_calendar_frame" id="0x7f090141" />
    <public type="id" name="mtrl_calendar_main_pane" id="0x7f090142" />
    <public type="id" name="mtrl_calendar_months" id="0x7f090143" />
    <public type="id" name="mtrl_calendar_selection_frame" id="0x7f090144" />
    <public type="id" name="mtrl_calendar_text_input_frame" id="0x7f090145" />
    <public type="id" name="mtrl_calendar_year_selector_frame" id="0x7f090146" />
    <public type="id" name="mtrl_card_checked_layer_id" id="0x7f090147" />
    <public type="id" name="mtrl_child_content_container" id="0x7f090148" />
    <public type="id" name="mtrl_internal_children_alpha_tag" id="0x7f090149" />
    <public type="id" name="mtrl_motion_snapshot_view" id="0x7f09014a" />
    <public type="id" name="mtrl_picker_fullscreen" id="0x7f09014b" />
    <public type="id" name="mtrl_picker_header" id="0x7f09014c" />
    <public type="id" name="mtrl_picker_header_selection_text" id="0x7f09014d" />
    <public type="id" name="mtrl_picker_header_title_and_selection" id="0x7f09014e" />
    <public type="id" name="mtrl_picker_header_toggle" id="0x7f09014f" />
    <public type="id" name="mtrl_picker_text_input_date" id="0x7f090150" />
    <public type="id" name="mtrl_picker_text_input_range_end" id="0x7f090151" />
    <public type="id" name="mtrl_picker_text_input_range_start" id="0x7f090152" />
    <public type="id" name="mtrl_picker_title_text" id="0x7f090153" />
    <public type="id" name="mtrl_view_tag_bottom_padding" id="0x7f090154" />
    <public type="id" name="multiply" id="0x7f090155" />
    <public type="id" name="navigation_bar_item_active_indicator_view" id="0x7f090156" />
    <public type="id" name="navigation_bar_item_icon_container" id="0x7f090157" />
    <public type="id" name="navigation_bar_item_icon_view" id="0x7f090158" />
    <public type="id" name="navigation_bar_item_labels_group" id="0x7f090159" />
    <public type="id" name="navigation_bar_item_large_label_view" id="0x7f09015a" />
    <public type="id" name="navigation_bar_item_small_label_view" id="0x7f09015b" />
    <public type="id" name="navigation_header_container" id="0x7f09015c" />
    <public type="id" name="never" id="0x7f09015d" />
    <public type="id" name="neverCompleteToEnd" id="0x7f09015e" />
    <public type="id" name="neverCompleteToStart" id="0x7f09015f" />
    <public type="id" name="noScroll" id="0x7f090160" />
    <public type="id" name="noState" id="0x7f090161" />
    <public type="id" name="none" id="0x7f090162" />
    <public type="id" name="normal" id="0x7f090163" />
    <public type="id" name="north" id="0x7f090164" />
    <public type="id" name="notification_background" id="0x7f090165" />
    <public type="id" name="notification_main_column" id="0x7f090166" />
    <public type="id" name="notification_main_column_container" id="0x7f090167" />
    <public type="id" name="off" id="0x7f090168" />
    <public type="id" name="on" id="0x7f090169" />
    <public type="id" name="onInterceptTouchReturnSwipe" id="0x7f09016a" />
    <public type="id" name="outline" id="0x7f09016b" />
    <public type="id" name="outward" id="0x7f09016c" />
    <public type="id" name="overshoot" id="0x7f09016d" />
    <public type="id" name="packed" id="0x7f09016e" />
    <public type="id" name="parallax" id="0x7f09016f" />
    <public type="id" name="parent" id="0x7f090170" />
    <public type="id" name="parentPanel" id="0x7f090171" />
    <public type="id" name="parentRelative" id="0x7f090172" />
    <public type="id" name="parent_matrix" id="0x7f090173" />
    <public type="id" name="password_toggle" id="0x7f090174" />
    <public type="id" name="path" id="0x7f090175" />
    <public type="id" name="pathRelative" id="0x7f090176" />
    <public type="id" name="peekHeight" id="0x7f090177" />
    <public type="id" name="percent" id="0x7f090178" />
    <public type="id" name="pin" id="0x7f090179" />
    <public type="id" name="position" id="0x7f09017a" />
    <public type="id" name="postLayout" id="0x7f09017b" />
    <public type="id" name="pressed" id="0x7f09017c" />
    <public type="id" name="progress_circular" id="0x7f09017d" />
    <public type="id" name="progress_horizontal" id="0x7f09017e" />
    <public type="id" name="radio" id="0x7f09017f" />
    <public type="id" name="ratio" id="0x7f090180" />
    <public type="id" name="rectangles" id="0x7f090181" />
    <public type="id" name="repeat" id="0x7f090182" />
    <public type="id" name="resizer" id="0x7f090183" />
    <public type="id" name="reverseSawtooth" id="0x7f090184" />
    <public type="id" name="right" id="0x7f090185" />
    <public type="id" name="rightToLeft" id="0x7f090186" />
    <public type="id" name="right_icon" id="0x7f090187" />
    <public type="id" name="right_side" id="0x7f090188" />
    <public type="id" name="rounded" id="0x7f090189" />
    <public type="id" name="row_index_key" id="0x7f09018a" />
    <public type="id" name="runOrStop" id="0x7f09018b" />
    <public type="id" name="save_non_transition_alpha" id="0x7f09018c" />
    <public type="id" name="save_overlay_view" id="0x7f09018d" />
    <public type="id" name="sawtooth" id="0x7f09018e" />
    <public type="id" name="scale" id="0x7f09018f" />
    <public type="id" name="screen" id="0x7f090190" />
    <public type="id" name="scroll" id="0x7f090191" />
    <public type="id" name="scrollIndicatorDown" id="0x7f090192" />
    <public type="id" name="scrollIndicatorUp" id="0x7f090193" />
    <public type="id" name="scrollView" id="0x7f090194" />
    <public type="id" name="scrollable" id="0x7f090195" />
    <public type="id" name="search_badge" id="0x7f090196" />
    <public type="id" name="search_bar" id="0x7f090197" />
    <public type="id" name="search_button" id="0x7f090198" />
    <public type="id" name="search_close_btn" id="0x7f090199" />
    <public type="id" name="search_edit_frame" id="0x7f09019a" />
    <public type="id" name="search_go_btn" id="0x7f09019b" />
    <public type="id" name="search_mag_icon" id="0x7f09019c" />
    <public type="id" name="search_plate" id="0x7f09019d" />
    <public type="id" name="search_src_text" id="0x7f09019e" />
    <public type="id" name="search_voice_btn" id="0x7f09019f" />
    <public type="id" name="select_dialog_listview" id="0x7f0901a0" />
    <public type="id" name="selected" id="0x7f0901a1" />
    <public type="id" name="selection_type" id="0x7f0901a2" />
    <public type="id" name="settings" id="0x7f0901a3" />
    <public type="id" name="sharedValueSet" id="0x7f0901a4" />
    <public type="id" name="sharedValueUnset" id="0x7f0901a5" />
    <public type="id" name="shortcut" id="0x7f0901a6" />
    <public type="id" name="showCustom" id="0x7f0901a7" />
    <public type="id" name="showHome" id="0x7f0901a8" />
    <public type="id" name="showTitle" id="0x7f0901a9" />
    <public type="id" name="sin" id="0x7f0901aa" />
    <public type="id" name="skipCollapsed" id="0x7f0901ab" />
    <public type="id" name="skipped" id="0x7f0901ac" />
    <public type="id" name="slide" id="0x7f0901ad" />
    <public type="id" name="slug" id="0x7f0901ae" />
    <public type="id" name="snackbar_action" id="0x7f0901af" />
    <public type="id" name="snackbar_text" id="0x7f0901b0" />
    <public type="id" name="snap" id="0x7f0901b1" />
    <public type="id" name="snapMargins" id="0x7f0901b2" />
    <public type="id" name="south" id="0x7f0901b3" />
    <public type="id" name="spacer" id="0x7f0901b4" />
    <public type="id" name="special_effects_controller_view_tag" id="0x7f0901b5" />
    <public type="id" name="spline" id="0x7f0901b6" />
    <public type="id" name="split_action_bar" id="0x7f0901b7" />
    <public type="id" name="spread" id="0x7f0901b8" />
    <public type="id" name="spread_inside" id="0x7f0901b9" />
    <public type="id" name="spring" id="0x7f0901ba" />
    <public type="id" name="square" id="0x7f0901bb" />
    <public type="id" name="src_atop" id="0x7f0901bc" />
    <public type="id" name="src_in" id="0x7f0901bd" />
    <public type="id" name="src_over" id="0x7f0901be" />
    <public type="id" name="standard" id="0x7f0901bf" />
    <public type="id" name="start" id="0x7f0901c0" />
    <public type="id" name="startHorizontal" id="0x7f0901c1" />
    <public type="id" name="startToEnd" id="0x7f0901c2" />
    <public type="id" name="startVertical" id="0x7f0901c3" />
    <public type="id" name="staticLayout" id="0x7f0901c4" />
    <public type="id" name="staticPostLayout" id="0x7f0901c5" />
    <public type="id" name="stop" id="0x7f0901c6" />
    <public type="id" name="stretch" id="0x7f0901c7" />
    <public type="id" name="submenuarrow" id="0x7f0901c8" />
    <public type="id" name="submit" id="0x7f0901c9" />
    <public type="id" name="submit_area" id="0x7f0901ca" />
    <public type="id" name="supportScrollUp" id="0x7f0901cb" />
    <public type="id" name="swipeRefreshLayout" id="0x7f0901cc" />
    <public type="id" name="tabMode" id="0x7f0901cd" />
    <public type="id" name="tag_accessibility_actions" id="0x7f0901ce" />
    <public type="id" name="tag_accessibility_clickable_spans" id="0x7f0901cf" />
    <public type="id" name="tag_accessibility_heading" id="0x7f0901d0" />
    <public type="id" name="tag_accessibility_pane_title" id="0x7f0901d1" />
    <public type="id" name="tag_on_apply_window_listener" id="0x7f0901d2" />
    <public type="id" name="tag_on_receive_content_listener" id="0x7f0901d3" />
    <public type="id" name="tag_on_receive_content_mime_types" id="0x7f0901d4" />
    <public type="id" name="tag_screen_reader_focusable" id="0x7f0901d5" />
    <public type="id" name="tag_state_description" id="0x7f0901d6" />
    <public type="id" name="tag_transition_group" id="0x7f0901d7" />
    <public type="id" name="tag_unhandled_key_event_manager" id="0x7f0901d8" />
    <public type="id" name="tag_unhandled_key_listeners" id="0x7f0901d9" />
    <public type="id" name="tag_window_insets_animation_callback" id="0x7f0901da" />
    <public type="id" name="text" id="0x7f0901db" />
    <public type="id" name="text2" id="0x7f0901dc" />
    <public type="id" name="textEnd" id="0x7f0901dd" />
    <public type="id" name="textSpacerNoButtons" id="0x7f0901de" />
    <public type="id" name="textSpacerNoTitle" id="0x7f0901df" />
    <public type="id" name="textStart" id="0x7f0901e0" />
    <public type="id" name="textTop" id="0x7f0901e1" />
    <public type="id" name="text_input_end_icon" id="0x7f0901e2" />
    <public type="id" name="text_input_error_icon" id="0x7f0901e3" />
    <public type="id" name="text_input_start_icon" id="0x7f0901e4" />
    <public type="id" name="textinput_counter" id="0x7f0901e5" />
    <public type="id" name="textinput_error" id="0x7f0901e6" />
    <public type="id" name="textinput_helper_text" id="0x7f0901e7" />
    <public type="id" name="textinput_placeholder" id="0x7f0901e8" />
    <public type="id" name="textinput_prefix_text" id="0x7f0901e9" />
    <public type="id" name="textinput_suffix_text" id="0x7f0901ea" />
    <public type="id" name="time" id="0x7f0901eb" />
    <public type="id" name="title" id="0x7f0901ec" />
    <public type="id" name="titleDividerNoCustom" id="0x7f0901ed" />
    <public type="id" name="title_template" id="0x7f0901ee" />
    <public type="id" name="toggle" id="0x7f0901ef" />
    <public type="id" name="toolbar" id="0x7f0901f0" />
    <public type="id" name="top" id="0x7f0901f1" />
    <public type="id" name="topPanel" id="0x7f0901f2" />
    <public type="id" name="touch_outside" id="0x7f0901f3" />
    <public type="id" name="transitionToEnd" id="0x7f0901f4" />
    <public type="id" name="transitionToStart" id="0x7f0901f5" />
    <public type="id" name="transition_current_scene" id="0x7f0901f6" />
    <public type="id" name="transition_layout_save" id="0x7f0901f7" />
    <public type="id" name="transition_position" id="0x7f0901f8" />
    <public type="id" name="transition_scene_layoutid_cache" id="0x7f0901f9" />
    <public type="id" name="transition_transform" id="0x7f0901fa" />
    <public type="id" name="triangle" id="0x7f0901fb" />
    <public type="id" name="unchecked" id="0x7f0901fc" />
    <public type="id" name="uniform" id="0x7f0901fd" />
    <public type="id" name="unlabeled" id="0x7f0901fe" />
    <public type="id" name="up" id="0x7f0901ff" />
    <public type="id" name="useLogo" id="0x7f090200" />
    <public type="id" name="vertical_only" id="0x7f090201" />
    <public type="id" name="view_offset_helper" id="0x7f090202" />
    <public type="id" name="view_tag_view_extras" id="0x7f090203" />
    <public type="id" name="view_transition" id="0x7f090204" />
    <public type="id" name="view_tree_lifecycle_owner" id="0x7f090205" />
    <public type="id" name="view_tree_on_back_pressed_dispatcher_owner" id="0x7f090206" />
    <public type="id" name="view_tree_saved_state_registry_owner" id="0x7f090207" />
    <public type="id" name="view_tree_view_model_store_owner" id="0x7f090208" />
    <public type="id" name="visible" id="0x7f090209" />
    <public type="id" name="visible_removing_fragment_view_tag" id="0x7f09020a" />
    <public type="id" name="west" id="0x7f09020b" />
    <public type="id" name="withText" id="0x7f09020c" />
    <public type="id" name="with_icon" id="0x7f09020d" />
    <public type="id" name="withinBounds" id="0x7f09020e" />
    <public type="id" name="wrap" id="0x7f09020f" />
    <public type="id" name="wrap_content" id="0x7f090210" />
    <public type="id" name="wrap_content_constrained" id="0x7f090211" />
    <public type="id" name="x_left" id="0x7f090212" />
    <public type="id" name="x_right" id="0x7f090213" />
    <public type="integer" name="abc_config_activityDefaultDur" id="0x7f0a0000" />
    <public type="integer" name="abc_config_activityShortDur" id="0x7f0a0001" />
    <public type="integer" name="app_bar_elevation_anim_duration" id="0x7f0a0002" />
    <public type="integer" name="bottom_sheet_slide_duration" id="0x7f0a0003" />
    <public type="integer" name="cancel_button_image_alpha" id="0x7f0a0004" />
    <public type="integer" name="config_tooltipAnimTime" id="0x7f0a0005" />
    <public type="integer" name="design_snackbar_text_max_lines" id="0x7f0a0006" />
    <public type="integer" name="design_tab_indicator_anim_duration_ms" id="0x7f0a0007" />
    <public type="integer" name="hide_password_duration" id="0x7f0a0008" />
    <public type="integer" name="m3_btn_anim_delay_ms" id="0x7f0a0009" />
    <public type="integer" name="m3_btn_anim_duration_ms" id="0x7f0a000a" />
    <public type="integer" name="m3_card_anim_delay_ms" id="0x7f0a000b" />
    <public type="integer" name="m3_card_anim_duration_ms" id="0x7f0a000c" />
    <public type="integer" name="m3_chip_anim_duration" id="0x7f0a000d" />
    <public type="integer" name="m3_sys_motion_duration_100" id="0x7f0a000e" />
    <public type="integer" name="m3_sys_motion_duration_1000" id="0x7f0a000f" />
    <public type="integer" name="m3_sys_motion_duration_150" id="0x7f0a0010" />
    <public type="integer" name="m3_sys_motion_duration_200" id="0x7f0a0011" />
    <public type="integer" name="m3_sys_motion_duration_250" id="0x7f0a0012" />
    <public type="integer" name="m3_sys_motion_duration_300" id="0x7f0a0013" />
    <public type="integer" name="m3_sys_motion_duration_350" id="0x7f0a0014" />
    <public type="integer" name="m3_sys_motion_duration_400" id="0x7f0a0015" />
    <public type="integer" name="m3_sys_motion_duration_450" id="0x7f0a0016" />
    <public type="integer" name="m3_sys_motion_duration_50" id="0x7f0a0017" />
    <public type="integer" name="m3_sys_motion_duration_500" id="0x7f0a0018" />
    <public type="integer" name="m3_sys_motion_duration_550" id="0x7f0a0019" />
    <public type="integer" name="m3_sys_motion_duration_600" id="0x7f0a001a" />
    <public type="integer" name="m3_sys_motion_duration_700" id="0x7f0a001b" />
    <public type="integer" name="m3_sys_motion_duration_800" id="0x7f0a001c" />
    <public type="integer" name="m3_sys_motion_duration_900" id="0x7f0a001d" />
    <public type="integer" name="m3_sys_motion_duration_extra_long1" id="0x7f0a001e" />
    <public type="integer" name="m3_sys_motion_duration_extra_long2" id="0x7f0a001f" />
    <public type="integer" name="m3_sys_motion_duration_extra_long3" id="0x7f0a0020" />
    <public type="integer" name="m3_sys_motion_duration_extra_long4" id="0x7f0a0021" />
    <public type="integer" name="m3_sys_motion_duration_long1" id="0x7f0a0022" />
    <public type="integer" name="m3_sys_motion_duration_long2" id="0x7f0a0023" />
    <public type="integer" name="m3_sys_motion_duration_long3" id="0x7f0a0024" />
    <public type="integer" name="m3_sys_motion_duration_long4" id="0x7f0a0025" />
    <public type="integer" name="m3_sys_motion_duration_medium1" id="0x7f0a0026" />
    <public type="integer" name="m3_sys_motion_duration_medium2" id="0x7f0a0027" />
    <public type="integer" name="m3_sys_motion_duration_medium3" id="0x7f0a0028" />
    <public type="integer" name="m3_sys_motion_duration_medium4" id="0x7f0a0029" />
    <public type="integer" name="m3_sys_motion_duration_short1" id="0x7f0a002a" />
    <public type="integer" name="m3_sys_motion_duration_short2" id="0x7f0a002b" />
    <public type="integer" name="m3_sys_motion_duration_short3" id="0x7f0a002c" />
    <public type="integer" name="m3_sys_motion_duration_short4" id="0x7f0a002d" />
    <public type="integer" name="material_motion_duration_long_1" id="0x7f0a002e" />
    <public type="integer" name="material_motion_duration_long_2" id="0x7f0a002f" />
    <public type="integer" name="material_motion_duration_medium_1" id="0x7f0a0030" />
    <public type="integer" name="material_motion_duration_medium_2" id="0x7f0a0031" />
    <public type="integer" name="material_motion_duration_short_1" id="0x7f0a0032" />
    <public type="integer" name="material_motion_duration_short_2" id="0x7f0a0033" />
    <public type="integer" name="material_motion_path" id="0x7f0a0034" />
    <public type="integer" name="mtrl_badge_max_character_count" id="0x7f0a0035" />
    <public type="integer" name="mtrl_btn_anim_delay_ms" id="0x7f0a0036" />
    <public type="integer" name="mtrl_btn_anim_duration_ms" id="0x7f0a0037" />
    <public type="integer" name="mtrl_calendar_header_orientation" id="0x7f0a0038" />
    <public type="integer" name="mtrl_calendar_selection_text_lines" id="0x7f0a0039" />
    <public type="integer" name="mtrl_calendar_year_selector_span" id="0x7f0a003a" />
    <public type="integer" name="mtrl_card_anim_delay_ms" id="0x7f0a003b" />
    <public type="integer" name="mtrl_card_anim_duration_ms" id="0x7f0a003c" />
    <public type="integer" name="mtrl_chip_anim_duration" id="0x7f0a003d" />
    <public type="integer" name="mtrl_switch_thumb_motion_duration" id="0x7f0a003e" />
    <public type="integer" name="mtrl_switch_thumb_post_morphing_duration" id="0x7f0a003f" />
    <public type="integer" name="mtrl_switch_thumb_pre_morphing_duration" id="0x7f0a0040" />
    <public type="integer" name="mtrl_switch_thumb_pressed_duration" id="0x7f0a0041" />
    <public type="integer" name="mtrl_switch_thumb_viewport_center_coordinate" id="0x7f0a0042" />
    <public type="integer" name="mtrl_switch_thumb_viewport_size" id="0x7f0a0043" />
    <public type="integer" name="mtrl_switch_track_viewport_height" id="0x7f0a0044" />
    <public type="integer" name="mtrl_switch_track_viewport_width" id="0x7f0a0045" />
    <public type="integer" name="mtrl_tab_indicator_anim_duration_ms" id="0x7f0a0046" />
    <public type="integer" name="mtrl_view_gone" id="0x7f0a0047" />
    <public type="integer" name="mtrl_view_invisible" id="0x7f0a0048" />
    <public type="integer" name="mtrl_view_visible" id="0x7f0a0049" />
    <public type="integer" name="show_password_duration" id="0x7f0a004a" />
    <public type="integer" name="status_bar_notification_info_maxnum" id="0x7f0a004b" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_0" id="0x7f0b0000" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_1" id="0x7f0b0001" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_0" id="0x7f0b0002" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_1" id="0x7f0b0003" />
    <public type="interpolator" name="btn_radio_to_off_mtrl_animation_interpolator_0" id="0x7f0b0004" />
    <public type="interpolator" name="btn_radio_to_on_mtrl_animation_interpolator_0" id="0x7f0b0005" />
    <public type="interpolator" name="fast_out_slow_in" id="0x7f0b0006" />
    <public type="interpolator" name="m3_sys_motion_easing_emphasized" id="0x7f0b0007" />
    <public type="interpolator" name="m3_sys_motion_easing_emphasized_accelerate" id="0x7f0b0008" />
    <public type="interpolator" name="m3_sys_motion_easing_emphasized_decelerate" id="0x7f0b0009" />
    <public type="interpolator" name="m3_sys_motion_easing_linear" id="0x7f0b000a" />
    <public type="interpolator" name="m3_sys_motion_easing_standard" id="0x7f0b000b" />
    <public type="interpolator" name="m3_sys_motion_easing_standard_accelerate" id="0x7f0b000c" />
    <public type="interpolator" name="m3_sys_motion_easing_standard_decelerate" id="0x7f0b000d" />
    <public type="interpolator" name="mtrl_fast_out_linear_in" id="0x7f0b000e" />
    <public type="interpolator" name="mtrl_fast_out_slow_in" id="0x7f0b000f" />
    <public type="interpolator" name="mtrl_linear" id="0x7f0b0010" />
    <public type="interpolator" name="mtrl_linear_out_slow_in" id="0x7f0b0011" />
    <public type="layout" name="abc_action_bar_title_item" id="0x7f0c0000" />
    <public type="layout" name="abc_action_bar_up_container" id="0x7f0c0001" />
    <public type="layout" name="abc_action_menu_item_layout" id="0x7f0c0002" />
    <public type="layout" name="abc_action_menu_layout" id="0x7f0c0003" />
    <public type="layout" name="abc_action_mode_bar" id="0x7f0c0004" />
    <public type="layout" name="abc_action_mode_close_item_material" id="0x7f0c0005" />
    <public type="layout" name="abc_activity_chooser_view" id="0x7f0c0006" />
    <public type="layout" name="abc_activity_chooser_view_list_item" id="0x7f0c0007" />
    <public type="layout" name="abc_alert_dialog_button_bar_material" id="0x7f0c0008" />
    <public type="layout" name="abc_alert_dialog_material" id="0x7f0c0009" />
    <public type="layout" name="abc_alert_dialog_title_material" id="0x7f0c000a" />
    <public type="layout" name="abc_cascading_menu_item_layout" id="0x7f0c000b" />
    <public type="layout" name="abc_dialog_title_material" id="0x7f0c000c" />
    <public type="layout" name="abc_expanded_menu_layout" id="0x7f0c000d" />
    <public type="layout" name="abc_list_menu_item_checkbox" id="0x7f0c000e" />
    <public type="layout" name="abc_list_menu_item_icon" id="0x7f0c000f" />
    <public type="layout" name="abc_list_menu_item_layout" id="0x7f0c0010" />
    <public type="layout" name="abc_list_menu_item_radio" id="0x7f0c0011" />
    <public type="layout" name="abc_popup_menu_header_item_layout" id="0x7f0c0012" />
    <public type="layout" name="abc_popup_menu_item_layout" id="0x7f0c0013" />
    <public type="layout" name="abc_screen_content_include" id="0x7f0c0014" />
    <public type="layout" name="abc_screen_simple" id="0x7f0c0015" />
    <public type="layout" name="abc_screen_simple_overlay_action_mode" id="0x7f0c0016" />
    <public type="layout" name="abc_screen_toolbar" id="0x7f0c0017" />
    <public type="layout" name="abc_search_dropdown_item_icons_2line" id="0x7f0c0018" />
    <public type="layout" name="abc_search_view" id="0x7f0c0019" />
    <public type="layout" name="abc_select_dialog_material" id="0x7f0c001a" />
    <public type="layout" name="abc_tooltip" id="0x7f0c001b" />
    <public type="layout" name="activity_crash_report" id="0x7f0c001c" />
    <public type="layout" name="activity_log" id="0x7f0c001d" />
    <public type="layout" name="activity_settings" id="0x7f0c001e" />
    <public type="layout" name="activity_splash" id="0x7f0c001f" />
    <public type="layout" name="console_view" id="0x7f0c0020" />
    <public type="layout" name="console_view_item" id="0x7f0c0021" />
    <public type="layout" name="custom_dialog" id="0x7f0c0022" />
    <public type="layout" name="date_picker_spinner" id="0x7f0c0023" />
    <public type="layout" name="design_bottom_navigation_item" id="0x7f0c0024" />
    <public type="layout" name="design_bottom_sheet_dialog" id="0x7f0c0025" />
    <public type="layout" name="design_layout_snackbar" id="0x7f0c0026" />
    <public type="layout" name="design_layout_snackbar_include" id="0x7f0c0027" />
    <public type="layout" name="design_layout_tab_icon" id="0x7f0c0028" />
    <public type="layout" name="design_layout_tab_text" id="0x7f0c0029" />
    <public type="layout" name="design_menu_item_action_area" id="0x7f0c002a" />
    <public type="layout" name="design_navigation_item" id="0x7f0c002b" />
    <public type="layout" name="design_navigation_item_header" id="0x7f0c002c" />
    <public type="layout" name="design_navigation_item_separator" id="0x7f0c002d" />
    <public type="layout" name="design_navigation_item_subheader" id="0x7f0c002e" />
    <public type="layout" name="design_navigation_menu" id="0x7f0c002f" />
    <public type="layout" name="design_navigation_menu_item" id="0x7f0c0030" />
    <public type="layout" name="design_text_input_end_icon" id="0x7f0c0031" />
    <public type="layout" name="design_text_input_start_icon" id="0x7f0c0032" />
    <public type="layout" name="ef_expandable_floaty_container" id="0x7f0c0033" />
    <public type="layout" name="ef_floaty_container" id="0x7f0c0034" />
    <public type="layout" name="floating_console_expand" id="0x7f0c0035" />
    <public type="layout" name="floating_controller" id="0x7f0c0036" />
    <public type="layout" name="floating_window_collapse" id="0x7f0c0037" />
    <public type="layout" name="floaty_window" id="0x7f0c0038" />
    <public type="layout" name="fragment_log" id="0x7f0c0039" />
    <public type="layout" name="global_console_view" id="0x7f0c003a" />
    <public type="layout" name="js_appbar" id="0x7f0c003b" />
    <public type="layout" name="js_tablayout" id="0x7f0c003c" />
    <public type="layout" name="js_toolbar" id="0x7f0c003d" />
    <public type="layout" name="log_view" id="0x7f0c003e" />
    <public type="layout" name="m3_alert_dialog" id="0x7f0c003f" />
    <public type="layout" name="m3_alert_dialog_actions" id="0x7f0c0040" />
    <public type="layout" name="m3_alert_dialog_title" id="0x7f0c0041" />
    <public type="layout" name="m3_auto_complete_simple_item" id="0x7f0c0042" />
    <public type="layout" name="material_chip_input_combo" id="0x7f0c0043" />
    <public type="layout" name="material_clock_display" id="0x7f0c0044" />
    <public type="layout" name="material_clock_display_divider" id="0x7f0c0045" />
    <public type="layout" name="material_clock_period_toggle" id="0x7f0c0046" />
    <public type="layout" name="material_clock_period_toggle_land" id="0x7f0c0047" />
    <public type="layout" name="material_clockface_textview" id="0x7f0c0048" />
    <public type="layout" name="material_clockface_view" id="0x7f0c0049" />
    <public type="layout" name="material_radial_view_group" id="0x7f0c004a" />
    <public type="layout" name="material_textinput_timepicker" id="0x7f0c004b" />
    <public type="layout" name="material_time_chip" id="0x7f0c004c" />
    <public type="layout" name="material_time_input" id="0x7f0c004d" />
    <public type="layout" name="material_timepicker" id="0x7f0c004e" />
    <public type="layout" name="material_timepicker_dialog" id="0x7f0c004f" />
    <public type="layout" name="material_timepicker_textinput_display" id="0x7f0c0050" />
    <public type="layout" name="md_dialog_basic" id="0x7f0c0051" />
    <public type="layout" name="md_dialog_basic_check" id="0x7f0c0052" />
    <public type="layout" name="md_dialog_custom" id="0x7f0c0053" />
    <public type="layout" name="md_dialog_input" id="0x7f0c0054" />
    <public type="layout" name="md_dialog_input_check" id="0x7f0c0055" />
    <public type="layout" name="md_dialog_list" id="0x7f0c0056" />
    <public type="layout" name="md_dialog_list_check" id="0x7f0c0057" />
    <public type="layout" name="md_dialog_progress" id="0x7f0c0058" />
    <public type="layout" name="md_dialog_progress_indeterminate" id="0x7f0c0059" />
    <public type="layout" name="md_dialog_progress_indeterminate_horizontal" id="0x7f0c005a" />
    <public type="layout" name="md_listitem" id="0x7f0c005b" />
    <public type="layout" name="md_listitem_multichoice" id="0x7f0c005c" />
    <public type="layout" name="md_listitem_singlechoice" id="0x7f0c005d" />
    <public type="layout" name="md_stub_actionbuttons" id="0x7f0c005e" />
    <public type="layout" name="md_stub_progress" id="0x7f0c005f" />
    <public type="layout" name="md_stub_progress_indeterminate" id="0x7f0c0060" />
    <public type="layout" name="md_stub_progress_indeterminate_horizontal" id="0x7f0c0061" />
    <public type="layout" name="md_stub_titleframe" id="0x7f0c0062" />
    <public type="layout" name="md_stub_titleframe_lesspadding" id="0x7f0c0063" />
    <public type="layout" name="mtrl_alert_dialog" id="0x7f0c0064" />
    <public type="layout" name="mtrl_alert_dialog_actions" id="0x7f0c0065" />
    <public type="layout" name="mtrl_alert_dialog_title" id="0x7f0c0066" />
    <public type="layout" name="mtrl_alert_select_dialog_item" id="0x7f0c0067" />
    <public type="layout" name="mtrl_alert_select_dialog_multichoice" id="0x7f0c0068" />
    <public type="layout" name="mtrl_alert_select_dialog_singlechoice" id="0x7f0c0069" />
    <public type="layout" name="mtrl_auto_complete_simple_item" id="0x7f0c006a" />
    <public type="layout" name="mtrl_calendar_day" id="0x7f0c006b" />
    <public type="layout" name="mtrl_calendar_day_of_week" id="0x7f0c006c" />
    <public type="layout" name="mtrl_calendar_days_of_week" id="0x7f0c006d" />
    <public type="layout" name="mtrl_calendar_horizontal" id="0x7f0c006e" />
    <public type="layout" name="mtrl_calendar_month" id="0x7f0c006f" />
    <public type="layout" name="mtrl_calendar_month_labeled" id="0x7f0c0070" />
    <public type="layout" name="mtrl_calendar_month_navigation" id="0x7f0c0071" />
    <public type="layout" name="mtrl_calendar_months" id="0x7f0c0072" />
    <public type="layout" name="mtrl_calendar_vertical" id="0x7f0c0073" />
    <public type="layout" name="mtrl_calendar_year" id="0x7f0c0074" />
    <public type="layout" name="mtrl_layout_snackbar" id="0x7f0c0075" />
    <public type="layout" name="mtrl_layout_snackbar_include" id="0x7f0c0076" />
    <public type="layout" name="mtrl_navigation_rail_item" id="0x7f0c0077" />
    <public type="layout" name="mtrl_picker_actions" id="0x7f0c0078" />
    <public type="layout" name="mtrl_picker_dialog" id="0x7f0c0079" />
    <public type="layout" name="mtrl_picker_fullscreen" id="0x7f0c007a" />
    <public type="layout" name="mtrl_picker_header_dialog" id="0x7f0c007b" />
    <public type="layout" name="mtrl_picker_header_fullscreen" id="0x7f0c007c" />
    <public type="layout" name="mtrl_picker_header_selection_text" id="0x7f0c007d" />
    <public type="layout" name="mtrl_picker_header_title_text" id="0x7f0c007e" />
    <public type="layout" name="mtrl_picker_header_toggle" id="0x7f0c007f" />
    <public type="layout" name="mtrl_picker_text_input_date" id="0x7f0c0080" />
    <public type="layout" name="mtrl_picker_text_input_date_range" id="0x7f0c0081" />
    <public type="layout" name="notification_action" id="0x7f0c0082" />
    <public type="layout" name="notification_action_tombstone" id="0x7f0c0083" />
    <public type="layout" name="notification_template_custom_big" id="0x7f0c0084" />
    <public type="layout" name="notification_template_icon_group" id="0x7f0c0085" />
    <public type="layout" name="notification_template_part_chronometer" id="0x7f0c0086" />
    <public type="layout" name="notification_template_part_time" id="0x7f0c0087" />
    <public type="layout" name="raw_window" id="0x7f0c0088" />
    <public type="layout" name="select_dialog_item_material" id="0x7f0c0089" />
    <public type="layout" name="select_dialog_multichoice_material" id="0x7f0c008a" />
    <public type="layout" name="select_dialog_singlechoice_material" id="0x7f0c008b" />
    <public type="layout" name="spinner_dropdown_item" id="0x7f0c008c" />
    <public type="layout" name="splash_default" id="0x7f0c008d" />
    <public type="layout" name="support_simple_spinner_dropdown_item" id="0x7f0c008e" />
    <public type="layout" name="time_picker_spinner" id="0x7f0c008f" />
    <public type="menu" name="menu_crash_report" id="0x7f0e0000" />
    <public type="menu" name="menu_log" id="0x7f0e0001" />
    <public type="menu" name="menu_log_view" id="0x7f0e0002" />
    <public type="mipmap" name="ic_launcher" id="0x7f0f0000" />
    <public type="plurals" name="mtrl_badge_content_description" id="0x7f100000" />
    <public type="string" name="abc_action_bar_home_description" id="0x7f110000" />
    <public type="string" name="abc_action_bar_up_description" id="0x7f110001" />
    <public type="string" name="abc_action_menu_overflow_description" id="0x7f110002" />
    <public type="string" name="abc_action_mode_done" id="0x7f110003" />
    <public type="string" name="abc_activity_chooser_view_see_all" id="0x7f110004" />
    <public type="string" name="abc_activitychooserview_choose_application" id="0x7f110005" />
    <public type="string" name="abc_capital_off" id="0x7f110006" />
    <public type="string" name="abc_capital_on" id="0x7f110007" />
    <public type="string" name="abc_menu_alt_shortcut_label" id="0x7f110008" />
    <public type="string" name="abc_menu_ctrl_shortcut_label" id="0x7f110009" />
    <public type="string" name="abc_menu_delete_shortcut_label" id="0x7f11000a" />
    <public type="string" name="abc_menu_enter_shortcut_label" id="0x7f11000b" />
    <public type="string" name="abc_menu_function_shortcut_label" id="0x7f11000c" />
    <public type="string" name="abc_menu_meta_shortcut_label" id="0x7f11000d" />
    <public type="string" name="abc_menu_shift_shortcut_label" id="0x7f11000e" />
    <public type="string" name="abc_menu_space_shortcut_label" id="0x7f11000f" />
    <public type="string" name="abc_menu_sym_shortcut_label" id="0x7f110010" />
    <public type="string" name="abc_prepend_shortcut_label" id="0x7f110011" />
    <public type="string" name="abc_search_hint" id="0x7f110012" />
    <public type="string" name="abc_searchview_description_clear" id="0x7f110013" />
    <public type="string" name="abc_searchview_description_query" id="0x7f110014" />
    <public type="string" name="abc_searchview_description_search" id="0x7f110015" />
    <public type="string" name="abc_searchview_description_submit" id="0x7f110016" />
    <public type="string" name="abc_searchview_description_voice" id="0x7f110017" />
    <public type="string" name="abc_shareactionprovider_share_with" id="0x7f110018" />
    <public type="string" name="abc_shareactionprovider_share_with_application" id="0x7f110019" />
    <public type="string" name="abc_toolbar_collapse_description" id="0x7f11001a" />
    <public type="string" name="androidx_startup" id="0x7f11001b" />
    <public type="string" name="app_name" id="0x7f11001c" />
    <public type="string" name="appbar_scrolling_view_behavior" id="0x7f11001d" />
    <public type="string" name="bottom_sheet_behavior" id="0x7f11001e" />
    <public type="string" name="bottomsheet_action_collapse" id="0x7f11001f" />
    <public type="string" name="bottomsheet_action_expand" id="0x7f110020" />
    <public type="string" name="bottomsheet_action_expand_halfway" id="0x7f110021" />
    <public type="string" name="bottomsheet_drag_handle_clicked" id="0x7f110022" />
    <public type="string" name="bottomsheet_drag_handle_content_description" id="0x7f110023" />
    <public type="string" name="cancel" id="0x7f110024" />
    <public type="string" name="character_counter_content_description" id="0x7f110025" />
    <public type="string" name="character_counter_overflowed_content_description" id="0x7f110026" />
    <public type="string" name="character_counter_pattern" id="0x7f110027" />
    <public type="string" name="clear_text_end_icon_content_description" id="0x7f110028" />
    <public type="string" name="define_roundedimageview" id="0x7f110029" />
    <public type="string" name="details_cannot_handle_intent" id="0x7f11002a" />
    <public type="string" name="error_a11y_label" id="0x7f11002b" />
    <public type="string" name="error_activity_not_found_for_apk_installing" id="0x7f11002c" />
    <public type="string" name="error_cannot_execute_timed_task_file_not_exists" id="0x7f11002d" />
    <public type="string" name="error_cannot_start_activity" id="0x7f11002e" />
    <public type="string" name="error_clip_too_large" id="0x7f11002f" />
    <public type="string" name="error_icon_content_description" id="0x7f110030" />
    <public type="string" name="error_read_log" id="0x7f110031" />
    <public type="string" name="error_screen_capture_need_foreground" id="0x7f110032" />
    <public type="string" name="error_unsuppored_plugin_connection" id="0x7f110033" />
    <public type="string" name="exception_notification_service_disabled" id="0x7f110034" />
    <public type="string" name="exposed_dropdown_menu_content_description" id="0x7f110035" />
    <public type="string" name="fab_transformation_scrim_behavior" id="0x7f110036" />
    <public type="string" name="fab_transformation_sheet_behavior" id="0x7f110037" />
    <public type="string" name="foreground_notification_channel_name" id="0x7f110038" />
    <public type="string" name="foreground_notification_text" id="0x7f110039" />
    <public type="string" name="foreground_notification_title" id="0x7f11003a" />
    <public type="string" name="hide_bottom_view_on_scroll_behavior" id="0x7f11003b" />
    <public type="string" name="icon_content_description" id="0x7f11003c" />
    <public type="string" name="item_view_role_description" id="0x7f11003d" />
    <public type="string" name="jdeferred" id="0x7f11003e" />
    <public type="string" name="key_dont_show_main_activity" id="0x7f11003f" />
    <public type="string" name="key_enable_accessibility_service_by_root" id="0x7f110040" />
    <public type="string" name="key_foreground_service" id="0x7f110041" />
    <public type="string" name="key_print_java_stack_trace" id="0x7f110042" />
    <public type="string" name="key_stable_mode" id="0x7f110043" />
    <public type="string" name="key_use_volume_control_running" id="0x7f110044" />
    <public type="string" name="library_roundedimageview_author" id="0x7f110045" />
    <public type="string" name="library_roundedimageview_authorWebsite" id="0x7f110046" />
    <public type="string" name="library_roundedimageview_isOpenSource" id="0x7f110047" />
    <public type="string" name="library_roundedimageview_libraryDescription" id="0x7f110048" />
    <public type="string" name="library_roundedimageview_libraryName" id="0x7f110049" />
    <public type="string" name="library_roundedimageview_libraryVersion" id="0x7f11004a" />
    <public type="string" name="library_roundedimageview_libraryWebsite" id="0x7f11004b" />
    <public type="string" name="library_roundedimageview_licenseId" id="0x7f11004c" />
    <public type="string" name="library_roundedimageview_repositoryLink" id="0x7f11004d" />
    <public type="string" name="log_accessibility_service_connected" id="0x7f11004e" />
    <public type="string" name="log_accessibility_service_created" id="0x7f11004f" />
    <public type="string" name="log_accessibility_service_destroyed" id="0x7f110050" />
    <public type="string" name="m3_sys_motion_easing_emphasized" id="0x7f110051" />
    <public type="string" name="m3_sys_motion_easing_emphasized_accelerate" id="0x7f110052" />
    <public type="string" name="m3_sys_motion_easing_emphasized_decelerate" id="0x7f110053" />
    <public type="string" name="m3_sys_motion_easing_emphasized_path_data" id="0x7f110054" />
    <public type="string" name="m3_sys_motion_easing_legacy" id="0x7f110055" />
    <public type="string" name="m3_sys_motion_easing_legacy_accelerate" id="0x7f110056" />
    <public type="string" name="m3_sys_motion_easing_legacy_decelerate" id="0x7f110057" />
    <public type="string" name="m3_sys_motion_easing_linear" id="0x7f110058" />
    <public type="string" name="m3_sys_motion_easing_standard" id="0x7f110059" />
    <public type="string" name="m3_sys_motion_easing_standard_accelerate" id="0x7f11005a" />
    <public type="string" name="m3_sys_motion_easing_standard_decelerate" id="0x7f11005b" />
    <public type="string" name="material_clock_display_divider" id="0x7f11005c" />
    <public type="string" name="material_clock_toggle_content_description" id="0x7f11005d" />
    <public type="string" name="material_hour_selection" id="0x7f11005e" />
    <public type="string" name="material_hour_suffix" id="0x7f11005f" />
    <public type="string" name="material_minute_selection" id="0x7f110060" />
    <public type="string" name="material_minute_suffix" id="0x7f110061" />
    <public type="string" name="material_motion_easing_accelerated" id="0x7f110062" />
    <public type="string" name="material_motion_easing_decelerated" id="0x7f110063" />
    <public type="string" name="material_motion_easing_emphasized" id="0x7f110064" />
    <public type="string" name="material_motion_easing_linear" id="0x7f110065" />
    <public type="string" name="material_motion_easing_standard" id="0x7f110066" />
    <public type="string" name="material_slider_range_end" id="0x7f110067" />
    <public type="string" name="material_slider_range_start" id="0x7f110068" />
    <public type="string" name="material_timepicker_am" id="0x7f110069" />
    <public type="string" name="material_timepicker_clock_mode_description" id="0x7f11006a" />
    <public type="string" name="material_timepicker_hour" id="0x7f11006b" />
    <public type="string" name="material_timepicker_minute" id="0x7f11006c" />
    <public type="string" name="material_timepicker_pm" id="0x7f11006d" />
    <public type="string" name="material_timepicker_select_time" id="0x7f11006e" />
    <public type="string" name="material_timepicker_text_input_mode_description" id="0x7f11006f" />
    <public type="string" name="msg_blocking_selector_in_ui_thread" id="0x7f110070" />
    <public type="string" name="msg_gesture_on_main_thread" id="0x7f110071" />
    <public type="string" name="msg_settings_volume_up" id="0x7f110072" />
    <public type="string" name="mtrl_badge_numberless_content_description" id="0x7f110073" />
    <public type="string" name="mtrl_checkbox_button_icon_path_checked" id="0x7f110074" />
    <public type="string" name="mtrl_checkbox_button_icon_path_group_name" id="0x7f110075" />
    <public type="string" name="mtrl_checkbox_button_icon_path_indeterminate" id="0x7f110076" />
    <public type="string" name="mtrl_checkbox_button_icon_path_name" id="0x7f110077" />
    <public type="string" name="mtrl_checkbox_button_path_checked" id="0x7f110078" />
    <public type="string" name="mtrl_checkbox_button_path_group_name" id="0x7f110079" />
    <public type="string" name="mtrl_checkbox_button_path_name" id="0x7f11007a" />
    <public type="string" name="mtrl_checkbox_button_path_unchecked" id="0x7f11007b" />
    <public type="string" name="mtrl_checkbox_state_description_checked" id="0x7f11007c" />
    <public type="string" name="mtrl_checkbox_state_description_indeterminate" id="0x7f11007d" />
    <public type="string" name="mtrl_checkbox_state_description_unchecked" id="0x7f11007e" />
    <public type="string" name="mtrl_chip_close_icon_content_description" id="0x7f11007f" />
    <public type="string" name="mtrl_exceed_max_badge_number_content_description" id="0x7f110080" />
    <public type="string" name="mtrl_exceed_max_badge_number_suffix" id="0x7f110081" />
    <public type="string" name="mtrl_picker_a11y_next_month" id="0x7f110082" />
    <public type="string" name="mtrl_picker_a11y_prev_month" id="0x7f110083" />
    <public type="string" name="mtrl_picker_announce_current_selection" id="0x7f110084" />
    <public type="string" name="mtrl_picker_cancel" id="0x7f110085" />
    <public type="string" name="mtrl_picker_confirm" id="0x7f110086" />
    <public type="string" name="mtrl_picker_date_header_selected" id="0x7f110087" />
    <public type="string" name="mtrl_picker_date_header_title" id="0x7f110088" />
    <public type="string" name="mtrl_picker_date_header_unselected" id="0x7f110089" />
    <public type="string" name="mtrl_picker_day_of_week_column_header" id="0x7f11008a" />
    <public type="string" name="mtrl_picker_invalid_format" id="0x7f11008b" />
    <public type="string" name="mtrl_picker_invalid_format_example" id="0x7f11008c" />
    <public type="string" name="mtrl_picker_invalid_format_use" id="0x7f11008d" />
    <public type="string" name="mtrl_picker_invalid_range" id="0x7f11008e" />
    <public type="string" name="mtrl_picker_navigate_to_year_description" id="0x7f11008f" />
    <public type="string" name="mtrl_picker_out_of_range" id="0x7f110090" />
    <public type="string" name="mtrl_picker_range_header_only_end_selected" id="0x7f110091" />
    <public type="string" name="mtrl_picker_range_header_only_start_selected" id="0x7f110092" />
    <public type="string" name="mtrl_picker_range_header_selected" id="0x7f110093" />
    <public type="string" name="mtrl_picker_range_header_title" id="0x7f110094" />
    <public type="string" name="mtrl_picker_range_header_unselected" id="0x7f110095" />
    <public type="string" name="mtrl_picker_save" id="0x7f110096" />
    <public type="string" name="mtrl_picker_text_input_date_hint" id="0x7f110097" />
    <public type="string" name="mtrl_picker_text_input_date_range_end_hint" id="0x7f110098" />
    <public type="string" name="mtrl_picker_text_input_date_range_start_hint" id="0x7f110099" />
    <public type="string" name="mtrl_picker_text_input_day_abbr" id="0x7f11009a" />
    <public type="string" name="mtrl_picker_text_input_month_abbr" id="0x7f11009b" />
    <public type="string" name="mtrl_picker_text_input_year_abbr" id="0x7f11009c" />
    <public type="string" name="mtrl_picker_toggle_to_calendar_input_mode" id="0x7f11009d" />
    <public type="string" name="mtrl_picker_toggle_to_day_selection" id="0x7f11009e" />
    <public type="string" name="mtrl_picker_toggle_to_text_input_mode" id="0x7f11009f" />
    <public type="string" name="mtrl_picker_toggle_to_year_selection" id="0x7f1100a0" />
    <public type="string" name="mtrl_switch_thumb_group_name" id="0x7f1100a1" />
    <public type="string" name="mtrl_switch_thumb_path_checked" id="0x7f1100a2" />
    <public type="string" name="mtrl_switch_thumb_path_morphing" id="0x7f1100a3" />
    <public type="string" name="mtrl_switch_thumb_path_name" id="0x7f1100a4" />
    <public type="string" name="mtrl_switch_thumb_path_pressed" id="0x7f1100a5" />
    <public type="string" name="mtrl_switch_thumb_path_unchecked" id="0x7f1100a6" />
    <public type="string" name="mtrl_switch_track_decoration_path" id="0x7f1100a7" />
    <public type="string" name="mtrl_switch_track_path" id="0x7f1100a8" />
    <public type="string" name="mtrl_timepicker_cancel" id="0x7f1100a9" />
    <public type="string" name="mtrl_timepicker_confirm" id="0x7f1100aa" />
    <public type="string" name="no_read_phone_state_permissin" id="0x7f1100ab" />
    <public type="string" name="no_read_phone_state_permissin_imei" id="0x7f1100ac" />
    <public type="string" name="no_usage_statics_permission" id="0x7f1100ad" />
    <public type="string" name="no_write_settings_permissin" id="0x7f1100ae" />
    <public type="string" name="ok" id="0x7f1100af" />
    <public type="string" name="password_toggle_content_description" id="0x7f1100b0" />
    <public type="string" name="path_password_eye" id="0x7f1100b1" />
    <public type="string" name="path_password_eye_mask_strike_through" id="0x7f1100b2" />
    <public type="string" name="path_password_eye_mask_visible" id="0x7f1100b3" />
    <public type="string" name="path_password_strike_through" id="0x7f1100b4" />
    <public type="string" name="powered_by_autojs" id="0x7f1100b5" />
    <public type="string" name="script_notification_channel_name" id="0x7f1100b6" />
    <public type="string" name="search_menu_title" id="0x7f1100b7" />
    <public type="string" name="status_bar_notification_info_overflow" id="0x7f1100b8" />
    <public type="string" name="summary_dont_show_main_activity" id="0x7f1100b9" />
    <public type="string" name="summary_enable_accessibility_service_by_root" id="0x7f1100ba" />
    <public type="string" name="summary_pref_foreground_service" id="0x7f1100bb" />
    <public type="string" name="summary_stable_mode" id="0x7f1100bc" />
    <public type="string" name="text_accessibility_service" id="0x7f1100bd" />
    <public type="string" name="text_accessibility_service_description" id="0x7f1100be" />
    <public type="string" name="text_already_copy_to_clip" id="0x7f1100bf" />
    <public type="string" name="text_already_stop_n_scripts" id="0x7f1100c0" />
    <public type="string" name="text_auto_operate_service_enabled_but_not_running" id="0x7f1100c1" />
    <public type="string" name="text_cannot_handle_intent" id="0x7f1100c2" />
    <public type="string" name="text_choose_script_to_handle_intent" id="0x7f1100c3" />
    <public type="string" name="text_clear_log_file" id="0x7f1100c4" />
    <public type="string" name="text_console" id="0x7f1100c5" />
    <public type="string" name="text_copy_crash_info" id="0x7f1100c6" />
    <public type="string" name="text_crash_report" id="0x7f1100c7" />
    <public type="string" name="text_dont_show_main_activity" id="0x7f1100c8" />
    <public type="string" name="text_drawer_close" id="0x7f1100c9" />
    <public type="string" name="text_drawer_open" id="0x7f1100ca" />
    <public type="string" name="text_enable_accessibility_service_by_root" id="0x7f1100cb" />
    <public type="string" name="text_enable_accessibility_service_by_root_timeout" id="0x7f1100cc" />
    <public type="string" name="text_error" id="0x7f1100cd" />
    <public type="string" name="text_execution_finished" id="0x7f1100ce" />
    <public type="string" name="text_exit" id="0x7f1100cf" />
    <public type="string" name="text_file_not_exists" id="0x7f1100d0" />
    <public type="string" name="text_floating_window_permission" id="0x7f1100d1" />
    <public type="string" name="text_foreground_service" id="0x7f1100d2" />
    <public type="string" name="text_ignore_power_battery_optimizations" id="0x7f1100d3" />
    <public type="string" name="text_log" id="0x7f1100d4" />
    <public type="string" name="text_log_level" id="0x7f1100d5" />
    <public type="string" name="text_no_accessibility_permission" id="0x7f1100d6" />
    <public type="string" name="text_no_file_rw_permission" id="0x7f1100d7" />
    <public type="string" name="text_no_floating_window_permission" id="0x7f1100d8" />
    <public type="string" name="text_open_by_other_apps" id="0x7f1100d9" />
    <public type="string" name="text_open_with_scripts" id="0x7f1100da" />
    <public type="string" name="text_others" id="0x7f1100db" />
    <public type="string" name="text_path_is_empty" id="0x7f1100dc" />
    <public type="string" name="text_permissions" id="0x7f1100dd" />
    <public type="string" name="text_please_choose" id="0x7f1100de" />
    <public type="string" name="text_requires_sdk_version_to_run_the_script" id="0x7f1100df" />
    <public type="string" name="text_script_running" id="0x7f1100e0" />
    <public type="string" name="text_search" id="0x7f1100e1" />
    <public type="string" name="text_select_image" id="0x7f1100e2" />
    <public type="string" name="text_settings" id="0x7f1100e3" />
    <public type="string" name="text_should_enable_key_observing" id="0x7f1100e4" />
    <public type="string" name="text_stable_mode" id="0x7f1100e5" />
    <public type="string" name="text_start_running" id="0x7f1100e6" />
    <public type="string" name="text_use_volume_to_stop_running" id="0x7f1100e7" />
    <public type="string" name="tips_background_launch_app" id="0x7f1100e8" />
    <public type="string" name="tray__authority" id="0x7f1100e9" />
    <public type="string" name="warn_memory_leak" id="0x7f1100ea" />
    <public type="style" name="AlertDialog.AppCompat" id="0x7f120000" />
    <public type="style" name="AlertDialog.AppCompat.Light" id="0x7f120001" />
    <public type="style" name="Animation.AppCompat.Dialog" id="0x7f120002" />
    <public type="style" name="Animation.AppCompat.DropDownUp" id="0x7f120003" />
    <public type="style" name="Animation.AppCompat.Tooltip" id="0x7f120004" />
    <public type="style" name="Animation.Design.BottomSheetDialog" id="0x7f120005" />
    <public type="style" name="Animation.MaterialComponents.BottomSheetDialog" id="0x7f120006" />
    <public type="style" name="AppTheme" id="0x7f120007" />
    <public type="style" name="AppTheme.AppBarOverlay" id="0x7f120008" />
    <public type="style" name="AppTheme.PopupOverlay" id="0x7f120009" />
    <public type="style" name="AppTheme.Splash" id="0x7f12000a" />
    <public type="style" name="Base.AlertDialog.AppCompat" id="0x7f12000b" />
    <public type="style" name="Base.AlertDialog.AppCompat.Light" id="0x7f12000c" />
    <public type="style" name="Base.Animation.AppCompat.Dialog" id="0x7f12000d" />
    <public type="style" name="Base.Animation.AppCompat.DropDownUp" id="0x7f12000e" />
    <public type="style" name="Base.Animation.AppCompat.Tooltip" id="0x7f12000f" />
    <public type="style" name="Base.CardView" id="0x7f120010" />
    <public type="style" name="Base.DialogWindowTitle.AppCompat" id="0x7f120011" />
    <public type="style" name="Base.DialogWindowTitleBackground.AppCompat" id="0x7f120012" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Icon" id="0x7f120013" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Panel" id="0x7f120014" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Text" id="0x7f120015" />
    <public type="style" name="Base.TextAppearance.AppCompat" id="0x7f120016" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body1" id="0x7f120017" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body2" id="0x7f120018" />
    <public type="style" name="Base.TextAppearance.AppCompat.Button" id="0x7f120019" />
    <public type="style" name="Base.TextAppearance.AppCompat.Caption" id="0x7f12001a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display1" id="0x7f12001b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display2" id="0x7f12001c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display3" id="0x7f12001d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display4" id="0x7f12001e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Headline" id="0x7f12001f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Inverse" id="0x7f120020" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large" id="0x7f120021" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large.Inverse" id="0x7f120022" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f120023" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f120024" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium" id="0x7f120025" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium.Inverse" id="0x7f120026" />
    <public type="style" name="Base.TextAppearance.AppCompat.Menu" id="0x7f120027" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult" id="0x7f120028" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f120029" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Title" id="0x7f12002a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small" id="0x7f12002b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small.Inverse" id="0x7f12002c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead" id="0x7f12002d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead.Inverse" id="0x7f12002e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title" id="0x7f12002f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title.Inverse" id="0x7f120030" />
    <public type="style" name="Base.TextAppearance.AppCompat.Tooltip" id="0x7f120031" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f120032" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f120033" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f120034" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f120035" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f120036" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f120037" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f120038" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button" id="0x7f120039" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f12003a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f12003b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f12003c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f12003d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f12003e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f12003f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f120040" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Switch" id="0x7f120041" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f120042" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Badge" id="0x7f120043" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Button" id="0x7f120044" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Headline6" id="0x7f120045" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Subtitle2" id="0x7f120046" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f120047" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f120048" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f120049" />
    <public type="style" name="Base.Theme.AppCompat" id="0x7f12004a" />
    <public type="style" name="Base.Theme.AppCompat.CompactMenu" id="0x7f12004b" />
    <public type="style" name="Base.Theme.AppCompat.Dialog" id="0x7f12004c" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.Alert" id="0x7f12004d" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.FixedSize" id="0x7f12004e" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.MinWidth" id="0x7f12004f" />
    <public type="style" name="Base.Theme.AppCompat.DialogWhenLarge" id="0x7f120050" />
    <public type="style" name="Base.Theme.AppCompat.Light" id="0x7f120051" />
    <public type="style" name="Base.Theme.AppCompat.Light.DarkActionBar" id="0x7f120052" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog" id="0x7f120053" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.Alert" id="0x7f120054" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.FixedSize" id="0x7f120055" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f120056" />
    <public type="style" name="Base.Theme.AppCompat.Light.DialogWhenLarge" id="0x7f120057" />
    <public type="style" name="Base.Theme.Material3.Dark" id="0x7f120058" />
    <public type="style" name="Base.Theme.Material3.Dark.BottomSheetDialog" id="0x7f120059" />
    <public type="style" name="Base.Theme.Material3.Dark.Dialog" id="0x7f12005a" />
    <public type="style" name="Base.Theme.Material3.Light" id="0x7f12005b" />
    <public type="style" name="Base.Theme.Material3.Light.BottomSheetDialog" id="0x7f12005c" />
    <public type="style" name="Base.Theme.Material3.Light.Dialog" id="0x7f12005d" />
    <public type="style" name="Base.Theme.MaterialComponents" id="0x7f12005e" />
    <public type="style" name="Base.Theme.MaterialComponents.Bridge" id="0x7f12005f" />
    <public type="style" name="Base.Theme.MaterialComponents.CompactMenu" id="0x7f120060" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog" id="0x7f120061" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Alert" id="0x7f120062" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Bridge" id="0x7f120063" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.FixedSize" id="0x7f120064" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.MinWidth" id="0x7f120065" />
    <public type="style" name="Base.Theme.MaterialComponents.DialogWhenLarge" id="0x7f120066" />
    <public type="style" name="Base.Theme.MaterialComponents.Light" id="0x7f120067" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Bridge" id="0x7f120068" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar" id="0x7f120069" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f12006a" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog" id="0x7f12006b" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f12006c" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f12006d" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f12006e" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f12006f" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f120070" />
    <public type="style" name="Base.ThemeOverlay.AppCompat" id="0x7f120071" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.ActionBar" id="0x7f120072" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark" id="0x7f120073" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f120074" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog" id="0x7f120075" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f120076" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Light" id="0x7f120077" />
    <public type="style" name="Base.ThemeOverlay.Material3.AutoCompleteTextView" id="0x7f120078" />
    <public type="style" name="Base.ThemeOverlay.Material3.BottomSheetDialog" id="0x7f120079" />
    <public type="style" name="Base.ThemeOverlay.Material3.Dialog" id="0x7f12007a" />
    <public type="style" name="Base.ThemeOverlay.Material3.TextInputEditText" id="0x7f12007b" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog" id="0x7f12007c" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f12007d" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" id="0x7f12007e" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" id="0x7f12007f" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f120080" />
    <public type="style" name="Base.V14.Theme.Material3.Dark" id="0x7f120081" />
    <public type="style" name="Base.V14.Theme.Material3.Dark.BottomSheetDialog" id="0x7f120082" />
    <public type="style" name="Base.V14.Theme.Material3.Dark.Dialog" id="0x7f120083" />
    <public type="style" name="Base.V14.Theme.Material3.Light" id="0x7f120084" />
    <public type="style" name="Base.V14.Theme.Material3.Light.BottomSheetDialog" id="0x7f120085" />
    <public type="style" name="Base.V14.Theme.Material3.Light.Dialog" id="0x7f120086" />
    <public type="style" name="Base.V14.Theme.MaterialComponents" id="0x7f120087" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Bridge" id="0x7f120088" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog" id="0x7f120089" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog.Bridge" id="0x7f12008a" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light" id="0x7f12008b" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Bridge" id="0x7f12008c" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f12008d" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog" id="0x7f12008e" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f12008f" />
    <public type="style" name="Base.V14.ThemeOverlay.Material3.BottomSheetDialog" id="0x7f120090" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog" id="0x7f120091" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" id="0x7f120092" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f120093" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f120094" />
    <public type="style" name="Base.V14.Widget.MaterialComponents.AutoCompleteTextView" id="0x7f120095" />
    <public type="style" name="Base.V21.Theme.AppCompat" id="0x7f120096" />
    <public type="style" name="Base.V21.Theme.AppCompat.Dialog" id="0x7f120097" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light" id="0x7f120098" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light.Dialog" id="0x7f120099" />
    <public type="style" name="Base.V21.Theme.MaterialComponents" id="0x7f12009a" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Dialog" id="0x7f12009b" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Light" id="0x7f12009c" />
    <public type="style" name="Base.V21.Theme.MaterialComponents.Light.Dialog" id="0x7f12009d" />
    <public type="style" name="Base.V21.ThemeOverlay.AppCompat.Dialog" id="0x7f12009e" />
    <public type="style" name="Base.V21.ThemeOverlay.Material3.BottomSheetDialog" id="0x7f12009f" />
    <public type="style" name="Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog" id="0x7f1200a0" />
    <public type="style" name="Base.V22.Theme.AppCompat" id="0x7f1200a1" />
    <public type="style" name="Base.V22.Theme.AppCompat.Light" id="0x7f1200a2" />
    <public type="style" name="Base.V23.Theme.AppCompat" id="0x7f1200a3" />
    <public type="style" name="Base.V23.Theme.AppCompat.Light" id="0x7f1200a4" />
    <public type="style" name="Base.V24.Theme.Material3.Dark" id="0x7f1200a5" />
    <public type="style" name="Base.V24.Theme.Material3.Dark.Dialog" id="0x7f1200a6" />
    <public type="style" name="Base.V24.Theme.Material3.Light" id="0x7f1200a7" />
    <public type="style" name="Base.V24.Theme.Material3.Light.Dialog" id="0x7f1200a8" />
    <public type="style" name="Base.V26.Theme.AppCompat" id="0x7f1200a9" />
    <public type="style" name="Base.V26.Theme.AppCompat.Light" id="0x7f1200aa" />
    <public type="style" name="Base.V26.Widget.AppCompat.Toolbar" id="0x7f1200ab" />
    <public type="style" name="Base.V28.Theme.AppCompat" id="0x7f1200ac" />
    <public type="style" name="Base.V28.Theme.AppCompat.Light" id="0x7f1200ad" />
    <public type="style" name="Base.V7.Theme.AppCompat" id="0x7f1200ae" />
    <public type="style" name="Base.V7.Theme.AppCompat.Dialog" id="0x7f1200af" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light" id="0x7f1200b0" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light.Dialog" id="0x7f1200b1" />
    <public type="style" name="Base.V7.ThemeOverlay.AppCompat.Dialog" id="0x7f1200b2" />
    <public type="style" name="Base.V7.Widget.AppCompat.AutoCompleteTextView" id="0x7f1200b3" />
    <public type="style" name="Base.V7.Widget.AppCompat.EditText" id="0x7f1200b4" />
    <public type="style" name="Base.V7.Widget.AppCompat.Toolbar" id="0x7f1200b5" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar" id="0x7f1200b6" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.Solid" id="0x7f1200b7" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabBar" id="0x7f1200b8" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabText" id="0x7f1200b9" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabView" id="0x7f1200ba" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton" id="0x7f1200bb" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.CloseMode" id="0x7f1200bc" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.Overflow" id="0x7f1200bd" />
    <public type="style" name="Base.Widget.AppCompat.ActionMode" id="0x7f1200be" />
    <public type="style" name="Base.Widget.AppCompat.ActivityChooserView" id="0x7f1200bf" />
    <public type="style" name="Base.Widget.AppCompat.AutoCompleteTextView" id="0x7f1200c0" />
    <public type="style" name="Base.Widget.AppCompat.Button" id="0x7f1200c1" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless" id="0x7f1200c2" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless.Colored" id="0x7f1200c3" />
    <public type="style" name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f1200c4" />
    <public type="style" name="Base.Widget.AppCompat.Button.Colored" id="0x7f1200c5" />
    <public type="style" name="Base.Widget.AppCompat.Button.Small" id="0x7f1200c6" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar" id="0x7f1200c7" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f1200c8" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.CheckBox" id="0x7f1200c9" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.RadioButton" id="0x7f1200ca" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.Switch" id="0x7f1200cb" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle" id="0x7f1200cc" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle.Common" id="0x7f1200cd" />
    <public type="style" name="Base.Widget.AppCompat.DropDownItem.Spinner" id="0x7f1200ce" />
    <public type="style" name="Base.Widget.AppCompat.EditText" id="0x7f1200cf" />
    <public type="style" name="Base.Widget.AppCompat.ImageButton" id="0x7f1200d0" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar" id="0x7f1200d1" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.Solid" id="0x7f1200d2" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f1200d3" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText" id="0x7f1200d4" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f1200d5" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabView" id="0x7f1200d6" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu" id="0x7f1200d7" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f1200d8" />
    <public type="style" name="Base.Widget.AppCompat.ListMenuView" id="0x7f1200d9" />
    <public type="style" name="Base.Widget.AppCompat.ListPopupWindow" id="0x7f1200da" />
    <public type="style" name="Base.Widget.AppCompat.ListView" id="0x7f1200db" />
    <public type="style" name="Base.Widget.AppCompat.ListView.DropDown" id="0x7f1200dc" />
    <public type="style" name="Base.Widget.AppCompat.ListView.Menu" id="0x7f1200dd" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu" id="0x7f1200de" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu.Overflow" id="0x7f1200df" />
    <public type="style" name="Base.Widget.AppCompat.PopupWindow" id="0x7f1200e0" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar" id="0x7f1200e1" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar.Horizontal" id="0x7f1200e2" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar" id="0x7f1200e3" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Indicator" id="0x7f1200e4" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Small" id="0x7f1200e5" />
    <public type="style" name="Base.Widget.AppCompat.SearchView" id="0x7f1200e6" />
    <public type="style" name="Base.Widget.AppCompat.SearchView.ActionBar" id="0x7f1200e7" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar" id="0x7f1200e8" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar.Discrete" id="0x7f1200e9" />
    <public type="style" name="Base.Widget.AppCompat.Spinner" id="0x7f1200ea" />
    <public type="style" name="Base.Widget.AppCompat.Spinner.Underlined" id="0x7f1200eb" />
    <public type="style" name="Base.Widget.AppCompat.TextView" id="0x7f1200ec" />
    <public type="style" name="Base.Widget.AppCompat.TextView.SpinnerItem" id="0x7f1200ed" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar" id="0x7f1200ee" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f1200ef" />
    <public type="style" name="Base.Widget.Design.TabLayout" id="0x7f1200f0" />
    <public type="style" name="Base.Widget.Material3.ActionBar.Solid" id="0x7f1200f1" />
    <public type="style" name="Base.Widget.Material3.ActionMode" id="0x7f1200f2" />
    <public type="style" name="Base.Widget.Material3.CardView" id="0x7f1200f3" />
    <public type="style" name="Base.Widget.Material3.Chip" id="0x7f1200f4" />
    <public type="style" name="Base.Widget.Material3.CollapsingToolbar" id="0x7f1200f5" />
    <public type="style" name="Base.Widget.Material3.CompoundButton.CheckBox" id="0x7f1200f6" />
    <public type="style" name="Base.Widget.Material3.CompoundButton.RadioButton" id="0x7f1200f7" />
    <public type="style" name="Base.Widget.Material3.CompoundButton.Switch" id="0x7f1200f8" />
    <public type="style" name="Base.Widget.Material3.ExtendedFloatingActionButton" id="0x7f1200f9" />
    <public type="style" name="Base.Widget.Material3.ExtendedFloatingActionButton.Icon" id="0x7f1200fa" />
    <public type="style" name="Base.Widget.Material3.FloatingActionButton" id="0x7f1200fb" />
    <public type="style" name="Base.Widget.Material3.FloatingActionButton.Large" id="0x7f1200fc" />
    <public type="style" name="Base.Widget.Material3.FloatingActionButton.Small" id="0x7f1200fd" />
    <public type="style" name="Base.Widget.Material3.Light.ActionBar.Solid" id="0x7f1200fe" />
    <public type="style" name="Base.Widget.Material3.MaterialCalendar.NavigationButton" id="0x7f1200ff" />
    <public type="style" name="Base.Widget.Material3.Snackbar" id="0x7f120100" />
    <public type="style" name="Base.Widget.Material3.TabLayout" id="0x7f120101" />
    <public type="style" name="Base.Widget.Material3.TabLayout.OnSurface" id="0x7f120102" />
    <public type="style" name="Base.Widget.Material3.TabLayout.Secondary" id="0x7f120103" />
    <public type="style" name="Base.Widget.MaterialComponents.AutoCompleteTextView" id="0x7f120104" />
    <public type="style" name="Base.Widget.MaterialComponents.CheckedTextView" id="0x7f120105" />
    <public type="style" name="Base.Widget.MaterialComponents.Chip" id="0x7f120106" />
    <public type="style" name="Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" id="0x7f120107" />
    <public type="style" name="Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton" id="0x7f120108" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu" id="0x7f120109" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.ContextMenu" id="0x7f12010a" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow" id="0x7f12010b" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.Overflow" id="0x7f12010c" />
    <public type="style" name="Base.Widget.MaterialComponents.Slider" id="0x7f12010d" />
    <public type="style" name="Base.Widget.MaterialComponents.Snackbar" id="0x7f12010e" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputEditText" id="0x7f12010f" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputLayout" id="0x7f120110" />
    <public type="style" name="Base.Widget.MaterialComponents.TextView" id="0x7f120111" />
    <public type="style" name="CardView" id="0x7f120112" />
    <public type="style" name="CardView.Dark" id="0x7f120113" />
    <public type="style" name="CardView.Light" id="0x7f120114" />
    <public type="style" name="ConsoleTheme" id="0x7f120115" />
    <public type="style" name="MD_ActionButton" id="0x7f120116" />
    <public type="style" name="MD_ActionButton.Text" id="0x7f120117" />
    <public type="style" name="MD_ActionButtonStacked" id="0x7f120118" />
    <public type="style" name="MD_Dark" id="0x7f120119" />
    <public type="style" name="MD_Light" id="0x7f12011a" />
    <public type="style" name="MD_WindowAnimation" id="0x7f12011b" />
    <public type="style" name="MaterialAlertDialog.Material3" id="0x7f12011c" />
    <public type="style" name="MaterialAlertDialog.Material3.Body.Text" id="0x7f12011d" />
    <public type="style" name="MaterialAlertDialog.Material3.Body.Text.CenterStacked" id="0x7f12011e" />
    <public type="style" name="MaterialAlertDialog.Material3.Title.Icon" id="0x7f12011f" />
    <public type="style" name="MaterialAlertDialog.Material3.Title.Icon.CenterStacked" id="0x7f120120" />
    <public type="style" name="MaterialAlertDialog.Material3.Title.Panel" id="0x7f120121" />
    <public type="style" name="MaterialAlertDialog.Material3.Title.Panel.CenterStacked" id="0x7f120122" />
    <public type="style" name="MaterialAlertDialog.Material3.Title.Text" id="0x7f120123" />
    <public type="style" name="MaterialAlertDialog.Material3.Title.Text.CenterStacked" id="0x7f120124" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents" id="0x7f120125" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Body.Text" id="0x7f120126" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar" id="0x7f120127" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner" id="0x7f120128" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Icon" id="0x7f120129" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked" id="0x7f12012a" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Panel" id="0x7f12012b" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked" id="0x7f12012c" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Text" id="0x7f12012d" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked" id="0x7f12012e" />
    <public type="style" name="Platform.AppCompat" id="0x7f12012f" />
    <public type="style" name="Platform.AppCompat.Light" id="0x7f120130" />
    <public type="style" name="Platform.MaterialComponents" id="0x7f120131" />
    <public type="style" name="Platform.MaterialComponents.Dialog" id="0x7f120132" />
    <public type="style" name="Platform.MaterialComponents.Light" id="0x7f120133" />
    <public type="style" name="Platform.MaterialComponents.Light.Dialog" id="0x7f120134" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat" id="0x7f120135" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Dark" id="0x7f120136" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Light" id="0x7f120137" />
    <public type="style" name="Platform.V21.AppCompat" id="0x7f120138" />
    <public type="style" name="Platform.V21.AppCompat.Light" id="0x7f120139" />
    <public type="style" name="Platform.V25.AppCompat" id="0x7f12013a" />
    <public type="style" name="Platform.V25.AppCompat.Light" id="0x7f12013b" />
    <public type="style" name="Platform.Widget.AppCompat.Spinner" id="0x7f12013c" />
    <public type="style" name="RtlOverlay.DialogWindowTitle.AppCompat" id="0x7f12013d" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" id="0x7f12013e" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" id="0x7f12013f" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem" id="0x7f120140" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" id="0x7f120141" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" id="0x7f120142" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" id="0x7f120143" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" id="0x7f120144" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" id="0x7f120145" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown" id="0x7f120146" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" id="0x7f120147" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" id="0x7f120148" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" id="0x7f120149" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" id="0x7f12014a" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" id="0x7f12014b" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton" id="0x7f12014c" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" id="0x7f12014d" />
    <public type="style" name="ScriptTheme" id="0x7f12014e" />
    <public type="style" name="ScriptTheme.AppBarOverlay" id="0x7f12014f" />
    <public type="style" name="ScriptTheme.Material3" id="0x7f120150" />
    <public type="style" name="ScriptTheme.PopupOverlay" id="0x7f120151" />
    <public type="style" name="ScriptTheme.Transparent" id="0x7f120152" />
    <public type="style" name="ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape" id="0x7f120153" />
    <public type="style" name="ShapeAppearance.M3.Comp.Switch.Handle.Shape" id="0x7f120154" />
    <public type="style" name="ShapeAppearance.M3.Comp.Switch.StateLayer.Shape" id="0x7f120155" />
    <public type="style" name="ShapeAppearance.M3.Comp.Switch.Track.Shape" id="0x7f120156" />
    <public type="style" name="ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge" id="0x7f120157" />
    <public type="style" name="ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall" id="0x7f120158" />
    <public type="style" name="ShapeAppearance.M3.Sys.Shape.Corner.Full" id="0x7f120159" />
    <public type="style" name="ShapeAppearance.M3.Sys.Shape.Corner.Large" id="0x7f12015a" />
    <public type="style" name="ShapeAppearance.M3.Sys.Shape.Corner.Medium" id="0x7f12015b" />
    <public type="style" name="ShapeAppearance.M3.Sys.Shape.Corner.None" id="0x7f12015c" />
    <public type="style" name="ShapeAppearance.M3.Sys.Shape.Corner.Small" id="0x7f12015d" />
    <public type="style" name="ShapeAppearance.Material3.Corner.ExtraLarge" id="0x7f12015e" />
    <public type="style" name="ShapeAppearance.Material3.Corner.ExtraSmall" id="0x7f12015f" />
    <public type="style" name="ShapeAppearance.Material3.Corner.Full" id="0x7f120160" />
    <public type="style" name="ShapeAppearance.Material3.Corner.Large" id="0x7f120161" />
    <public type="style" name="ShapeAppearance.Material3.Corner.Medium" id="0x7f120162" />
    <public type="style" name="ShapeAppearance.Material3.Corner.None" id="0x7f120163" />
    <public type="style" name="ShapeAppearance.Material3.Corner.Small" id="0x7f120164" />
    <public type="style" name="ShapeAppearance.Material3.LargeComponent" id="0x7f120165" />
    <public type="style" name="ShapeAppearance.Material3.MediumComponent" id="0x7f120166" />
    <public type="style" name="ShapeAppearance.Material3.NavigationBarView.ActiveIndicator" id="0x7f120167" />
    <public type="style" name="ShapeAppearance.Material3.SmallComponent" id="0x7f120168" />
    <public type="style" name="ShapeAppearance.Material3.Tooltip" id="0x7f120169" />
    <public type="style" name="ShapeAppearance.MaterialComponents" id="0x7f12016a" />
    <public type="style" name="ShapeAppearance.MaterialComponents.LargeComponent" id="0x7f12016b" />
    <public type="style" name="ShapeAppearance.MaterialComponents.MediumComponent" id="0x7f12016c" />
    <public type="style" name="ShapeAppearance.MaterialComponents.SmallComponent" id="0x7f12016d" />
    <public type="style" name="ShapeAppearance.MaterialComponents.Tooltip" id="0x7f12016e" />
    <public type="style" name="ShapeAppearanceOverlay.Material3.Button" id="0x7f12016f" />
    <public type="style" name="ShapeAppearanceOverlay.Material3.Chip" id="0x7f120170" />
    <public type="style" name="ShapeAppearanceOverlay.Material3.Corner.Bottom" id="0x7f120171" />
    <public type="style" name="ShapeAppearanceOverlay.Material3.Corner.Left" id="0x7f120172" />
    <public type="style" name="ShapeAppearanceOverlay.Material3.Corner.Right" id="0x7f120173" />
    <public type="style" name="ShapeAppearanceOverlay.Material3.Corner.Top" id="0x7f120174" />
    <public type="style" name="ShapeAppearanceOverlay.Material3.FloatingActionButton" id="0x7f120175" />
    <public type="style" name="ShapeAppearanceOverlay.Material3.NavigationView.Item" id="0x7f120176" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialAlertDialog.Material3" id="0x7f120177" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.BottomSheet" id="0x7f120178" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.Chip" id="0x7f120179" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton" id="0x7f12017a" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton" id="0x7f12017b" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" id="0x7f12017c" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen" id="0x7f12017d" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year" id="0x7f12017e" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox" id="0x7f12017f" />
    <public type="style" name="TextAppearance.AppCompat" id="0x7f120180" />
    <public type="style" name="TextAppearance.AppCompat.Body1" id="0x7f120181" />
    <public type="style" name="TextAppearance.AppCompat.Body2" id="0x7f120182" />
    <public type="style" name="TextAppearance.AppCompat.Button" id="0x7f120183" />
    <public type="style" name="TextAppearance.AppCompat.Caption" id="0x7f120184" />
    <public type="style" name="TextAppearance.AppCompat.Display1" id="0x7f120185" />
    <public type="style" name="TextAppearance.AppCompat.Display2" id="0x7f120186" />
    <public type="style" name="TextAppearance.AppCompat.Display3" id="0x7f120187" />
    <public type="style" name="TextAppearance.AppCompat.Display4" id="0x7f120188" />
    <public type="style" name="TextAppearance.AppCompat.Headline" id="0x7f120189" />
    <public type="style" name="TextAppearance.AppCompat.Inverse" id="0x7f12018a" />
    <public type="style" name="TextAppearance.AppCompat.Large" id="0x7f12018b" />
    <public type="style" name="TextAppearance.AppCompat.Large.Inverse" id="0x7f12018c" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" id="0x7f12018d" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Title" id="0x7f12018e" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f12018f" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f120190" />
    <public type="style" name="TextAppearance.AppCompat.Medium" id="0x7f120191" />
    <public type="style" name="TextAppearance.AppCompat.Medium.Inverse" id="0x7f120192" />
    <public type="style" name="TextAppearance.AppCompat.Menu" id="0x7f120193" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f120194" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Title" id="0x7f120195" />
    <public type="style" name="TextAppearance.AppCompat.Small" id="0x7f120196" />
    <public type="style" name="TextAppearance.AppCompat.Small.Inverse" id="0x7f120197" />
    <public type="style" name="TextAppearance.AppCompat.Subhead" id="0x7f120198" />
    <public type="style" name="TextAppearance.AppCompat.Subhead.Inverse" id="0x7f120199" />
    <public type="style" name="TextAppearance.AppCompat.Title" id="0x7f12019a" />
    <public type="style" name="TextAppearance.AppCompat.Title.Inverse" id="0x7f12019b" />
    <public type="style" name="TextAppearance.AppCompat.Tooltip" id="0x7f12019c" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f12019d" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f12019e" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f12019f" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f1201a0" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f1201a1" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f1201a2" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" id="0x7f1201a3" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f1201a4" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" id="0x7f1201a5" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button" id="0x7f1201a6" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f1201a7" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f1201a8" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f1201a9" />
    <public type="style" name="TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f1201aa" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f1201ab" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f1201ac" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f1201ad" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Switch" id="0x7f1201ae" />
    <public type="style" name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f1201af" />
    <public type="style" name="TextAppearance.Compat.Notification" id="0x7f1201b0" />
    <public type="style" name="TextAppearance.Compat.Notification.Info" id="0x7f1201b1" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2" id="0x7f1201b2" />
    <public type="style" name="TextAppearance.Compat.Notification.Time" id="0x7f1201b3" />
    <public type="style" name="TextAppearance.Compat.Notification.Title" id="0x7f1201b4" />
    <public type="style" name="TextAppearance.Design.CollapsingToolbar.Expanded" id="0x7f1201b5" />
    <public type="style" name="TextAppearance.Design.Counter" id="0x7f1201b6" />
    <public type="style" name="TextAppearance.Design.Counter.Overflow" id="0x7f1201b7" />
    <public type="style" name="TextAppearance.Design.Error" id="0x7f1201b8" />
    <public type="style" name="TextAppearance.Design.HelperText" id="0x7f1201b9" />
    <public type="style" name="TextAppearance.Design.Hint" id="0x7f1201ba" />
    <public type="style" name="TextAppearance.Design.Placeholder" id="0x7f1201bb" />
    <public type="style" name="TextAppearance.Design.Prefix" id="0x7f1201bc" />
    <public type="style" name="TextAppearance.Design.Snackbar.Message" id="0x7f1201bd" />
    <public type="style" name="TextAppearance.Design.Suffix" id="0x7f1201be" />
    <public type="style" name="TextAppearance.Design.Tab" id="0x7f1201bf" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.BodyLarge" id="0x7f1201c0" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.BodyMedium" id="0x7f1201c1" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.BodySmall" id="0x7f1201c2" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.DisplayLarge" id="0x7f1201c3" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.DisplayMedium" id="0x7f1201c4" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.DisplaySmall" id="0x7f1201c5" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.HeadlineLarge" id="0x7f1201c6" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.HeadlineMedium" id="0x7f1201c7" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.HeadlineSmall" id="0x7f1201c8" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.LabelLarge" id="0x7f1201c9" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.LabelMedium" id="0x7f1201ca" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.LabelSmall" id="0x7f1201cb" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.TitleLarge" id="0x7f1201cc" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.TitleMedium" id="0x7f1201cd" />
    <public type="style" name="TextAppearance.M3.Sys.Typescale.TitleSmall" id="0x7f1201ce" />
    <public type="style" name="TextAppearance.Material3.ActionBar.Subtitle" id="0x7f1201cf" />
    <public type="style" name="TextAppearance.Material3.ActionBar.Title" id="0x7f1201d0" />
    <public type="style" name="TextAppearance.Material3.BodyLarge" id="0x7f1201d1" />
    <public type="style" name="TextAppearance.Material3.BodyMedium" id="0x7f1201d2" />
    <public type="style" name="TextAppearance.Material3.BodySmall" id="0x7f1201d3" />
    <public type="style" name="TextAppearance.Material3.DisplayLarge" id="0x7f1201d4" />
    <public type="style" name="TextAppearance.Material3.DisplayMedium" id="0x7f1201d5" />
    <public type="style" name="TextAppearance.Material3.DisplaySmall" id="0x7f1201d6" />
    <public type="style" name="TextAppearance.Material3.HeadlineLarge" id="0x7f1201d7" />
    <public type="style" name="TextAppearance.Material3.HeadlineMedium" id="0x7f1201d8" />
    <public type="style" name="TextAppearance.Material3.HeadlineSmall" id="0x7f1201d9" />
    <public type="style" name="TextAppearance.Material3.LabelLarge" id="0x7f1201da" />
    <public type="style" name="TextAppearance.Material3.LabelMedium" id="0x7f1201db" />
    <public type="style" name="TextAppearance.Material3.LabelSmall" id="0x7f1201dc" />
    <public type="style" name="TextAppearance.Material3.MaterialTimePicker.Title" id="0x7f1201dd" />
    <public type="style" name="TextAppearance.Material3.TitleLarge" id="0x7f1201de" />
    <public type="style" name="TextAppearance.Material3.TitleMedium" id="0x7f1201df" />
    <public type="style" name="TextAppearance.Material3.TitleSmall" id="0x7f1201e0" />
    <public type="style" name="TextAppearance.MaterialComponents.Badge" id="0x7f1201e1" />
    <public type="style" name="TextAppearance.MaterialComponents.Body1" id="0x7f1201e2" />
    <public type="style" name="TextAppearance.MaterialComponents.Body2" id="0x7f1201e3" />
    <public type="style" name="TextAppearance.MaterialComponents.Button" id="0x7f1201e4" />
    <public type="style" name="TextAppearance.MaterialComponents.Caption" id="0x7f1201e5" />
    <public type="style" name="TextAppearance.MaterialComponents.Chip" id="0x7f1201e6" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline1" id="0x7f1201e7" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline2" id="0x7f1201e8" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline3" id="0x7f1201e9" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline4" id="0x7f1201ea" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline5" id="0x7f1201eb" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline6" id="0x7f1201ec" />
    <public type="style" name="TextAppearance.MaterialComponents.Overline" id="0x7f1201ed" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle1" id="0x7f1201ee" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle2" id="0x7f1201ef" />
    <public type="style" name="TextAppearance.MaterialComponents.TimePicker.Title" id="0x7f1201f0" />
    <public type="style" name="TextAppearance.MaterialComponents.Tooltip" id="0x7f1201f1" />
    <public type="style" name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f1201f2" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f1201f3" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f1201f4" />
    <public type="style" name="Theme.AppCompat" id="0x7f1201f5" />
    <public type="style" name="Theme.AppCompat.CompactMenu" id="0x7f1201f6" />
    <public type="style" name="Theme.AppCompat.DayNight" id="0x7f1201f7" />
    <public type="style" name="Theme.AppCompat.DayNight.DarkActionBar" id="0x7f1201f8" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog" id="0x7f1201f9" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.Alert" id="0x7f1201fa" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.MinWidth" id="0x7f1201fb" />
    <public type="style" name="Theme.AppCompat.DayNight.DialogWhenLarge" id="0x7f1201fc" />
    <public type="style" name="Theme.AppCompat.DayNight.NoActionBar" id="0x7f1201fd" />
    <public type="style" name="Theme.AppCompat.Dialog" id="0x7f1201fe" />
    <public type="style" name="Theme.AppCompat.Dialog.Alert" id="0x7f1201ff" />
    <public type="style" name="Theme.AppCompat.Dialog.MinWidth" id="0x7f120200" />
    <public type="style" name="Theme.AppCompat.DialogWhenLarge" id="0x7f120201" />
    <public type="style" name="Theme.AppCompat.Empty" id="0x7f120202" />
    <public type="style" name="Theme.AppCompat.Light" id="0x7f120203" />
    <public type="style" name="Theme.AppCompat.Light.DarkActionBar" id="0x7f120204" />
    <public type="style" name="Theme.AppCompat.Light.Dialog" id="0x7f120205" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.Alert" id="0x7f120206" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f120207" />
    <public type="style" name="Theme.AppCompat.Light.DialogWhenLarge" id="0x7f120208" />
    <public type="style" name="Theme.AppCompat.Light.NoActionBar" id="0x7f120209" />
    <public type="style" name="Theme.AppCompat.NoActionBar" id="0x7f12020a" />
    <public type="style" name="Theme.Design" id="0x7f12020b" />
    <public type="style" name="Theme.Design.BottomSheetDialog" id="0x7f12020c" />
    <public type="style" name="Theme.Design.Light" id="0x7f12020d" />
    <public type="style" name="Theme.Design.Light.BottomSheetDialog" id="0x7f12020e" />
    <public type="style" name="Theme.Design.Light.NoActionBar" id="0x7f12020f" />
    <public type="style" name="Theme.Design.NoActionBar" id="0x7f120210" />
    <public type="style" name="Theme.Material3.Dark" id="0x7f120211" />
    <public type="style" name="Theme.Material3.Dark.BottomSheetDialog" id="0x7f120212" />
    <public type="style" name="Theme.Material3.Dark.Dialog" id="0x7f120213" />
    <public type="style" name="Theme.Material3.Dark.Dialog.Alert" id="0x7f120214" />
    <public type="style" name="Theme.Material3.Dark.Dialog.MinWidth" id="0x7f120215" />
    <public type="style" name="Theme.Material3.Dark.DialogWhenLarge" id="0x7f120216" />
    <public type="style" name="Theme.Material3.Dark.NoActionBar" id="0x7f120217" />
    <public type="style" name="Theme.Material3.DayNight" id="0x7f120218" />
    <public type="style" name="Theme.Material3.DayNight.BottomSheetDialog" id="0x7f120219" />
    <public type="style" name="Theme.Material3.DayNight.Dialog" id="0x7f12021a" />
    <public type="style" name="Theme.Material3.DayNight.Dialog.Alert" id="0x7f12021b" />
    <public type="style" name="Theme.Material3.DayNight.Dialog.MinWidth" id="0x7f12021c" />
    <public type="style" name="Theme.Material3.DayNight.DialogWhenLarge" id="0x7f12021d" />
    <public type="style" name="Theme.Material3.DayNight.NoActionBar" id="0x7f12021e" />
    <public type="style" name="Theme.Material3.DynamicColors.Dark" id="0x7f12021f" />
    <public type="style" name="Theme.Material3.DynamicColors.DayNight" id="0x7f120220" />
    <public type="style" name="Theme.Material3.DynamicColors.Light" id="0x7f120221" />
    <public type="style" name="Theme.Material3.Light" id="0x7f120222" />
    <public type="style" name="Theme.Material3.Light.BottomSheetDialog" id="0x7f120223" />
    <public type="style" name="Theme.Material3.Light.Dialog" id="0x7f120224" />
    <public type="style" name="Theme.Material3.Light.Dialog.Alert" id="0x7f120225" />
    <public type="style" name="Theme.Material3.Light.Dialog.MinWidth" id="0x7f120226" />
    <public type="style" name="Theme.Material3.Light.DialogWhenLarge" id="0x7f120227" />
    <public type="style" name="Theme.Material3.Light.NoActionBar" id="0x7f120228" />
    <public type="style" name="Theme.MaterialComponents" id="0x7f120229" />
    <public type="style" name="Theme.MaterialComponents.BottomSheetDialog" id="0x7f12022a" />
    <public type="style" name="Theme.MaterialComponents.Bridge" id="0x7f12022b" />
    <public type="style" name="Theme.MaterialComponents.CompactMenu" id="0x7f12022c" />
    <public type="style" name="Theme.MaterialComponents.DayNight" id="0x7f12022d" />
    <public type="style" name="Theme.MaterialComponents.DayNight.BottomSheetDialog" id="0x7f12022e" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Bridge" id="0x7f12022f" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DarkActionBar" id="0x7f120230" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" id="0x7f120231" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog" id="0x7f120232" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Alert" id="0x7f120233" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" id="0x7f120234" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Bridge" id="0x7f120235" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" id="0x7f120236" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" id="0x7f120237" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" id="0x7f120238" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" id="0x7f120239" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DialogWhenLarge" id="0x7f12023a" />
    <public type="style" name="Theme.MaterialComponents.DayNight.NoActionBar" id="0x7f12023b" />
    <public type="style" name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" id="0x7f12023c" />
    <public type="style" name="Theme.MaterialComponents.Dialog" id="0x7f12023d" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert" id="0x7f12023e" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert.Bridge" id="0x7f12023f" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Bridge" id="0x7f120240" />
    <public type="style" name="Theme.MaterialComponents.Dialog.FixedSize" id="0x7f120241" />
    <public type="style" name="Theme.MaterialComponents.Dialog.FixedSize.Bridge" id="0x7f120242" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth" id="0x7f120243" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth.Bridge" id="0x7f120244" />
    <public type="style" name="Theme.MaterialComponents.DialogWhenLarge" id="0x7f120245" />
    <public type="style" name="Theme.MaterialComponents.Light" id="0x7f120246" />
    <public type="style" name="Theme.MaterialComponents.Light.BottomSheetDialog" id="0x7f120247" />
    <public type="style" name="Theme.MaterialComponents.Light.Bridge" id="0x7f120248" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar" id="0x7f120249" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f12024a" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog" id="0x7f12024b" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f12024c" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert.Bridge" id="0x7f12024d" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f12024e" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f12024f" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge" id="0x7f120250" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f120251" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge" id="0x7f120252" />
    <public type="style" name="Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f120253" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar" id="0x7f120254" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar.Bridge" id="0x7f120255" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar" id="0x7f120256" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar.Bridge" id="0x7f120257" />
    <public type="style" name="ThemeOverlay.AppCompat" id="0x7f120258" />
    <public type="style" name="ThemeOverlay.AppCompat.ActionBar" id="0x7f120259" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark" id="0x7f12025a" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f12025b" />
    <public type="style" name="ThemeOverlay.AppCompat.DayNight" id="0x7f12025c" />
    <public type="style" name="ThemeOverlay.AppCompat.DayNight.ActionBar" id="0x7f12025d" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog" id="0x7f12025e" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f12025f" />
    <public type="style" name="ThemeOverlay.AppCompat.Light" id="0x7f120260" />
    <public type="style" name="ThemeOverlay.Design.TextInputEditText" id="0x7f120261" />
    <public type="style" name="ThemeOverlay.Material3" id="0x7f120262" />
    <public type="style" name="ThemeOverlay.Material3.ActionBar" id="0x7f120263" />
    <public type="style" name="ThemeOverlay.Material3.AutoCompleteTextView" id="0x7f120264" />
    <public type="style" name="ThemeOverlay.Material3.AutoCompleteTextView.FilledBox" id="0x7f120265" />
    <public type="style" name="ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense" id="0x7f120266" />
    <public type="style" name="ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox" id="0x7f120267" />
    <public type="style" name="ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense" id="0x7f120268" />
    <public type="style" name="ThemeOverlay.Material3.BottomAppBar" id="0x7f120269" />
    <public type="style" name="ThemeOverlay.Material3.BottomAppBar.Legacy" id="0x7f12026a" />
    <public type="style" name="ThemeOverlay.Material3.BottomSheetDialog" id="0x7f12026b" />
    <public type="style" name="ThemeOverlay.Material3.Button" id="0x7f12026c" />
    <public type="style" name="ThemeOverlay.Material3.Button.ElevatedButton" id="0x7f12026d" />
    <public type="style" name="ThemeOverlay.Material3.Button.IconButton" id="0x7f12026e" />
    <public type="style" name="ThemeOverlay.Material3.Button.IconButton.Filled" id="0x7f12026f" />
    <public type="style" name="ThemeOverlay.Material3.Button.IconButton.Filled.Tonal" id="0x7f120270" />
    <public type="style" name="ThemeOverlay.Material3.Button.TextButton" id="0x7f120271" />
    <public type="style" name="ThemeOverlay.Material3.Button.TextButton.Snackbar" id="0x7f120272" />
    <public type="style" name="ThemeOverlay.Material3.Button.TonalButton" id="0x7f120273" />
    <public type="style" name="ThemeOverlay.Material3.Chip" id="0x7f120274" />
    <public type="style" name="ThemeOverlay.Material3.Chip.Assist" id="0x7f120275" />
    <public type="style" name="ThemeOverlay.Material3.Dark" id="0x7f120276" />
    <public type="style" name="ThemeOverlay.Material3.Dark.ActionBar" id="0x7f120277" />
    <public type="style" name="ThemeOverlay.Material3.DayNight.BottomSheetDialog" id="0x7f120278" />
    <public type="style" name="ThemeOverlay.Material3.Dialog" id="0x7f120279" />
    <public type="style" name="ThemeOverlay.Material3.Dialog.Alert" id="0x7f12027a" />
    <public type="style" name="ThemeOverlay.Material3.Dialog.Alert.Framework" id="0x7f12027b" />
    <public type="style" name="ThemeOverlay.Material3.DynamicColors.Dark" id="0x7f12027c" />
    <public type="style" name="ThemeOverlay.Material3.DynamicColors.DayNight" id="0x7f12027d" />
    <public type="style" name="ThemeOverlay.Material3.DynamicColors.Light" id="0x7f12027e" />
    <public type="style" name="ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary" id="0x7f12027f" />
    <public type="style" name="ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary" id="0x7f120280" />
    <public type="style" name="ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface" id="0x7f120281" />
    <public type="style" name="ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary" id="0x7f120282" />
    <public type="style" name="ThemeOverlay.Material3.FloatingActionButton.Primary" id="0x7f120283" />
    <public type="style" name="ThemeOverlay.Material3.FloatingActionButton.Secondary" id="0x7f120284" />
    <public type="style" name="ThemeOverlay.Material3.FloatingActionButton.Surface" id="0x7f120285" />
    <public type="style" name="ThemeOverlay.Material3.FloatingActionButton.Tertiary" id="0x7f120286" />
    <public type="style" name="ThemeOverlay.Material3.HarmonizedColors" id="0x7f120287" />
    <public type="style" name="ThemeOverlay.Material3.HarmonizedColors.Empty" id="0x7f120288" />
    <public type="style" name="ThemeOverlay.Material3.Light" id="0x7f120289" />
    <public type="style" name="ThemeOverlay.Material3.Light.Dialog.Alert.Framework" id="0x7f12028a" />
    <public type="style" name="ThemeOverlay.Material3.MaterialAlertDialog" id="0x7f12028b" />
    <public type="style" name="ThemeOverlay.Material3.MaterialAlertDialog.Centered" id="0x7f12028c" />
    <public type="style" name="ThemeOverlay.Material3.MaterialCalendar" id="0x7f12028d" />
    <public type="style" name="ThemeOverlay.Material3.MaterialCalendar.Fullscreen" id="0x7f12028e" />
    <public type="style" name="ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton" id="0x7f12028f" />
    <public type="style" name="ThemeOverlay.Material3.MaterialTimePicker" id="0x7f120290" />
    <public type="style" name="ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText" id="0x7f120291" />
    <public type="style" name="ThemeOverlay.Material3.NavigationView" id="0x7f120292" />
    <public type="style" name="ThemeOverlay.Material3.Snackbar" id="0x7f120293" />
    <public type="style" name="ThemeOverlay.Material3.TextInputEditText" id="0x7f120294" />
    <public type="style" name="ThemeOverlay.Material3.TextInputEditText.FilledBox" id="0x7f120295" />
    <public type="style" name="ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense" id="0x7f120296" />
    <public type="style" name="ThemeOverlay.Material3.TextInputEditText.OutlinedBox" id="0x7f120297" />
    <public type="style" name="ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense" id="0x7f120298" />
    <public type="style" name="ThemeOverlay.Material3.Toolbar.Surface" id="0x7f120299" />
    <public type="style" name="ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon" id="0x7f12029a" />
    <public type="style" name="ThemeOverlay.MaterialComponents" id="0x7f12029b" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar" id="0x7f12029c" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar.Primary" id="0x7f12029d" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar.Surface" id="0x7f12029e" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView" id="0x7f12029f" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox" id="0x7f1202a0" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" id="0x7f1202a1" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox" id="0x7f1202a2" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" id="0x7f1202a3" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomAppBar.Primary" id="0x7f1202a4" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomAppBar.Surface" id="0x7f1202a5" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomSheetDialog" id="0x7f1202a6" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark" id="0x7f1202a7" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark.ActionBar" id="0x7f1202a8" />
    <public type="style" name="ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog" id="0x7f1202a9" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog" id="0x7f1202aa" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f1202ab" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog.Alert.Framework" id="0x7f1202ac" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light" id="0x7f1202ad" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework" id="0x7f1202ae" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f1202af" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered" id="0x7f1202b0" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date" id="0x7f1202b1" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar" id="0x7f1202b2" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text" id="0x7f1202b3" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day" id="0x7f1202b4" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner" id="0x7f1202b5" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialCalendar" id="0x7f1202b6" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen" id="0x7f1202b7" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText" id="0x7f1202b8" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox" id="0x7f1202b9" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f1202ba" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f1202bb" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f1202bc" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TimePicker" id="0x7f1202bd" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TimePicker.Display" id="0x7f1202be" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText" id="0x7f1202bf" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary" id="0x7f1202c0" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Toolbar.Primary" id="0x7f1202c1" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Toolbar.Surface" id="0x7f1202c2" />
    <public type="style" name="Widget.AppCompat.ActionBar" id="0x7f1202c3" />
    <public type="style" name="Widget.AppCompat.ActionBar.Solid" id="0x7f1202c4" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabBar" id="0x7f1202c5" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabText" id="0x7f1202c6" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabView" id="0x7f1202c7" />
    <public type="style" name="Widget.AppCompat.ActionButton" id="0x7f1202c8" />
    <public type="style" name="Widget.AppCompat.ActionButton.CloseMode" id="0x7f1202c9" />
    <public type="style" name="Widget.AppCompat.ActionButton.Overflow" id="0x7f1202ca" />
    <public type="style" name="Widget.AppCompat.ActionMode" id="0x7f1202cb" />
    <public type="style" name="Widget.AppCompat.ActivityChooserView" id="0x7f1202cc" />
    <public type="style" name="Widget.AppCompat.AutoCompleteTextView" id="0x7f1202cd" />
    <public type="style" name="Widget.AppCompat.Button" id="0x7f1202ce" />
    <public type="style" name="Widget.AppCompat.Button.Borderless" id="0x7f1202cf" />
    <public type="style" name="Widget.AppCompat.Button.Borderless.Colored" id="0x7f1202d0" />
    <public type="style" name="Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f1202d1" />
    <public type="style" name="Widget.AppCompat.Button.Colored" id="0x7f1202d2" />
    <public type="style" name="Widget.AppCompat.Button.Small" id="0x7f1202d3" />
    <public type="style" name="Widget.AppCompat.ButtonBar" id="0x7f1202d4" />
    <public type="style" name="Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f1202d5" />
    <public type="style" name="Widget.AppCompat.CompoundButton.CheckBox" id="0x7f1202d6" />
    <public type="style" name="Widget.AppCompat.CompoundButton.RadioButton" id="0x7f1202d7" />
    <public type="style" name="Widget.AppCompat.CompoundButton.Switch" id="0x7f1202d8" />
    <public type="style" name="Widget.AppCompat.DrawerArrowToggle" id="0x7f1202d9" />
    <public type="style" name="Widget.AppCompat.DropDownItem.Spinner" id="0x7f1202da" />
    <public type="style" name="Widget.AppCompat.EditText" id="0x7f1202db" />
    <public type="style" name="Widget.AppCompat.ImageButton" id="0x7f1202dc" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar" id="0x7f1202dd" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid" id="0x7f1202de" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" id="0x7f1202df" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f1202e0" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" id="0x7f1202e1" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText" id="0x7f1202e2" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f1202e3" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView" id="0x7f1202e4" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" id="0x7f1202e5" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton" id="0x7f1202e6" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.CloseMode" id="0x7f1202e7" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.Overflow" id="0x7f1202e8" />
    <public type="style" name="Widget.AppCompat.Light.ActionMode.Inverse" id="0x7f1202e9" />
    <public type="style" name="Widget.AppCompat.Light.ActivityChooserView" id="0x7f1202ea" />
    <public type="style" name="Widget.AppCompat.Light.AutoCompleteTextView" id="0x7f1202eb" />
    <public type="style" name="Widget.AppCompat.Light.DropDownItem.Spinner" id="0x7f1202ec" />
    <public type="style" name="Widget.AppCompat.Light.ListPopupWindow" id="0x7f1202ed" />
    <public type="style" name="Widget.AppCompat.Light.ListView.DropDown" id="0x7f1202ee" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu" id="0x7f1202ef" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f1202f0" />
    <public type="style" name="Widget.AppCompat.Light.SearchView" id="0x7f1202f1" />
    <public type="style" name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" id="0x7f1202f2" />
    <public type="style" name="Widget.AppCompat.ListMenuView" id="0x7f1202f3" />
    <public type="style" name="Widget.AppCompat.ListPopupWindow" id="0x7f1202f4" />
    <public type="style" name="Widget.AppCompat.ListView" id="0x7f1202f5" />
    <public type="style" name="Widget.AppCompat.ListView.DropDown" id="0x7f1202f6" />
    <public type="style" name="Widget.AppCompat.ListView.Menu" id="0x7f1202f7" />
    <public type="style" name="Widget.AppCompat.PopupMenu" id="0x7f1202f8" />
    <public type="style" name="Widget.AppCompat.PopupMenu.Overflow" id="0x7f1202f9" />
    <public type="style" name="Widget.AppCompat.PopupWindow" id="0x7f1202fa" />
    <public type="style" name="Widget.AppCompat.ProgressBar" id="0x7f1202fb" />
    <public type="style" name="Widget.AppCompat.ProgressBar.Horizontal" id="0x7f1202fc" />
    <public type="style" name="Widget.AppCompat.RatingBar" id="0x7f1202fd" />
    <public type="style" name="Widget.AppCompat.RatingBar.Indicator" id="0x7f1202fe" />
    <public type="style" name="Widget.AppCompat.RatingBar.Small" id="0x7f1202ff" />
    <public type="style" name="Widget.AppCompat.SearchView" id="0x7f120300" />
    <public type="style" name="Widget.AppCompat.SearchView.ActionBar" id="0x7f120301" />
    <public type="style" name="Widget.AppCompat.SeekBar" id="0x7f120302" />
    <public type="style" name="Widget.AppCompat.SeekBar.Discrete" id="0x7f120303" />
    <public type="style" name="Widget.AppCompat.Spinner" id="0x7f120304" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown" id="0x7f120305" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown.ActionBar" id="0x7f120306" />
    <public type="style" name="Widget.AppCompat.Spinner.Underlined" id="0x7f120307" />
    <public type="style" name="Widget.AppCompat.TextView" id="0x7f120308" />
    <public type="style" name="Widget.AppCompat.TextView.SpinnerItem" id="0x7f120309" />
    <public type="style" name="Widget.AppCompat.Toolbar" id="0x7f12030a" />
    <public type="style" name="Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f12030b" />
    <public type="style" name="Widget.Compat.NotificationActionContainer" id="0x7f12030c" />
    <public type="style" name="Widget.Compat.NotificationActionText" id="0x7f12030d" />
    <public type="style" name="Widget.Design.AppBarLayout" id="0x7f12030e" />
    <public type="style" name="Widget.Design.BottomNavigationView" id="0x7f12030f" />
    <public type="style" name="Widget.Design.BottomSheet.Modal" id="0x7f120310" />
    <public type="style" name="Widget.Design.CollapsingToolbar" id="0x7f120311" />
    <public type="style" name="Widget.Design.FloatingActionButton" id="0x7f120312" />
    <public type="style" name="Widget.Design.NavigationView" id="0x7f120313" />
    <public type="style" name="Widget.Design.ScrimInsetsFrameLayout" id="0x7f120314" />
    <public type="style" name="Widget.Design.Snackbar" id="0x7f120315" />
    <public type="style" name="Widget.Design.TabLayout" id="0x7f120316" />
    <public type="style" name="Widget.Design.TextInputEditText" id="0x7f120317" />
    <public type="style" name="Widget.Design.TextInputLayout" id="0x7f120318" />
    <public type="style" name="Widget.Material3.ActionBar.Solid" id="0x7f120319" />
    <public type="style" name="Widget.Material3.ActionMode" id="0x7f12031a" />
    <public type="style" name="Widget.Material3.AppBarLayout" id="0x7f12031b" />
    <public type="style" name="Widget.Material3.AutoCompleteTextView.FilledBox" id="0x7f12031c" />
    <public type="style" name="Widget.Material3.AutoCompleteTextView.FilledBox.Dense" id="0x7f12031d" />
    <public type="style" name="Widget.Material3.AutoCompleteTextView.OutlinedBox" id="0x7f12031e" />
    <public type="style" name="Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense" id="0x7f12031f" />
    <public type="style" name="Widget.Material3.Badge" id="0x7f120320" />
    <public type="style" name="Widget.Material3.BottomAppBar" id="0x7f120321" />
    <public type="style" name="Widget.Material3.BottomAppBar.Button.Navigation" id="0x7f120322" />
    <public type="style" name="Widget.Material3.BottomAppBar.Legacy" id="0x7f120323" />
    <public type="style" name="Widget.Material3.BottomNavigationView" id="0x7f120324" />
    <public type="style" name="Widget.Material3.BottomNavigationView.ActiveIndicator" id="0x7f120325" />
    <public type="style" name="Widget.Material3.BottomSheet" id="0x7f120326" />
    <public type="style" name="Widget.Material3.BottomSheet.DragHandle" id="0x7f120327" />
    <public type="style" name="Widget.Material3.BottomSheet.Modal" id="0x7f120328" />
    <public type="style" name="Widget.Material3.Button" id="0x7f120329" />
    <public type="style" name="Widget.Material3.Button.ElevatedButton" id="0x7f12032a" />
    <public type="style" name="Widget.Material3.Button.ElevatedButton.Icon" id="0x7f12032b" />
    <public type="style" name="Widget.Material3.Button.Icon" id="0x7f12032c" />
    <public type="style" name="Widget.Material3.Button.IconButton" id="0x7f12032d" />
    <public type="style" name="Widget.Material3.Button.IconButton.Filled" id="0x7f12032e" />
    <public type="style" name="Widget.Material3.Button.IconButton.Filled.Tonal" id="0x7f12032f" />
    <public type="style" name="Widget.Material3.Button.IconButton.Outlined" id="0x7f120330" />
    <public type="style" name="Widget.Material3.Button.OutlinedButton" id="0x7f120331" />
    <public type="style" name="Widget.Material3.Button.OutlinedButton.Icon" id="0x7f120332" />
    <public type="style" name="Widget.Material3.Button.TextButton" id="0x7f120333" />
    <public type="style" name="Widget.Material3.Button.TextButton.Dialog" id="0x7f120334" />
    <public type="style" name="Widget.Material3.Button.TextButton.Dialog.Flush" id="0x7f120335" />
    <public type="style" name="Widget.Material3.Button.TextButton.Dialog.Icon" id="0x7f120336" />
    <public type="style" name="Widget.Material3.Button.TextButton.Icon" id="0x7f120337" />
    <public type="style" name="Widget.Material3.Button.TextButton.Snackbar" id="0x7f120338" />
    <public type="style" name="Widget.Material3.Button.TonalButton" id="0x7f120339" />
    <public type="style" name="Widget.Material3.Button.TonalButton.Icon" id="0x7f12033a" />
    <public type="style" name="Widget.Material3.Button.UnelevatedButton" id="0x7f12033b" />
    <public type="style" name="Widget.Material3.CardView.Elevated" id="0x7f12033c" />
    <public type="style" name="Widget.Material3.CardView.Filled" id="0x7f12033d" />
    <public type="style" name="Widget.Material3.CardView.Outlined" id="0x7f12033e" />
    <public type="style" name="Widget.Material3.CheckedTextView" id="0x7f12033f" />
    <public type="style" name="Widget.Material3.Chip.Assist" id="0x7f120340" />
    <public type="style" name="Widget.Material3.Chip.Assist.Elevated" id="0x7f120341" />
    <public type="style" name="Widget.Material3.Chip.Filter" id="0x7f120342" />
    <public type="style" name="Widget.Material3.Chip.Filter.Elevated" id="0x7f120343" />
    <public type="style" name="Widget.Material3.Chip.Input" id="0x7f120344" />
    <public type="style" name="Widget.Material3.Chip.Input.Elevated" id="0x7f120345" />
    <public type="style" name="Widget.Material3.Chip.Input.Icon" id="0x7f120346" />
    <public type="style" name="Widget.Material3.Chip.Input.Icon.Elevated" id="0x7f120347" />
    <public type="style" name="Widget.Material3.Chip.Suggestion" id="0x7f120348" />
    <public type="style" name="Widget.Material3.Chip.Suggestion.Elevated" id="0x7f120349" />
    <public type="style" name="Widget.Material3.ChipGroup" id="0x7f12034a" />
    <public type="style" name="Widget.Material3.CircularProgressIndicator" id="0x7f12034b" />
    <public type="style" name="Widget.Material3.CircularProgressIndicator.ExtraSmall" id="0x7f12034c" />
    <public type="style" name="Widget.Material3.CircularProgressIndicator.Medium" id="0x7f12034d" />
    <public type="style" name="Widget.Material3.CircularProgressIndicator.Small" id="0x7f12034e" />
    <public type="style" name="Widget.Material3.CollapsingToolbar" id="0x7f12034f" />
    <public type="style" name="Widget.Material3.CollapsingToolbar.Large" id="0x7f120350" />
    <public type="style" name="Widget.Material3.CollapsingToolbar.Medium" id="0x7f120351" />
    <public type="style" name="Widget.Material3.CompoundButton.CheckBox" id="0x7f120352" />
    <public type="style" name="Widget.Material3.CompoundButton.MaterialSwitch" id="0x7f120353" />
    <public type="style" name="Widget.Material3.CompoundButton.RadioButton" id="0x7f120354" />
    <public type="style" name="Widget.Material3.CompoundButton.Switch" id="0x7f120355" />
    <public type="style" name="Widget.Material3.DrawerLayout" id="0x7f120356" />
    <public type="style" name="Widget.Material3.ExtendedFloatingActionButton.Icon.Primary" id="0x7f120357" />
    <public type="style" name="Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary" id="0x7f120358" />
    <public type="style" name="Widget.Material3.ExtendedFloatingActionButton.Icon.Surface" id="0x7f120359" />
    <public type="style" name="Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary" id="0x7f12035a" />
    <public type="style" name="Widget.Material3.ExtendedFloatingActionButton.Primary" id="0x7f12035b" />
    <public type="style" name="Widget.Material3.ExtendedFloatingActionButton.Secondary" id="0x7f12035c" />
    <public type="style" name="Widget.Material3.ExtendedFloatingActionButton.Surface" id="0x7f12035d" />
    <public type="style" name="Widget.Material3.ExtendedFloatingActionButton.Tertiary" id="0x7f12035e" />
    <public type="style" name="Widget.Material3.FloatingActionButton.Large.Primary" id="0x7f12035f" />
    <public type="style" name="Widget.Material3.FloatingActionButton.Large.Secondary" id="0x7f120360" />
    <public type="style" name="Widget.Material3.FloatingActionButton.Large.Surface" id="0x7f120361" />
    <public type="style" name="Widget.Material3.FloatingActionButton.Large.Tertiary" id="0x7f120362" />
    <public type="style" name="Widget.Material3.FloatingActionButton.Primary" id="0x7f120363" />
    <public type="style" name="Widget.Material3.FloatingActionButton.Secondary" id="0x7f120364" />
    <public type="style" name="Widget.Material3.FloatingActionButton.Small.Primary" id="0x7f120365" />
    <public type="style" name="Widget.Material3.FloatingActionButton.Small.Secondary" id="0x7f120366" />
    <public type="style" name="Widget.Material3.FloatingActionButton.Small.Surface" id="0x7f120367" />
    <public type="style" name="Widget.Material3.FloatingActionButton.Small.Tertiary" id="0x7f120368" />
    <public type="style" name="Widget.Material3.FloatingActionButton.Surface" id="0x7f120369" />
    <public type="style" name="Widget.Material3.FloatingActionButton.Tertiary" id="0x7f12036a" />
    <public type="style" name="Widget.Material3.Light.ActionBar.Solid" id="0x7f12036b" />
    <public type="style" name="Widget.Material3.LinearProgressIndicator" id="0x7f12036c" />
    <public type="style" name="Widget.Material3.MaterialCalendar" id="0x7f12036d" />
    <public type="style" name="Widget.Material3.MaterialCalendar.Day" id="0x7f12036e" />
    <public type="style" name="Widget.Material3.MaterialCalendar.Day.Invalid" id="0x7f12036f" />
    <public type="style" name="Widget.Material3.MaterialCalendar.Day.Selected" id="0x7f120370" />
    <public type="style" name="Widget.Material3.MaterialCalendar.Day.Today" id="0x7f120371" />
    <public type="style" name="Widget.Material3.MaterialCalendar.DayOfWeekLabel" id="0x7f120372" />
    <public type="style" name="Widget.Material3.MaterialCalendar.DayTextView" id="0x7f120373" />
    <public type="style" name="Widget.Material3.MaterialCalendar.Fullscreen" id="0x7f120374" />
    <public type="style" name="Widget.Material3.MaterialCalendar.HeaderCancelButton" id="0x7f120375" />
    <public type="style" name="Widget.Material3.MaterialCalendar.HeaderDivider" id="0x7f120376" />
    <public type="style" name="Widget.Material3.MaterialCalendar.HeaderLayout" id="0x7f120377" />
    <public type="style" name="Widget.Material3.MaterialCalendar.HeaderSelection" id="0x7f120378" />
    <public type="style" name="Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen" id="0x7f120379" />
    <public type="style" name="Widget.Material3.MaterialCalendar.HeaderTitle" id="0x7f12037a" />
    <public type="style" name="Widget.Material3.MaterialCalendar.HeaderToggleButton" id="0x7f12037b" />
    <public type="style" name="Widget.Material3.MaterialCalendar.Item" id="0x7f12037c" />
    <public type="style" name="Widget.Material3.MaterialCalendar.MonthNavigationButton" id="0x7f12037d" />
    <public type="style" name="Widget.Material3.MaterialCalendar.MonthTextView" id="0x7f12037e" />
    <public type="style" name="Widget.Material3.MaterialCalendar.Year" id="0x7f12037f" />
    <public type="style" name="Widget.Material3.MaterialCalendar.Year.Selected" id="0x7f120380" />
    <public type="style" name="Widget.Material3.MaterialCalendar.Year.Today" id="0x7f120381" />
    <public type="style" name="Widget.Material3.MaterialCalendar.YearNavigationButton" id="0x7f120382" />
    <public type="style" name="Widget.Material3.MaterialDivider" id="0x7f120383" />
    <public type="style" name="Widget.Material3.MaterialDivider.Heavy" id="0x7f120384" />
    <public type="style" name="Widget.Material3.MaterialTimePicker" id="0x7f120385" />
    <public type="style" name="Widget.Material3.MaterialTimePicker.Button" id="0x7f120386" />
    <public type="style" name="Widget.Material3.MaterialTimePicker.Clock" id="0x7f120387" />
    <public type="style" name="Widget.Material3.MaterialTimePicker.Display" id="0x7f120388" />
    <public type="style" name="Widget.Material3.MaterialTimePicker.Display.Divider" id="0x7f120389" />
    <public type="style" name="Widget.Material3.MaterialTimePicker.Display.HelperText" id="0x7f12038a" />
    <public type="style" name="Widget.Material3.MaterialTimePicker.Display.TextInputEditText" id="0x7f12038b" />
    <public type="style" name="Widget.Material3.MaterialTimePicker.Display.TextInputLayout" id="0x7f12038c" />
    <public type="style" name="Widget.Material3.MaterialTimePicker.ImageButton" id="0x7f12038d" />
    <public type="style" name="Widget.Material3.NavigationRailView" id="0x7f12038e" />
    <public type="style" name="Widget.Material3.NavigationRailView.ActiveIndicator" id="0x7f12038f" />
    <public type="style" name="Widget.Material3.NavigationView" id="0x7f120390" />
    <public type="style" name="Widget.Material3.PopupMenu" id="0x7f120391" />
    <public type="style" name="Widget.Material3.PopupMenu.ContextMenu" id="0x7f120392" />
    <public type="style" name="Widget.Material3.PopupMenu.ListPopupWindow" id="0x7f120393" />
    <public type="style" name="Widget.Material3.PopupMenu.Overflow" id="0x7f120394" />
    <public type="style" name="Widget.Material3.Slider" id="0x7f120395" />
    <public type="style" name="Widget.Material3.Snackbar" id="0x7f120396" />
    <public type="style" name="Widget.Material3.Snackbar.FullWidth" id="0x7f120397" />
    <public type="style" name="Widget.Material3.Snackbar.TextView" id="0x7f120398" />
    <public type="style" name="Widget.Material3.TabLayout" id="0x7f120399" />
    <public type="style" name="Widget.Material3.TabLayout.OnSurface" id="0x7f12039a" />
    <public type="style" name="Widget.Material3.TabLayout.Secondary" id="0x7f12039b" />
    <public type="style" name="Widget.Material3.TextInputEditText.FilledBox" id="0x7f12039c" />
    <public type="style" name="Widget.Material3.TextInputEditText.FilledBox.Dense" id="0x7f12039d" />
    <public type="style" name="Widget.Material3.TextInputEditText.OutlinedBox" id="0x7f12039e" />
    <public type="style" name="Widget.Material3.TextInputEditText.OutlinedBox.Dense" id="0x7f12039f" />
    <public type="style" name="Widget.Material3.TextInputLayout.FilledBox" id="0x7f1203a0" />
    <public type="style" name="Widget.Material3.TextInputLayout.FilledBox.Dense" id="0x7f1203a1" />
    <public type="style" name="Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu" id="0x7f1203a2" />
    <public type="style" name="Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu" id="0x7f1203a3" />
    <public type="style" name="Widget.Material3.TextInputLayout.OutlinedBox" id="0x7f1203a4" />
    <public type="style" name="Widget.Material3.TextInputLayout.OutlinedBox.Dense" id="0x7f1203a5" />
    <public type="style" name="Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu" id="0x7f1203a6" />
    <public type="style" name="Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu" id="0x7f1203a7" />
    <public type="style" name="Widget.Material3.Toolbar" id="0x7f1203a8" />
    <public type="style" name="Widget.Material3.Toolbar.OnSurface" id="0x7f1203a9" />
    <public type="style" name="Widget.Material3.Toolbar.Surface" id="0x7f1203aa" />
    <public type="style" name="Widget.Material3.Tooltip" id="0x7f1203ab" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Primary" id="0x7f1203ac" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.PrimarySurface" id="0x7f1203ad" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Solid" id="0x7f1203ae" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Surface" id="0x7f1203af" />
    <public type="style" name="Widget.MaterialComponents.ActionMode" id="0x7f1203b0" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.Primary" id="0x7f1203b1" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.PrimarySurface" id="0x7f1203b2" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.Surface" id="0x7f1203b3" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox" id="0x7f1203b4" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" id="0x7f1203b5" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox" id="0x7f1203b6" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" id="0x7f1203b7" />
    <public type="style" name="Widget.MaterialComponents.Badge" id="0x7f1203b8" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar" id="0x7f1203b9" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar.Colored" id="0x7f1203ba" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar.PrimarySurface" id="0x7f1203bb" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView" id="0x7f1203bc" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView.Colored" id="0x7f1203bd" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView.PrimarySurface" id="0x7f1203be" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet" id="0x7f1203bf" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet.Modal" id="0x7f1203c0" />
    <public type="style" name="Widget.MaterialComponents.Button" id="0x7f1203c1" />
    <public type="style" name="Widget.MaterialComponents.Button.Icon" id="0x7f1203c2" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton" id="0x7f1203c3" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton.Icon" id="0x7f1203c4" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton" id="0x7f1203c5" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog" id="0x7f1203c6" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog.Flush" id="0x7f1203c7" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon" id="0x7f1203c8" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Icon" id="0x7f1203c9" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Snackbar" id="0x7f1203ca" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton" id="0x7f1203cb" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton.Icon" id="0x7f1203cc" />
    <public type="style" name="Widget.MaterialComponents.CardView" id="0x7f1203cd" />
    <public type="style" name="Widget.MaterialComponents.CheckedTextView" id="0x7f1203ce" />
    <public type="style" name="Widget.MaterialComponents.Chip.Action" id="0x7f1203cf" />
    <public type="style" name="Widget.MaterialComponents.Chip.Choice" id="0x7f1203d0" />
    <public type="style" name="Widget.MaterialComponents.Chip.Entry" id="0x7f1203d1" />
    <public type="style" name="Widget.MaterialComponents.Chip.Filter" id="0x7f1203d2" />
    <public type="style" name="Widget.MaterialComponents.ChipGroup" id="0x7f1203d3" />
    <public type="style" name="Widget.MaterialComponents.CircularProgressIndicator" id="0x7f1203d4" />
    <public type="style" name="Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall" id="0x7f1203d5" />
    <public type="style" name="Widget.MaterialComponents.CircularProgressIndicator.Medium" id="0x7f1203d6" />
    <public type="style" name="Widget.MaterialComponents.CircularProgressIndicator.Small" id="0x7f1203d7" />
    <public type="style" name="Widget.MaterialComponents.CollapsingToolbar" id="0x7f1203d8" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.CheckBox" id="0x7f1203d9" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.RadioButton" id="0x7f1203da" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.Switch" id="0x7f1203db" />
    <public type="style" name="Widget.MaterialComponents.ExtendedFloatingActionButton" id="0x7f1203dc" />
    <public type="style" name="Widget.MaterialComponents.ExtendedFloatingActionButton.Icon" id="0x7f1203dd" />
    <public type="style" name="Widget.MaterialComponents.FloatingActionButton" id="0x7f1203de" />
    <public type="style" name="Widget.MaterialComponents.Light.ActionBar.Solid" id="0x7f1203df" />
    <public type="style" name="Widget.MaterialComponents.LinearProgressIndicator" id="0x7f1203e0" />
    <public type="style" name="Widget.MaterialComponents.MaterialButtonToggleGroup" id="0x7f1203e1" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar" id="0x7f1203e2" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day" id="0x7f1203e3" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Invalid" id="0x7f1203e4" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Selected" id="0x7f1203e5" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Today" id="0x7f1203e6" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel" id="0x7f1203e7" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.DayTextView" id="0x7f1203e8" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Fullscreen" id="0x7f1203e9" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton" id="0x7f1203ea" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton" id="0x7f1203eb" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderDivider" id="0x7f1203ec" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout" id="0x7f1203ed" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection" id="0x7f1203ee" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen" id="0x7f1203ef" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderTitle" id="0x7f1203f0" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" id="0x7f1203f1" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Item" id="0x7f1203f2" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton" id="0x7f1203f3" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.MonthTextView" id="0x7f1203f4" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year" id="0x7f1203f5" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year.Selected" id="0x7f1203f6" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year.Today" id="0x7f1203f7" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.YearNavigationButton" id="0x7f1203f8" />
    <public type="style" name="Widget.MaterialComponents.MaterialDivider" id="0x7f1203f9" />
    <public type="style" name="Widget.MaterialComponents.NavigationRailView" id="0x7f1203fa" />
    <public type="style" name="Widget.MaterialComponents.NavigationRailView.Colored" id="0x7f1203fb" />
    <public type="style" name="Widget.MaterialComponents.NavigationRailView.Colored.Compact" id="0x7f1203fc" />
    <public type="style" name="Widget.MaterialComponents.NavigationRailView.Compact" id="0x7f1203fd" />
    <public type="style" name="Widget.MaterialComponents.NavigationRailView.PrimarySurface" id="0x7f1203fe" />
    <public type="style" name="Widget.MaterialComponents.NavigationView" id="0x7f1203ff" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu" id="0x7f120400" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.ContextMenu" id="0x7f120401" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.ListPopupWindow" id="0x7f120402" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.Overflow" id="0x7f120403" />
    <public type="style" name="Widget.MaterialComponents.ProgressIndicator" id="0x7f120404" />
    <public type="style" name="Widget.MaterialComponents.ShapeableImageView" id="0x7f120405" />
    <public type="style" name="Widget.MaterialComponents.Slider" id="0x7f120406" />
    <public type="style" name="Widget.MaterialComponents.Snackbar" id="0x7f120407" />
    <public type="style" name="Widget.MaterialComponents.Snackbar.FullWidth" id="0x7f120408" />
    <public type="style" name="Widget.MaterialComponents.Snackbar.TextView" id="0x7f120409" />
    <public type="style" name="Widget.MaterialComponents.TabLayout" id="0x7f12040a" />
    <public type="style" name="Widget.MaterialComponents.TabLayout.Colored" id="0x7f12040b" />
    <public type="style" name="Widget.MaterialComponents.TabLayout.PrimarySurface" id="0x7f12040c" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox" id="0x7f12040d" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f12040e" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f12040f" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f120410" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox" id="0x7f120411" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense" id="0x7f120412" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu" id="0x7f120413" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu" id="0x7f120414" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" id="0x7f120415" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense" id="0x7f120416" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu" id="0x7f120417" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu" id="0x7f120418" />
    <public type="style" name="Widget.MaterialComponents.TextView" id="0x7f120419" />
    <public type="style" name="Widget.MaterialComponents.TimePicker" id="0x7f12041a" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Button" id="0x7f12041b" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Clock" id="0x7f12041c" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Display" id="0x7f12041d" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Display.Divider" id="0x7f12041e" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Display.HelperText" id="0x7f12041f" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Display.TextInputEditText" id="0x7f120420" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.Display.TextInputLayout" id="0x7f120421" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.ImageButton" id="0x7f120422" />
    <public type="style" name="Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance" id="0x7f120423" />
    <public type="style" name="Widget.MaterialComponents.Toolbar" id="0x7f120424" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.Primary" id="0x7f120425" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.PrimarySurface" id="0x7f120426" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.Surface" id="0x7f120427" />
    <public type="style" name="Widget.MaterialComponents.Tooltip" id="0x7f120428" />
    <public type="style" name="Widget.MaterialProgressBar.ProgressBar" id="0x7f120429" />
    <public type="style" name="Widget.MaterialProgressBar.ProgressBar.Horizontal" id="0x7f12042a" />
    <public type="style" name="Widget.MaterialProgressBar.ProgressBar.Horizontal.NoPadding" id="0x7f12042b" />
    <public type="style" name="Widget.MaterialProgressBar.ProgressBar.Large" id="0x7f12042c" />
    <public type="style" name="Widget.MaterialProgressBar.ProgressBar.Large.NoPadding" id="0x7f12042d" />
    <public type="style" name="Widget.MaterialProgressBar.ProgressBar.NoPadding" id="0x7f12042e" />
    <public type="style" name="Widget.MaterialProgressBar.ProgressBar.Small" id="0x7f12042f" />
    <public type="style" name="Widget.MaterialProgressBar.ProgressBar.Small.NoPadding" id="0x7f120430" />
    <public type="style" name="Widget.Support.CoordinatorLayout" id="0x7f120431" />
    <public type="xml" name="accessibility_service_config" id="0x7f140000" />
    <public type="xml" name="preference" id="0x7f140001" />
    <public type="xml" name="provider_paths" id="0x7f140002" />
</resources>

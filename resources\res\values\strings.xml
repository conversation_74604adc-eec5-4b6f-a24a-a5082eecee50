<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with %s</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="androidx_startup">androidx.startup</string>
    <string name="app_name">inrt</string>
    <string name="appbar_scrolling_view_behavior">com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior</string>
    <string name="bottom_sheet_behavior">com.google.android.material.bottomsheet.BottomSheetBehavior</string>
    <string name="bottomsheet_action_collapse">Collapse the bottom sheet</string>
    <string name="bottomsheet_action_expand">Expand the bottom sheet</string>
    <string name="bottomsheet_action_expand_halfway">Expand halfway</string>
    <string name="bottomsheet_drag_handle_clicked">Drag handle double-tapped</string>
    <string name="bottomsheet_drag_handle_content_description">Drag handle</string>
    <string name="cancel">Cancel</string>
    <string name="character_counter_content_description">Characters entered %1$d of %2$d</string>
    <string name="character_counter_overflowed_content_description">Character limit exceeded %1$d of %2$d</string>
    <string name="character_counter_pattern">%1$d/%2$d</string>
    <string name="clear_text_end_icon_content_description">Clear text</string>
    <string name="define_roundedimageview" />
    <string name="details_cannot_handle_intent">Cannot find task to handle intent. \n%s</string>
    <string name="error_a11y_label">Error: invalid</string>
    <string name="error_activity_not_found_for_apk_installing">Cannot find installer</string>
    <string name="error_cannot_execute_timed_task_file_not_exists">Cannot execute task, the file does not exists: %s</string>
    <string name="error_cannot_start_activity">Error: Cannot start activity</string>
    <string name="error_clip_too_large">Text too large，truncated to 500KB</string>
    <string name="error_icon_content_description">Error</string>
    <string name="error_read_log">Read logs error: %s</string>
    <string name="error_screen_capture_need_foreground">On Android 11+，it needs to start foreground service notification to request screen capture. Please enable it in the drawer or use settings module in your code.</string>
    <string name="error_unsuppored_plugin_connection">remote aidl connection is not supported for no-installation plugin</string>
    <string name="exception_notification_service_disabled">The notification service is not running, please re-enable the notification permission</string>
    <string name="exposed_dropdown_menu_content_description">Show dropdown menu</string>
    <string name="fab_transformation_scrim_behavior">com.google.android.material.transformation.FabTransformationScrimBehavior</string>
    <string name="fab_transformation_sheet_behavior">com.google.android.material.transformation.FabTransformationSheetBehavior</string>
    <string name="foreground_notification_channel_name">Foreground Notification</string>
    <string name="foreground_notification_text">The notification is to keep running and request screen capture, you can disable it on drawer.</string>
    <string name="foreground_notification_title">%s is running</string>
    <string name="hide_bottom_view_on_scroll_behavior">com.google.android.material.behavior.HideBottomViewOnScrollBehavior</string>
    <string name="icon_content_description">Dialog Icon</string>
    <string name="item_view_role_description">Tab</string>
    <string name="jdeferred">jdeferred</string>
    <string name="key_dont_show_main_activity">key_dont_show_main_activity</string>
    <string name="key_enable_accessibility_service_by_root">key_enable_accessibility_service_by_root</string>
    <string name="key_foreground_service">key_foreground_service</string>
    <string name="key_print_java_stack_trace">key_print_java_stack_trace</string>
    <string name="key_stable_mode">key_stable_mode</string>
    <string name="key_use_volume_control_running">key_use_volume_control_running</string>
    <string name="library_roundedimageview_author">Vince Mi</string>
    <string name="library_roundedimageview_authorWebsite">https://github.com/vinc3m1</string>
    <string name="library_roundedimageview_isOpenSource">true</string>
    <string name="library_roundedimageview_libraryDescription">A fast ImageView (and Drawable) that supports rounded corners (and ovals or circles) based on the original example from Romain Guy.</string>
    <string name="library_roundedimageview_libraryName">RoundedImageView</string>
    <string name="library_roundedimageview_libraryVersion">1.3.0</string>
    <string name="library_roundedimageview_libraryWebsite">https://github.com/vinc3m1/RoundedImageView</string>
    <string name="library_roundedimageview_licenseId">apache_2_0</string>
    <string name="library_roundedimageview_repositoryLink">https://github.com/vinc3m1/RoundedImageView.git</string>
    <string name="log_accessibility_service_connected" formatted="false">Accessibility service connected(%s): stable mode: %s</string>
    <string name="log_accessibility_service_created">Accessibility service created(%s)</string>
    <string name="log_accessibility_service_destroyed">Accessibility service destroyed(%s)</string>
    <string name="m3_sys_motion_easing_emphasized">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string>
    <string name="m3_sys_motion_easing_emphasized_accelerate">cubic-bezier(0.3, 0.0, 0.8, 0.2)</string>
    <string name="m3_sys_motion_easing_emphasized_decelerate">cubic-bezier(0.1, 0.7, 0.1, 1.0)</string>
    <string name="m3_sys_motion_easing_emphasized_path_data">M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1</string>
    <string name="m3_sys_motion_easing_legacy">cubic-bezier(0.4, 0.0, 0.2, 1.0)</string>
    <string name="m3_sys_motion_easing_legacy_accelerate">cubic-bezier(0.4, 0.0, 1.0, 1.0)</string>
    <string name="m3_sys_motion_easing_legacy_decelerate">cubic-bezier(0.0, 0.0, 0.2, 1.0)</string>
    <string name="m3_sys_motion_easing_linear">cubic-bezier(0.0, 0.0, 1.0, 1.0)</string>
    <string name="m3_sys_motion_easing_standard">cubic-bezier(0.2, 0.0, 0.0, 1.0)</string>
    <string name="m3_sys_motion_easing_standard_accelerate">cubic-bezier(0.3, 0.0, 1.0, 1.0)</string>
    <string name="m3_sys_motion_easing_standard_decelerate">cubic-bezier(0.0, 0.0, 0.0, 1.0)</string>
    <string name="material_clock_display_divider">:</string>
    <string name="material_clock_toggle_content_description">Select AM or PM</string>
    <string name="material_hour_selection">Select hour</string>
    <string name="material_hour_suffix">%1$s o\'clock</string>
    <string name="material_minute_selection">Select minutes</string>
    <string name="material_minute_suffix">%1$s minutes</string>
    <string name="material_motion_easing_accelerated">cubic-bezier(0.4, 0.0, 1.0, 1.0)</string>
    <string name="material_motion_easing_decelerated">cubic-bezier(0.0, 0.0, 0.2, 1.0)</string>
    <string name="material_motion_easing_emphasized">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string>
    <string name="material_motion_easing_linear">cubic-bezier(0.0, 0.0, 1.0, 1.0)</string>
    <string name="material_motion_easing_standard">cubic-bezier(0.4, 0.0, 0.2, 1.0)</string>
    <string name="material_slider_range_end">Range end,</string>
    <string name="material_slider_range_start">Range start,</string>
    <string name="material_timepicker_am">AM</string>
    <string name="material_timepicker_clock_mode_description">Switch to clock mode for the time input.</string>
    <string name="material_timepicker_hour">Hour</string>
    <string name="material_timepicker_minute">Minute</string>
    <string name="material_timepicker_pm">PM</string>
    <string name="material_timepicker_select_time">Select time</string>
    <string name="material_timepicker_text_input_mode_description">Switch to text input mode for the time input.</string>
    <string name="msg_blocking_selector_in_ui_thread">Cannot perform blocking operations in the ui thread, please execute findOne() or untilFind() in the sub thread or sub script</string>
    <string name="msg_gesture_on_main_thread">Cannot perform gesture on ui thread, use gesturesAsync(), clickAsync() instead</string>
    <string name="msg_settings_volume_up">Modify settings of \'Volume up key to stop the script\'(stop_all_on_volume_up) is only available in released apk</string>
    <string name="mtrl_badge_numberless_content_description">New notification</string>
    <string name="mtrl_checkbox_button_icon_path_checked">M14,18.2 11.4,15.6 10,17 14,21 22,13 20.6,11.6z</string>
    <string name="mtrl_checkbox_button_icon_path_group_name">icon</string>
    <string name="mtrl_checkbox_button_icon_path_indeterminate">M13.4,15 11,15 11,17 13.4,17 21,17 21,15z</string>
    <string name="mtrl_checkbox_button_icon_path_name">icon path</string>
    <string name="mtrl_checkbox_button_path_checked">M23,7H9C7.9,7,7,7.9,7,9v14c0,1.1,0.9,2,2,2h14c1.1,0,2-0.9,2-2V9C25,7.9,24.1,7,23,7z</string>
    <string name="mtrl_checkbox_button_path_group_name">button</string>
    <string name="mtrl_checkbox_button_path_name">button path</string>
    <string name="mtrl_checkbox_button_path_unchecked">M23,7H9C7.9,7,7,7.9,7,9v14c0,1.1,0.9,2,2,2h14c1.1,0,2-0.9,2-2V9C25,7.9,24.1,7,23,7z M23,23H9V9h14V23z</string>
    <string name="mtrl_checkbox_state_description_checked">Checked</string>
    <string name="mtrl_checkbox_state_description_indeterminate">Partially checked</string>
    <string name="mtrl_checkbox_state_description_unchecked">Not checked</string>
    <string name="mtrl_chip_close_icon_content_description">Remove %1$s</string>
    <string name="mtrl_exceed_max_badge_number_content_description">More than %1$d new notifications</string>
    <string name="mtrl_exceed_max_badge_number_suffix">%1$d%2$s</string>
    <string name="mtrl_picker_a11y_next_month">Change to next month</string>
    <string name="mtrl_picker_a11y_prev_month">Change to previous month</string>
    <string name="mtrl_picker_announce_current_selection">Current selection: %1$s</string>
    <string name="mtrl_picker_cancel">Cancel</string>
    <string name="mtrl_picker_confirm">OK</string>
    <string name="mtrl_picker_date_header_selected">%1$s</string>
    <string name="mtrl_picker_date_header_title">Select Date</string>
    <string name="mtrl_picker_date_header_unselected">Selected date</string>
    <string name="mtrl_picker_day_of_week_column_header">Column of days: %1$s</string>
    <string name="mtrl_picker_invalid_format">Invalid format.</string>
    <string name="mtrl_picker_invalid_format_example">Example: %1$s</string>
    <string name="mtrl_picker_invalid_format_use">Use: %1$s</string>
    <string name="mtrl_picker_invalid_range">Invalid range.</string>
    <string name="mtrl_picker_navigate_to_year_description">Navigate to year %1$s</string>
    <string name="mtrl_picker_out_of_range">Out of range: %1$s</string>
    <string name="mtrl_picker_range_header_only_end_selected">Start date – %1$s</string>
    <string name="mtrl_picker_range_header_only_start_selected">%1$s – End date</string>
    <string name="mtrl_picker_range_header_selected">%1$s – %2$s</string>
    <string name="mtrl_picker_range_header_title">Select Range</string>
    <string name="mtrl_picker_range_header_unselected">Start date – End date</string>
    <string name="mtrl_picker_save">Save</string>
    <string name="mtrl_picker_text_input_date_hint">Date</string>
    <string name="mtrl_picker_text_input_date_range_end_hint">End date</string>
    <string name="mtrl_picker_text_input_date_range_start_hint">Start date</string>
    <string name="mtrl_picker_text_input_day_abbr">d</string>
    <string name="mtrl_picker_text_input_month_abbr">m</string>
    <string name="mtrl_picker_text_input_year_abbr">y</string>
    <string name="mtrl_picker_toggle_to_calendar_input_mode">Switch to calendar input mode</string>
    <string name="mtrl_picker_toggle_to_day_selection">Tap to switch to selecting a day</string>
    <string name="mtrl_picker_toggle_to_text_input_mode">Switch to text input mode</string>
    <string name="mtrl_picker_toggle_to_year_selection">Tap to switch to selecting a year</string>
    <string name="mtrl_switch_thumb_group_name">circle_group</string>
    <string name="mtrl_switch_thumb_path_checked">M4,16 A12,12 0 0,1 16,4 H16 A12,12 0 0,1 16,28 H16 A12,12 0 0,1 4,16</string>
    <string name="mtrl_switch_thumb_path_morphing">M0,16 A11,11 0 0,1 11,5 H21 A11,11 0 0,1 21,27 H11 A11,11 0 0,1 0,16</string>
    <string name="mtrl_switch_thumb_path_name">circle</string>
    <string name="mtrl_switch_thumb_path_pressed">M2,16 A14,14 0 0,1 16,2 H16 A14,14 0 0,1 16,30 H16 A14,14 0 0,1 2,16</string>
    <string name="mtrl_switch_thumb_path_unchecked">M8,16 A8,8 0 0,1 16,8 H16 A8,8 0 0,1 16,24 H16 A8,8 0 0,1 8,16</string>
    <string name="mtrl_switch_track_decoration_path">M1,16 A15,15 0 0,1 16,1 H36 A15,15 0 0,1 36,31 H16 A15,15 0 0,1 1,16</string>
    <string name="mtrl_switch_track_path">M0,16 A16,16 0 0,1 16,0 H36 A16,16 0 0,1 36,32 H16 A16,16 0 0,1 0,16</string>
    <string name="mtrl_timepicker_cancel">Cancel</string>
    <string name="mtrl_timepicker_confirm">OK</string>
    <string name="no_read_phone_state_permissin">No read phone state permission</string>
    <string name="no_read_phone_state_permissin_imei">No read phone state permission (Android 10 and above are no longer allowed to read IMEI)</string>
    <string name="no_usage_statics_permission">No usage statics permission</string>
    <string name="no_write_settings_permissin">No writing settings permission</string>
    <string name="ok">OK</string>
    <string name="password_toggle_content_description">Show password</string>
    <string name="path_password_eye">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string>
    <string name="path_password_eye_mask_strike_through">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_eye_mask_visible">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_strike_through">M3.27,4.27 L19.74,20.74</string>
    <string name="powered_by_autojs">Powered by Auto.js Pro</string>
    <string name="script_notification_channel_name">Notification from scripts</string>
    <string name="search_menu_title">Search</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="summary_dont_show_main_activity">Run code without UI when launched</string>
    <string name="summary_enable_accessibility_service_by_root">Use root privilege to enable accessibility service automatically</string>
    <string name="summary_pref_foreground_service">To keep the app alive in background</string>
    <string name="summary_stable_mode">Less details for UiObject of automator</string>
    <string name="text_accessibility_service">Accessibility</string>
    <string name="text_accessibility_service_description">Auto.js Pro</string>
    <string name="text_already_copy_to_clip">Copied</string>
    <string name="text_already_stop_n_scripts">Stop %d script(s)</string>
    <string name="text_auto_operate_service_enabled_but_not_running">Accessibility service is enabled but not running, this may be an Android bug, you may need to restart your phone or restart the accessibility service</string>
    <string name="text_cannot_handle_intent">Cannot handle intent</string>
    <string name="text_choose_script_to_handle_intent">Choose script to handle the intent/file</string>
    <string name="text_clear_log_file">Clear log file</string>
    <string name="text_console">Console</string>
    <string name="text_copy_crash_info">Copy crash info</string>
    <string name="text_crash_report">Crash Report</string>
    <string name="text_dont_show_main_activity">DO NOT show the main page</string>
    <string name="text_drawer_close">Close drawer</string>
    <string name="text_drawer_open">Open drawer</string>
    <string name="text_enable_accessibility_service_by_root">Enable accessibility service by root</string>
    <string name="text_enable_accessibility_service_by_root_timeout">start the accessibility service by root timeout</string>
    <string name="text_error">Error:</string>
    <string name="text_execution_finished" formatted="false">\n------------\n[%s]Execution finished, %f sec.</string>
    <string name="text_exit">Exit</string>
    <string name="text_file_not_exists">File does not exist</string>
    <string name="text_floating_window_permission">Floating window permission</string>
    <string name="text_foreground_service">Foreground service</string>
    <string name="text_ignore_power_battery_optimizations">Ignoring power battery optimizations</string>
    <string name="text_log">Log</string>
    <string name="text_log_level">Log level</string>
    <string name="text_no_accessibility_permission">Accessibility service not started</string>
    <string name="text_no_file_rw_permission">No file reading&amp;writing permission</string>
    <string name="text_no_floating_window_permission">No drawing overlay permission</string>
    <string name="text_open_by_other_apps">Open by other apps</string>
    <string name="text_open_with_scripts">Open with tasks</string>
    <string name="text_others">Ohters</string>
    <string name="text_path_is_empty">Path is empty</string>
    <string name="text_permissions">Permissions</string>
    <string name="text_please_choose">Please choose</string>
    <string name="text_requires_sdk_version_to_run_the_script">Required Android OS version:</string>
    <string name="text_script_running">Running Config</string>
    <string name="text_search">Search</string>
    <string name="text_select_image">Select image</string>
    <string name="text_settings">Settings</string>
    <string name="text_should_enable_key_observing">Key observing is disabled, please enable in settings</string>
    <string name="text_stable_mode">Stable mode</string>
    <string name="text_start_running">Script Running</string>
    <string name="text_use_volume_to_stop_running">Volume up to stop scripts</string>
    <string name="tips_background_launch_app">Android 10 and higher place restrictions on when apps can start activities when the app is running it the background，you can try to grant the app \'Start activities on background\' permission (if has).</string>
    <string name="tray__authority">legacyTrayAuthority</string>
    <string name="warn_memory_leak">Memory leak is detected, please check if your code recycle memory correctly. See https://g.pro.autojs.org/docs/#/debug 。\n%s</string>
</resources>

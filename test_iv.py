#!/usr/bin/env python3
import hashlib

BUILD_ID = 'D0899DD7-100'
APP_NAME = 'PPMT助手'
iv_string = f'{BUILD_ID}{APP_NAME}'
print(f'IV String: {repr(iv_string)}')
print(f'IV String bytes: {iv_string.encode("utf-8")}')
iv_hash = hashlib.md5(iv_string.encode('utf-8')).hexdigest()
print(f'IV Hash: {iv_hash}')
print(f'IV 16: {iv_hash[:16]}')

# 测试Java中的字符串
java_iv_string = BUILD_ID + APP_NAME
print(f'Java IV String: {repr(java_iv_string)}')
java_iv_hash = hashlib.md5(java_iv_string.encode('utf-8')).hexdigest()
print(f'Java IV Hash: {java_iv_hash}')
print(f'Java IV 16: {java_iv_hash[:16]}')

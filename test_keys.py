#!/usr/bin/env python3
import hashlib
from Crypto.Cipher import AES

# 项目配置参数
PACKAGE_NAME = 'com.ppmtzs.scr'
VERSION_NAME = '1.00'
MAIN_SCRIPT_FILE = 'main.js'
VERSION_CODE = 100
BUILD_ID = 'D0899DD7-100'
APP_NAME = 'PPMT助手'
FIXED_PLAINTEXT = b'9a1132118990c3db'

# 生成基础密钥
key_string = f'{PACKAGE_NAME}{VERSION_NAME}{MAIN_SCRIPT_FILE}{VERSION_CODE}'
key_hash = hashlib.md5(key_string.encode('utf-8')).hexdigest()
base_key = key_hash.encode('utf-8')

# 生成初始向量
iv_string = f'{BUILD_ID}{APP_NAME}'
iv_hash = hashlib.md5(iv_string.encode('utf-8')).hexdigest()
iv_16 = iv_hash[:16]

print(f'Key String: {key_string}')
print(f'Key Hash: {key_hash}')
print(f'Base Key Length: {len(base_key)}')
print(f'IV String: {iv_string}')
print(f'IV Hash: {iv_hash}')
print(f'IV 16: {iv_16}')
print(f'IV 16 Length: {len(iv_16)}')

# 尝试生成最终密钥
try:
    cipher = AES.new(base_key, AES.MODE_CBC, iv_16.encode('utf-8'))
    final_key = cipher.encrypt(FIXED_PLAINTEXT)
    print(f'Final Key: {final_key.hex()}')
except Exception as e:
    print(f'Error: {e}')
    print(f'Base key length: {len(base_key)}')
    print(f'IV length: {len(iv_16.encode("utf-8"))}')

# PPMT助手 - 悬浮窗点击执行无障碍脚本完整流程分析

## 🎯 核心发现

通过深度分析源码，我已经完全理清了从点击悬浮窗到执行无障碍脚本的完整调用链路！

## 📋 完整执行流程

### 1. 悬浮窗点击事件处理
**文件**: `FloatingControllerView.java`
**关键代码**:
```java
// 悬浮窗的运行/停止按钮点击处理
((ImageView) _$_findCachedViewById(C3502f.runOrStop)).setOnClickListener(new View.OnClickListener() {
    @Override
    public void onClick(View view) {
        FloatingControllerView floatingControllerView = this.f9675f;
        floatingControllerView.runOrStop(); // 核心调用
    }
});
```

### 2. runOrStop方法执行
**文件**: `FloatingControllerView.java` 第326行
```java
public final void runOrStop() {
    ScriptExecution scriptExecution;
    EnumC4022c value = this.f1723e.f9663h.getValue();
    EnumC4022c enumC4022c = EnumC4022c.RUNNING;
    
    if (value != enumC4022c) {
        // 如果脚本未运行，启动脚本
        this.f1723e.m5171b(); // 调用Controller的启动方法
        return;
    }
    
    // 如果脚本正在运行，停止脚本
    C4021b c4021b = this.f1723e;
    if (c4021b.f9663h.getValue() == enumC4022c && (scriptExecution = c4021b.f9664i) != null) {
        // 停止当前执行的脚本
        C2623b.m2998H(c4021b, null, 0, new C4020a(scriptExecution, null), 3);
    }
}
```

### 3. 脚本启动逻辑
**文件**: `C4021b.java` (Controller类) 第64行
```java
public final void m5171b() {
    EnumC4022c value = this.f9663h.getValue();
    EnumC4022c enumC4022c = EnumC4022c.RUNNING;
    
    if (value == enumC4022c) {
        return; // 已经在运行，直接返回
    }
    
    // 移除之前的监听器
    ScriptExecution scriptExecution = this.f9664i;
    if (scriptExecution != null) {
        scriptExecution.removeScriptExecutionListener(this);
    }
    
    // 创建新的脚本执行任务
    ScriptExecution scriptExecutionM4089a = C3508l.f6648j.m4089a(
        new ScriptExecutionTask(this.f9660e, null, this.f9661f)
    );
    this.f9664i = scriptExecutionM4089a;
    
    // 添加执行监听器并启动
    if (scriptExecutionM4089a.addScriptExecutionListener(this)) {
        interfaceC3379k = this.f9663h;
    } else {
        interfaceC3379k = this.f9663h;
        enumC4022c = EnumC4022c.STOPPED;
    }
    interfaceC3379k.setValue(enumC4022c);
}
```

### 4. 脚本执行任务创建
**文件**: `ScriptExecutionTask.java`
```java
public ScriptExecutionTask(ScriptSource scriptSource, ScriptExecutionListener scriptExecutionListener, ExecutionConfig executionConfig) {
    this.source = scriptSource;      // 脚本源码 (JavaScriptFileSource)
    this.config = executionConfig;   // 执行配置
    this.listener = scriptExecutionListener; // 执行监听器
}
```

### 5. 脚本引擎执行
**文件**: `RunnableScriptExecution.java`
```java
public Object execute() {
    try {
        // 创建脚本引擎
        ScriptEngine<?> scriptEngineCreateEngineOfSourceOrThrow = 
            this.mScriptEngineManager.createEngineOfSourceOrThrow(getSource(), getId());
        this.mScriptEngine = scriptEngineCreateEngineOfSourceOrThrow;
        
        // 设置执行配置
        scriptEngineCreateEngineOfSourceOrThrow.setTag(ExecutionConfig.TAG, getConfig());
        notifyEngineReady(this.mScriptEngine);
        
        // 执行脚本
        return execute(this.mScriptEngine);
    } catch (Throwable th) {
        th.printStackTrace();
        notifyException(th);
        return null;
    }
}

public Object doExecution(ScriptEngine scriptEngine) {
    scriptEngine.setTag(ScriptEngine.TAG_SOURCE, getSource());
    notifyStart();
    
    // 获取执行配置
    long delay = getConfig().getDelay();
    int loopTimes = getConfig().getLoopTimes();
    if (loopTimes == 0) {
        loopTimes = Integer.MAX_VALUE; // 无限循环
    }
    long interval = getConfig().getInterval();
    
    sleep(delay); // 延迟启动
    
    ScriptSource source = getSource();
    Object objExecute = null;
    
    // 循环执行脚本
    for (int i7 = 0; i7 < loopTimes; i7++) {
        objExecute = execute(scriptEngine, source);
        sleep(interval); // 执行间隔
    }
    return objExecute;
}
```

### 6. 🔑 关键：加密脚本解密执行
**文件**: `C0805c.java` (继承自RhinoJavaScriptEngine)
```java
@Override
public Object doExecution(JavaScriptSource javaScriptSource) throws IOException {
    if (!(javaScriptSource instanceof JavaScriptFileSource)) {
        return super.doExecution(javaScriptSource);
    }
    
    JavaScriptFileSource javaScriptFileSource = (JavaScriptFileSource) javaScriptSource;
    
    // 读取加密文件
    byte[] bytes = PFiles.readBytes(javaScriptFileSource.f1781h.getPath());
    
    // 检查文件头，判断是否为加密文件
    if (bytes.length < 6) {
        return super.doExecution(javaScriptSource); // 非加密文件
    }
    
    // 验证加密标识
    for (int i7 = 0; i7 < 5; i7++) {
        if (bytes[i7] != C1727a.f2345a[i7]) {
            return super.doExecution(javaScriptSource); // 非加密文件
        }
    }
    
    // 检查加密类型 (第6字节)
    byte encryptionType = bytes[5];
    if (encryptionType != 18 && encryptionType != 19) {
        return super.doExecution(javaScriptSource); // 不支持的加密类型
    }
    
    try {
        // 🔥 核心：解密脚本内容
        byte[] decryptedData = ScriptEncryption.Companion.decrypt$default(
            ScriptEncryption.Companion, bytes, 8, 0, 4, null
        );
        
        if (encryptionType == 18) {
            // 类型18：解密后是纯文本JavaScript
            String fileName = javaScriptFileSource.f1781h.getName();
            return super.execute(new StringScriptSource(fileName, 
                new String(decryptedData, StandardCharsets.UTF_8)));
        } else if (encryptionType == 19) {
            // 类型19：解密后是序列化的Rhino Script对象
            C0804b objectInputStream = new C0804b(new ByteArrayInputStream(decryptedData));
            try {
                Object object = objectInputStream.readObject();
                Script script = (Script) object; // Rhino编译后的脚本对象
                
                // 🎯 直接执行编译后的脚本！
                return script.exec(getContext(), getScriptable());
            } finally {
                objectInputStream.close();
            }
        }
    } catch (GeneralSecurityException e7) {
        e7.printStackTrace();
        return super.doExecution(javaScriptSource); // 解密失败，尝试普通执行
    }
}
```

### 7. 自定义ObjectInputStream处理
**文件**: `C0804b.java`
```java
public final class C0804b extends ObjectInputStream {
    @Override
    public final ObjectStreamClass readClassDescriptor() throws ClassNotFoundException, IOException {
        ObjectStreamClass classDescriptor = super.readClassDescriptor();
        try {
            ObjectStreamClass objectStreamClassLookup = ObjectStreamClass.lookup(
                Class.forName(classDescriptor.getName())
            );
            if (objectStreamClassLookup != null) {
                // 处理序列化版本不匹配问题
                return classDescriptor.getSerialVersionUID() != objectStreamClassLookup.getSerialVersionUID() 
                    ? objectStreamClassLookup : classDescriptor;
            }
            return classDescriptor;
        } catch (ClassNotFoundException unused) {
            return classDescriptor;
        }
    }
}
```

## 🔍 关键技术点

### 1. 脚本加密识别
- **文件头**: 前5字节为固定标识 `C1727a.f2345a`
- **加密类型**: 第6字节标识加密类型
  - `18`: 文本加密 (解密后为JavaScript源码)
  - `19`: 对象加密 (解密后为Rhino Script对象)

### 2. 解密算法
- 使用 `ScriptEncryption` 类进行AES解密
- 跳过前8字节文件头，从第9字节开始解密

### 3. 执行方式
- **类型18**: 解密→文本→编译→执行
- **类型19**: 解密→反序列化→直接执行 (PPMT使用此方式)

### 4. 无障碍服务集成
```java
@Override
public Object execute(JavaScriptSource javaScriptSource) {
    if ((javaScriptSource.m1202b() & 2) != 0) {
        // 如果脚本需要无障碍权限，确保服务已启用
        getRuntime().ensureAccessibilityServiceEnabled();
    }
    return doExecution(javaScriptSource);
}
```

## 🎯 PPMT助手执行流程总结

1. **用户点击悬浮窗** → `FloatingControllerView.runOrStop()`
2. **检查运行状态** → 如果未运行则启动，如果运行中则停止
3. **创建执行任务** → `ScriptExecutionTask` 包含脚本源和配置
4. **启动脚本引擎** → `RunnableScriptExecution.execute()`
5. **检测加密文件** → 验证文件头和加密类型
6. **解密脚本内容** → AES解密获得Rhino Script对象
7. **反序列化对象** → 使用自定义ObjectInputStream
8. **执行脚本对象** → `script.exec(context, scriptable)`
9. **启用无障碍服务** → 确保有权限执行自动化操作
10. **循环执行** → 根据配置的间隔和次数重复执行

这就是PPMT助手从点击悬浮窗到执行加密JavaScript脚本进行无障碍自动化操作的完整技术流程！

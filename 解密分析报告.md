# PPMT助手 脚本解密分析报告

## 概述
成功解密了PPMT助手Android应用中的6个加密JavaScript脚本文件。这些脚本使用了两阶段加密：AES/CBC/PKCS7Padding + XOR，并且脚本被编译为Mozilla Rhino的InterpretedFunction对象。

## 解密技术细节

### 加密算法
1. **密钥生成**: 使用项目配置参数（packageName, versionName, versionCode）的MD5哈希
2. **IV生成**: 使用构建配置参数（buildId, appName）
3. **第一阶段**: AES-256/CBC/PKCS7Padding解密
4. **第二阶段**: XOR解密，使用AES加密固定字符串生成的密钥流

### 关键参数
- Base Key Hash: `db612244ba74f7d003f02225e8f04fc5`
- IV String: `36bc74f67cfbc476`
- Final Key: `72c72882b256821010aa840aa502ea04508d0f8978f62fa257cce81d63b366d0`

## 脚本分析结果

### 1. main.js (43,825 字节)
**功能**: 主要业务逻辑脚本
**特征**: 
- 使用jsjiami.com.v6混淆器进行代码混淆
- 包含混淆变量名：`_0xodW`, `_0xb144`, `_0x2049`等
- 包含UI相关功能：`ui`, `view`, `layout`
- 包含定时器功能：`setInterval`, `dytime`
- 包含网络相关：`urlck`

### 2. init.js (16,984 字节)
**功能**: 悬浮按钮初始化和配置
**主要组件**:
- FloatButton主模块
- 配置对象mConfig，包含：
  - 颜色配置：`#00000000` (透明), `#FFFFFF` (白色)
  - 状态管理：`isInit`, `isShow`, `isMenuOpen`, `isOrientation`
  - 动画配置：`menuRadius`, `logoAlpha`, `padding`
- 事件处理：`item_click`, `direction_changed`, `menu_state_changed`, `orientation_changed`
- 模块依赖：`__util__`, `CreateRoundButtonView`, `FloatButtonAnim`

### 3. __util__.js (6,225 字节)
**功能**: Android系统API工具函数
**主要功能**:
- Android图形API：`android.graphics.drawable.GradientDrawable`
- 屏幕适配：`dp2px`, `px2dp`, `isHorizontalScreen`
- 状态栏高度获取：`iStatusBarHeight`, `status_bar_height`
- 系统资源访问：`getResources`, `getDisplayMetrics`, `getConfiguration`
- 形状创建：`CreateShape`
- 颜色评估器：`ColorEvaluator`

### 4. FloatButtonAnim.js (13,045 字节)
**功能**: 悬浮按钮动画处理
**动画组件**:
- Android动画API：`ValueAnimator`, `ObjectAnimator`, `AnimatorSet`
- 插值器：`BounceInterpolator`
- 动画属性：`aTx`, `aTy`, `aTz` (位移), `aSx`, `aSy` (缩放), `aAl` (透明度), `aPt` (旋转)
- 动画管理：`mAnimList`, `createAnim`, `show`, `hide`, `direction`, `menu`, `stateChanged`

### 5. CreateRoundButtonView.js (8,160 字节)
**功能**: 圆形按钮视图创建
**依赖**: RoundButton组件

### 6. RoundButton.js (3,420 字节)
**功能**: 圆形按钮组件实现
**依赖**: __util__工具函数（dp2px, CreateShape）

## 应用架构分析

### 模块结构
```
main.js (主业务逻辑)
├── init.js (悬浮按钮初始化)
    ├── __util__.js (Android API工具)
    ├── CreateRoundButtonView.js (视图创建)
    │   └── RoundButton.js (按钮组件)
    └── FloatButtonAnim.js (动画处理)
        └── __util__.js (工具依赖)
```

### 技术栈
- **JavaScript引擎**: Mozilla Rhino
- **UI框架**: Android原生View系统
- **动画系统**: Android Animation API
- **代码混淆**: jsjiami.com.v6

## 功能推测

基于提取的字符串和模块结构，PPMT助手的主要功能包括：

1. **悬浮窗口系统**: 创建可拖拽的悬浮按钮
2. **菜单系统**: 支持展开/收缩的圆形菜单
3. **动画效果**: 丰富的按钮和菜单动画
4. **屏幕适配**: 支持横竖屏切换和不同分辨率
5. **定时任务**: 可能包含自动化脚本执行
6. **网络功能**: 可能包含数据获取或提交功能

## 安全性评估

1. **加密强度**: 使用AES-256加密，安全性较高
2. **混淆程度**: 主脚本使用专业混淆器，增加了逆向难度
3. **模块化设计**: 代码结构清晰，便于维护和更新

## 结论

成功完成了PPMT助手加密脚本的解密工作，提取了所有关键字符串和模块结构信息。该应用是一个功能完整的Android自动化工具，具有悬浮窗口、动画效果和定时任务等功能。解密过程展示了现代JavaScript加密和混淆技术的复杂性。

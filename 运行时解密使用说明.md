# AutoJS脚本运行时解密使用说明

## 概述

通过运行时Hook的方式获取AutoJS加密脚本的解密后源码，这是最有效的方法，因为可以直接在脚本执行时获取到解密后的内容。

## 方法一：使用Frida Hook（推荐）

### 准备工作

1. **安装Frida**
   ```bash
   pip install frida-tools
   ```

2. **在手机上安装frida-server**
   - 下载对应架构的frida-server
   - 推送到手机并给予执行权限
   ```bash
   adb push frida-server /data/local/tmp/
   adb shell chmod 755 /data/local/tmp/frida-server
   adb shell /data/local/tmp/frida-server &
   ```

### 使用步骤

1. **启动frida-server**
   ```bash
   adb shell /data/local/tmp/frida-server &
   ```

2. **运行Hook脚本**
   ```bash
   frida -U -f com.ppmtzs.scr -l autojs_runtime_hook.js --no-pause
   ```

3. **操作应用**
   - 在手机上操作PPMT助手应用
   - 点击悬浮窗按钮执行脚本
   - 触发各种脚本功能

4. **查看结果**
   - 解密后的脚本会自动保存到 `/sdcard/` 目录
   - 文件名格式：`decrypted_script_[时间戳].js`、`runtime_script_[时间戳].js` 等

### Hook的关键点

我们的Frida脚本会Hook以下关键方法：

1. **ScriptEncryption.decrypt()** - 脚本解密方法
2. **Context.compileString()** - Rhino编译JavaScript源码
3. **Script.exec()** - 脚本执行
4. **InterpretedFunction.call()** - 函数调用
5. **ScriptSource.getScript()** - 获取脚本源码
6. **EncryptedScriptFileSource.getScript()** - 获取加密脚本解密后内容

## 方法二：使用Xposed模块

### 准备工作

1. **手机需要Root并安装Xposed框架**
   - LSPosed（推荐，支持Android 8.0+）
   - EdXposed
   - 原版Xposed（仅支持Android 8.0以下）

2. **编译Xposed模块**
   - 使用Android Studio创建新项目
   - 将 `AutoJSScriptDumper.java` 添加到项目中
   - 配置Xposed模块相关文件

### 使用步骤

1. **安装编译好的Xposed模块APK**
2. **在Xposed管理器中激活模块**
3. **重启手机**
4. **运行PPMT助手应用并执行脚本**
5. **查看 `/sdcard/autojs_dumps/` 目录下的解密文件**

## 方法三：使用调试器附加

### 使用Android Studio调试器

1. **启用应用调试**
   ```bash
   adb shell am set-debug-app -w com.ppmtzs.scr
   ```

2. **附加调试器**
   - 在Android Studio中选择"Attach Debugger to Android Process"
   - 选择PPMT助手进程

3. **设置断点**
   - 在关键的解密和脚本执行方法上设置断点
   - 查看运行时变量值

## 预期结果

成功Hook后，你应该能获得以下类型的文件：

### 1. 解密后的主脚本
```javascript
// main.js 解密后内容
function startScript() {
    console.log("开始执行脚本");
    // 具体的业务逻辑
}
```

### 2. 悬浮窗相关脚本
```javascript
// FloatButton相关脚本
function createFloatButton() {
    // 创建悬浮按钮的逻辑
}
```

### 3. 工具函数脚本
```javascript
// __util__.js 工具函数
function sleep(ms) {
    // 延时函数实现
}
```

## 故障排除

### 1. Frida连接失败
```bash
# 检查frida-server是否运行
adb shell ps | grep frida

# 检查端口是否被占用
adb shell netstat -tlnp | grep 27042

# 重启frida-server
adb shell killall frida-server
adb shell /data/local/tmp/frida-server &
```

### 2. Hook失败
- 检查类名和方法名是否正确
- 确认目标应用是否使用了相同的AutoJS版本
- 查看Frida控制台的错误信息

### 3. 权限问题
```bash
# 给予存储权限
adb shell pm grant com.ppmtzs.scr android.permission.WRITE_EXTERNAL_STORAGE
adb shell pm grant com.ppmtzs.scr android.permission.READ_EXTERNAL_STORAGE
```

### 4. 文件保存失败
- 检查 `/sdcard/` 目录是否可写
- 确认应用有存储权限
- 尝试使用应用私有目录：`/data/data/com.ppmtzs.scr/files/`

## 高级技巧

### 1. 实时监控脚本执行
```javascript
// 在Frida脚本中添加实时日志
console.log("[+] 脚本执行路径: " + Java.use("java.lang.Thread").currentThread().getStackTrace());
```

### 2. 过滤特定脚本
```javascript
// 只保存包含特定关键字的脚本
if (scriptContent.includes("抢购") || scriptContent.includes("刷新")) {
    saveToFile(filename, scriptContent);
}
```

### 3. 自动分析脚本功能
```javascript
// 分析脚本中的关键功能
function analyzeScript(content) {
    var features = [];
    if (content.includes("click")) features.push("点击操作");
    if (content.includes("swipe")) features.push("滑动操作");
    if (content.includes("findOne")) features.push("元素查找");
    return features;
}
```

## 注意事项

1. **合法使用**：仅用于学习和研究目的，不要用于非法活动
2. **版本兼容性**：不同版本的AutoJS可能有不同的类名和方法名
3. **性能影响**：Hook会对应用性能产生一定影响
4. **隐私保护**：注意保护获取到的脚本内容，不要泄露他人隐私

## 总结

运行时Hook是获取AutoJS加密脚本最有效的方法，因为：

1. **绕过所有加密**：直接在解密后获取源码
2. **完整性保证**：获取的是实际执行的代码
3. **实时性**：可以看到脚本的实际执行流程
4. **通用性**：适用于各种加密方式

通过这种方法，你可以完全了解PPMT助手的脚本实现原理，满足学习和研究的需求。
